<script setup>
import { computed, nextTick, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { useVbenModal, VbenIcon, VbenSpinner } from '@vben/common-ui';
import { cloneDeep } from '@vben/utils';

import {
  Button,
  Card,
  Divider,
  InputSearch,
  message,
  Modal,
  Pagination,
  Popconfirm,
  Select,
  Switch,
  Table,
  Textarea,
} from 'ant-design-vue';
import { debounce } from 'lodash-es';

import {
  editObjApi,
  getObjectDetailApi,
  getTableHeaderColumnApi,
  getTableItemApi,
  getTableLookUpListApi,
} from '#/api';
import { useLoading } from '#/hook';

import FieldModel from '../AiField/FieldModel.vue';
// 对象新建数据弹窗
const [FieldModal, FieldModelApi] = useVbenModal({
  connectedComponent: FieldModel,
});
const { isLoading, startLoading, stopLoading } = useLoading();

const route = useRoute();

// 数据表结构
const fullData = ref({
  ai_summary_prompt: null, // AI总结提示词
  ai_suggested_prompt: null, // AI建议提示词
  self_summary_time_range: null, // AI总结自身对象时间范围
  self_suggested_time_range: null, // AI建议提示词时间范围
  ai_summary_content: [], // AI总结提示词选择的字段
  ai_suggested_content: [], // AI建议提示词选择的字段
});

const AILoading = ref(false);
const dataSourceFinal = ref([]);
const AiTotal = ref(0);
const TotalAi = ref(0);
const dataColumns = [
  {
    title: '对象名称',
    dataIndex: 'object_name',
    key: 'object_name',
    align: 'center',
    width: '39%',
  },
  {
    title: '字段',
    dataIndex: 'field',
    key: 'field',
    align: 'center',
    width: '20%',
  },
  {
    title: '时间范围',
    dataIndex: 'time',
    key: 'time',
    align: 'center',
    width: '28%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
    width: '15%',
  },
];

// 时间范围下拉选项
const timeOptions = ref([
  {
    value: '近七天',
    label: '近七天',
  },
  {
    value: '近一个月',
    label: '近一个月',
  },
  {
    value: '近一个季度',
    label: '近一个季度',
  },
  {
    value: '近一年',
    label: '近一年',
  },
  {
    value: '全部',
    label: '全部',
  },
]);

const dataParamsfinal = reactive({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
  order_by: 'button_orderby',
  sort_by: 'ASC',
});
const dataParamsAi = reactive({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
  order_by: 'button_orderby',
  sort_by: 'ASC',
});
// 获取按钮列表
async function getAiList() {
  AILoading.value = true;
  dataSourceFinal.value = dataSourceFinal.value.map((item) => {
    return {
      ...item,
      object_name: item.object_name,
    };
  });
  AiTotal.value = dataSourceFinal.value.length;
  AILoading.value = false;
}

const showEdit = ref(false);

// 编辑按钮
const onEdit = () => {
  showEdit.value = true;
};
// 生成唯一随机标识
const generateUniqueKey = () => {
  const timestamp = Date.now().toString(36); // 时间戳转为36进制，节省长度
  const randomStr = Math.random().toString(36).slice(2, 8); // 随机字母数字组合
  const randomNum = Math.floor(10 ** 4 + Math.random() * 90_000); // 5位随机数字
  return `${timestamp}-${randomStr}-${randomNum}`;
};

const formState = ref({});
const objectName = ref('object');
// 获取对象详情
async function getObjectDetail() {
  startLoading();
  const { data } = await getObjectDetailApi(route.params.id || '');

  const { data: dataall } = await getTableItemApi(
    objectName.value,
    route.params.id,
  );
  if (dataall?.Object_ai_agent) {
    const showData = cloneDeep(dataall.Object_ai_agent);
    fullData.value = JSON.parse(showData);
    if (
      fullData.value?.ai_suggested_content &&
      fullData.value?.ai_suggested_content.length > 0
    ) {
      fullData.value.ai_suggested_content =
        fullData.value.ai_suggested_content.map((item) => {
          return {
            ...item,
            keyId: generateUniqueKey(),
          };
        });
    }
    if (
      fullData.value?.ai_summary_content &&
      fullData.value?.ai_summary_content.length > 0
    ) {
      fullData.value.ai_summary_content = fullData.value.ai_summary_content.map(
        (item) => {
          return {
            ...item,
            keyId: generateUniqueKey(),
          };
        },
      );
    }
  }
  formState.value = data;
  stopLoading();
}
function containsChinese(str) {
  const pattern = /[\u4E00-\u9FA5]/; // Unicode 范围：中文字符的起始编码到结束编码
  return pattern.test(str);
}

// 保存按钮
const onSave = async () => {
  startLoading();
  // 过滤 object_id 为空的条目
  const filterByObjectId = (arr) =>
    arr.filter((item) => item.object_id && item.object_id.trim() !== '');
  // 对两个数组分别过滤
  fullData.value.ai_suggested_content = filterByObjectId(
    fullData.value.ai_suggested_content,
  );
  fullData.value.ai_summary_content = filterByObjectId(
    fullData.value.ai_summary_content,
  );

  const toData = cloneDeep(formState.value);
  for (const key in toData) {
    if (toData[key] !== null && typeof toData[key] === 'object') {
      toData[key] = containsChinese(toData[key].name)
        ? toData[key].id
        : toData[key].name;
    }
  }
  TotalAi.value = fullData.value.ai_suggested_content.length;
  AiTotal.value = fullData.value.ai_summary_content.length;
  const allData = cloneDeep(fullData.value);
  // 移除 keyId 属性
  allData.ai_summary_content = allData.ai_summary_content.map((item) => {
    const { keyId: _keyId, ...rest } = item;
    return rest;
  });
  allData.ai_suggested_content = allData.ai_suggested_content.map((item) => {
    const { keyId: _keyId, ...rest } = item;
    return rest;
  });
  toData.Object_ai_agent = JSON.stringify(allData);
  stopLoading();
  await editObjApi(toData);
  message.success('保存成功');
  showEdit.value = false;
};

// 取消按钮
const onCancel = () => {
  showEdit.value = false;
  getObjectDetail();
};

// 添加对象按钮
const onAdd = (type) => {
  if (type === 'Ai') {
    const newData = {
      object_uuid: null, // 对象id  id
      object_id: null, // 对象英文表名  Object_id
      object_name: null, // 对象中文表名 Object_name
      suggested_time_range: null, // 时间范围
      fields_content: [], // 选择的字段
      keyId: generateUniqueKey(),
    };

    fullData.value.ai_suggested_content.push(newData);
    TotalAi.value = fullData.value.ai_suggested_content.length;
  } else {
    const newData = {
      object_uuid: null, // 对象id  id
      object_id: null, // 对象英文表名  Object_id
      object_name: null, // 对象中文表名 Object_name
      summary_time_range: null, // 时间范围
      fields_content: [], // 选择的字段
      keyId: generateUniqueKey(),
    };
    fullData.value.ai_summary_content.push(newData);
    AiTotal.value = fullData.value.ai_summary_content.length;
  }
};

// 删除按钮
async function deletedBtn(data) {
  const index = fullData.value.ai_summary_content.findIndex(
    (item) => item.keyId === data.keyId,
  );
  if (index !== -1) {
    fullData.value.ai_summary_content.splice(index, 1);
  }
  AiTotal.value = fullData.value.ai_summary_content.length;
}
async function deletedBtnAi(data) {
  const index = fullData.value.ai_suggested_content.findIndex(
    (item) => item.keyId === data.keyId,
  );
  if (index !== -1) {
    fullData.value.ai_suggested_content.splice(index, 1);
  }
  TotalAi.value = fullData.value.ai_suggested_content.length;
}

// 改变页数
async function pageChange(page, pageSize) {
  dataParamsfinal.page = page;
  dataParamsfinal.page_size = pageSize;
}
async function pageChangeAi(page, pageSize) {
  dataParamsAi.page = page;
  dataParamsAi.page_size = pageSize;
}

onMounted(() => {
  getObjectDetail();
  getAiList();
});

// ============================选择字段相关=======================
// 当前选择对象
const choseData = ref({});
const dataType = ref({
  type: null,
  btnType: null,
});
// 显示弹窗
const openFiled = async (data, btnType) => {
  if (!data.object_id) {
    message.warning('请先选择对象');
    return;
  }
  choseData.value = data;
  dataType.value.type = 'final';
  dataType.value.btnType = btnType;

  await nextTick();
  FieldModelApi.open();
};
const openFiledAi = async (data, btnType) => {
  if (!data.object_id) {
    message.warning('请先选择对象');
    return;
  }
  choseData.value = data;
  dataType.value.type = 'Ai';
  dataType.value.btnType = btnType;
  await nextTick();
  FieldModelApi.open();
};

const choseOpts = (data, all, type) => {
  if (type === 'final') {
    const index = fullData.value.ai_summary_content.findIndex(
      (item) => item.keyId === choseData.value?.keyId,
    );
    if (index !== -1) {
      fullData.value.ai_summary_content[index].fields_content = cloneDeep(data);
      fullData.value.ai_summary_content[index].is_all_fields = all;
    }
  } else if (type === 'Ai') {
    const index = fullData.value.ai_suggested_content.findIndex(
      (item) => item.keyId === choseData.value?.keyId,
    );
    if (index !== -1) {
      fullData.value.ai_suggested_content[index].fields_content =
        cloneDeep(data);
      fullData.value.ai_suggested_content[index].is_all_fields = all;
    }
  }
};

// ============================选择对象相关=======================
// 搜索对象
const field_readonly = ref(false);
const fetching = ref(false);
const selectOption = ref([]);
const openMore = ref(false);
const state = reactive({
  selectedRowKeys: [],
});
const moreDataSource = ref([]);
const moreParems = reactive({
  page: 1,
  page_size: 10,
  total: 0,
  keywords: null,
});
const loading = ref(false);
const moreColumns = ref([]);
// 获取下拉选项列表
async function getSelectOptions(value, page, page_size) {
  selectOption.value = [];

  const dataParams = {
    conditions: [],
    field: 'Field_Related_To',
    object_id: 'Field',
    keywords: value,
    page,
    page_size,
  };
  const { data } = await getTableLookUpListApi(
    'field',
    'Field_Related_To',
    dataParams,
  );
  return data;
}
const changeValue = (value, option, items) => {
  if (value) {
    if (items) {
      items.object_uuid = option.id;
      items.object_id = option.Object_id;
      items.object_name = option.Object_name;
      state.selectedRowKeys[0] = items.object_uuid;
      items.fields_content = [];
    }
  } else {
    items.object_uuid = null;
    items.object_id = null;
    items.object_name = null;
    state.selectedRowKeys = [];
    items.fields_content = [];
  }
};
const searchValue = debounce(async (value) => {
  selectOption.value = [];
  if (!value) return;
  fetching.value = true;
  const { data } = await getSelectOptions(value, 1, 5);
  selectOption.value = data.map((item) => {
    return {
      label: item.name,
      value: item.name,
      ...item,
    };
  });
  fetching.value = false;
}, 500);

async function onSearch(value) {
  if (!value) {
    moreParems.keywords = null;
  }
  if (value) {
    moreParems.keywords = value;
  }
  moreParems.page = 1;
  moreParems.page_size = 10;
  loading.value = true;
  const dataSource = await getSelectOptions(moreParems.keywords, 1, 10);
  moreDataSource.value = dataSource.data.map((item) => {
    return {
      ...item,
      key: item.id,
    };
  });

  moreParems.total = dataSource.total;
  loading.value = false;
}
// 当前选择对象
const nowData = ref({});
async function openMoreModal(items) {
  const { data } = await getTableHeaderColumnApi('Field', 'Field_Related_To');
  moreColumns.value = data
    .filter((item) => item.key !== 'id')
    .map((item) => {
      return {
        title: item.name,
        key: item.key,
        dataIndex: item.key,
        type: item.type,
        width: 'auto',
        align: 'center',
      };
    });
  openMore.value = true;

  loading.value = true;
  moreParems.keywords = null;
  moreParems.page = 1;
  moreParems.page_size = 10;
  const dataSource = await getSelectOptions(null, 1, 10);
  moreDataSource.value = dataSource.data.map((item) => {
    return {
      ...item,
      key: item.id,
    };
  });
  moreParems.total = dataSource.total;
  state.selectedRowKeys[0] = items.object_uuid;
  nowData.value = items;
  loading.value = false;
}
const rowSelection = computed(() => {
  return {
    type: 'radio',
    selectedRowKeys: state.selectedRowKeys,
    onChange: (selectedRowKeys) => {
      state.selectedRowKeys = selectedRowKeys;
    },
    onSelect: (record) => {
      const id = cloneDeep(nowData.value.object_uuid);
      nowData.value.object_name = record.name;
      nowData.value.object_uuid = record.id;
      nowData.value.object_id = record.Object_id;
      if (id !== record.id) {
        nowData.value.fields_content = [];
      }
      openMore.value = false;
    },
  };
});
const customRow = (record) => {
  return {
    onClick: () => {
      const id = cloneDeep(nowData.value.object_uuid);
      state.selectedRowKeys = [record.key];
      nowData.value.object_name = record.name;
      nowData.value.object_uuid = record.id;
      nowData.value.object_id = record.Object_id;
      if (id !== record.id) {
        nowData.value.fields_content = [];
      }
      openMore.value = false;
    },
  };
};
async function pageChangeModel(pageNum, pageSize) {
  loading.value = true;
  const dataSource = await getSelectOptions(
    moreParems.keywords,
    pageNum,
    pageSize,
  );
  moreDataSource.value = dataSource.data.map((item) => {
    return {
      ...item,
      key: item.id,
    };
  });

  moreParems.total = dataSource.total;
  loading.value = false;
}
</script>

<template>
  <div>
    <Card title="AI助理">
      <template #extra>
        <div class="flex gap-2">
          <Button v-if="!showEdit" type="primary" @click="onEdit">
            编辑
          </Button>
          <Button v-if="showEdit" type="primary" @click="onCancel">
            取消
          </Button>
          <Button v-if="showEdit" class="ml-4" type="primary" @click="onSave">
            保存
          </Button>
        </div>
      </template>
      <div class="mb-4 mt-2 flex justify-between">
        <div class="w-[49%]">
          <div class="mb-2 ml-1 font-bold">总结专家指导文字</div>
          <Textarea
            v-model:value="fullData.ai_summary_prompt"
            :disabled="!showEdit"
            :placeholder="!showEdit ? '暂无内容' : '请输入......'"
            :rows="4"
            class="mb-5"
          />
          <div class="mb-2 ml-1 flex items-center gap-2">
            <span>自身对象时间范围：</span>
            <Select
              v-if="showEdit"
              v-model:value="fullData.self_summary_time_range"
              :options="timeOptions"
              placeholder="请选择时间范围..."
              style="width: 200px !important"
            />
            <div v-else>
              {{
                fullData.self_summary_time_range
                  ? fullData.self_summary_time_range
                  : '未选择时间范围'
              }}
            </div>
          </div>
          <div class="mb-4 flex items-center justify-between">
            <span class="ml-1 font-bold">其他相关对象</span>
            <Button v-if="showEdit" type="primary" @click="onAdd('final')">
              添加对象
            </Button>
          </div>
          <Table
            :columns="dataColumns"
            :data-source="fullData.ai_summary_content"
            :loading="AILoading"
            :pagination="{
              current: dataParamsfinal.page,
              pageSize: dataParamsfinal.page_size,
              total: AiTotal,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: pageChange,
              onShowSizeChange: (current, size) => {
                dataParamsfinal.page_size = size;
                pageChange(1, size);
              },
            }"
            :scroll="{ y: 340 }"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'object_name'">
                <div v-if="showEdit" class="select-search-group">
                  <Select
                    v-model:value="record.object_name"
                    :disabled="field_readonly"
                    :not-found-content="fetching ? undefined : null"
                    :options="selectOption"
                    allow-clear
                    class="custom-select"
                    placeholder="请选择或搜索"
                    show-search
                    @change="
                      (value, option) => changeValue(value, option, record)
                    "
                    @search="searchValue"
                  >
                    <template v-if="selectOption.length === 0" #notFoundContent>
                      <div class="flex-center">加载中...</div>
                    </template>
                    <template #suffixIcon>
                      <span style="display: none"></span>
                    </template>
                  </Select>
                  <Button
                    class="search-trigger"
                    type="primary"
                    @click="openMoreModal(record)"
                  >
                    <VbenIcon class="size-4" icon="ic:baseline-search" />
                  </Button>
                </div>
                <text v-if="!showEdit">{{ record.object_name }}</text>
              </template>
              <template v-if="column.key === 'field'">
                <Button
                  v-if="showEdit"
                  type="primary"
                  @click="openFiled(record, 'edit')"
                >
                  编辑字段
                </Button>
                <Button
                  v-if="!showEdit"
                  type="primary"
                  @click="openFiled(record, 'look')"
                >
                  查看字段
                </Button>
              </template>
              <template v-if="column.key === 'time'">
                <Select
                  v-if="showEdit"
                  v-model:value="record.summary_time_range"
                  :options="timeOptions"
                  class="w-[100%]"
                  placeholder="请选择..."
                  style="width: 100% !important"
                />
                <text v-if="!showEdit">{{ record.summary_time_range }}</text>
              </template>
              <template v-if="column.key === 'operation'">
                <div class="flex justify-center gap-6">
                  <Popconfirm
                    :disabled="!showEdit"
                    cancel-text="取消"
                    ok-text="确定"
                    title="您确定要删除吗？"
                    @confirm="deletedBtn(record)"
                  >
                    <Button :disabled="!showEdit" type="primary"> 删除 </Button>
                  </Popconfirm>
                </div>
              </template>
            </template>
          </Table>
        </div>
        <Divider
          class="border-theme-color"
          dashed
          style="height: auto"
          type="vertical"
        />

        <div class="w-[49%]">
          <div class="mb-2 ml-1 font-bold">AI助理建议指导文字</div>
          <Textarea
            v-model:value="fullData.ai_suggested_prompt"
            :disabled="!showEdit"
            :placeholder="!showEdit ? '暂无内容' : '请输入......'"
            :rows="4"
            class="mb-5"
          />
          <div class="mb-2 ml-1 flex items-center gap-2">
            <span>自身对象时间范围：</span>
            <Select
              v-if="showEdit"
              v-model:value="fullData.self_suggested_time_range"
              :options="timeOptions"
              placeholder="请选择时间范围..."
              style="width: 200px !important"
            />
            <div v-else>
              {{
                fullData.self_suggested_time_range
                  ? fullData.self_suggested_time_range
                  : '未选择时间范围'
              }}
            </div>
          </div>
          <div class="mb-4 ml-1 flex items-center justify-between">
            <span class="font-bold">其他相关对象</span>
            <Button v-if="showEdit" type="primary" @click="onAdd('Ai')">
              添加对象
            </Button>
          </div>
          <Table
            :columns="dataColumns"
            :data-source="fullData.ai_suggested_content"
            :loading="AILoading"
            :pagination="{
              current: dataParamsAi.page,
              pageSize: dataParamsAi.page_size,
              total: TotalAi,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条`,
              onChange: pageChangeAi,
              onShowSizeChange: (current, size) => {
                dataParamsAi.page_size = size;
                pageChangeAi(1, size);
              },
            }"
            :scroll="{ y: 340 }"
            bordered
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'object_name'">
                <div v-if="showEdit" class="select-search-group">
                  <Select
                    v-model:value="record.object_name"
                    :disabled="field_readonly"
                    :not-found-content="fetching ? undefined : null"
                    :options="selectOption"
                    allow-clear
                    class="custom-select"
                    placeholder="请选择或搜索"
                    show-search
                    @change="
                      (value, option) => changeValue(value, option, record)
                    "
                    @search="searchValue"
                  >
                    <template v-if="selectOption.length === 0" #notFoundContent>
                      <div class="flex-center">加载中...</div>
                    </template>
                    <template #suffixIcon>
                      <span style="display: none"></span>
                    </template>
                  </Select>
                  <Button
                    class="search-trigger"
                    type="primary"
                    @click="openMoreModal(record)"
                  >
                    <VbenIcon class="size-4" icon="ic:baseline-search" />
                  </Button>
                </div>
                <text v-if="!showEdit">{{ record.object_name }}</text>
              </template>
              <template v-if="column.key === 'field'">
                <Button
                  v-if="showEdit"
                  type="primary"
                  @click="openFiledAi(record, 'edit')"
                >
                  编辑字段
                </Button>
                <Button
                  v-if="!showEdit"
                  type="primary"
                  @click="openFiledAi(record, 'look')"
                >
                  查看字段
                </Button>
              </template>
              <template v-if="column.key === 'time'">
                <Select
                  v-if="showEdit"
                  v-model:value="record.suggested_time_range"
                  :options="timeOptions"
                  class="w-[100%]"
                  placeholder="请选择..."
                  style="width: 100% !important"
                />
                <text v-if="!showEdit">{{ record.suggested_time_range }}</text>
              </template>
              <template v-if="column.key === 'operation'">
                <div class="flex justify-center gap-6">
                  <Popconfirm
                    :disabled="!showEdit"
                    cancel-text="取消"
                    ok-text="确定"
                    title="您确定要删除吗？"
                    @confirm="deletedBtnAi(record)"
                  >
                    <Button :disabled="!showEdit" type="primary"> 删除 </Button>
                  </Popconfirm>
                </div>
              </template>
            </template>
          </Table>
        </div>
      </div>
      <!-- 对象弹窗 -->
      <Modal
        v-model:open="openMore"
        :footer="null"
        title="对象可选列表"
        width="70%"
      >
        <InputSearch
          v-model:value="moreParems.keywords"
          allow-clear
          class="mb-4 w-[30%]"
          enter-button
          placeholder="输入关键字"
          @search="onSearch"
        />
        <Table
          :columns="moreColumns"
          :custom-row="customRow"
          :data-source="moreDataSource"
          :loading="loading"
          :pagination="false"
          :row-selection="rowSelection"
          :scroll="{ y: 500 }"
          bordered
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template
              v-if="
                column.type === 'dropdownlist' ||
                column.type === 'Lookup_Relationship'
              "
            >
              {{ record[column.dataIndex]?.name }}
            </template>
            <template v-if="column.type === 'Checkbox'">
              <Switch
                v-model:checked="record[column.dataIndex]"
                :disabled="true"
                size="small"
              />
            </template>
          </template>
          <template #footer>
            <div class="text-right">
              <Pagination
                v-model:current="moreParems.page"
                v-model:page-size="moreParems.page_size"
                :show-size-changer="true"
                :show-total="() => `共 ${moreParems.total} 条`"
                :total="moreParems.total"
                size="small"
                @change="pageChangeModel"
              />
            </div>
          </template>
        </Table>
      </Modal>
    </Card>
    <FieldModal
      :chose-data="choseData"
      :data-type="dataType"
      @chose-opts="choseOpts"
    />
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>

<style lang="scss" scoped>
.select-search-group {
  position: relative;
  display: inline-flex;
  width: 100% !important;
}

.custom-select :deep(.ant-select-selector) {
  // width: 80% !important;
  border-right: none !important;
  border-radius: 4px 0 0 4px !important;
  transition: all 0.3s;
}

:deep(.ant-select) {
  width: 80% !important;
}

.search-trigger {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  border-left: none !important;
  border-radius: 0 4px 4px 0 !important;
  box-shadow: none;
}

.select-search-group:hover .custom-select :deep(.ant-select-selector),
.select-search-group:hover .search-trigger {
  border-color: #40a9ff !important;
}

.custom-select:focus-within + .search-trigger {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
}

.custom-select :deep(.ant-select-arrow) {
  display: none;
}

:deep(.ant-pagination-options-size-changer) {
  width: auto !important;
}
</style>
