<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import {
  Button,
  Card,
  FormItem,
  Input,
  message,
  Popconfirm,
  Table,
} from 'ant-design-vue';

import {
  deleteTableItemApi,
  editTableItemApi,
  getTableDataApi,
  getTableItemApi,
} from '#/api';

import AddModel from '../../add/AddModel.vue';
import EditModel from '../../edit/EditModel.vue';

const route = useRoute();
const saveLoading = ref(false);
const deletedLoading = ref(false);

// 新增弹窗
const [AddModal, AddmodalApi] = useVbenModal({
  connectedComponent: AddModel,
});

// 编辑弹窗
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: EditModel,
});

const engineColumns = [
  {
    title: '引擎名称',
    dataIndex: 'engine_name',
    key: 'engine_name',
    align: 'center',
  },
  {
    title: '引擎标识名称',
    dataIndex: 'engine_key_name',
    key: 'engine_key_name',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'edit',
    key: 'edit',
    align: 'center',
    width: 200,
  },
];

const engineList = ref([]);
const editRecardId = ref(null);

function openEdit(id) {
  editRecardId.value = id;
  setTimeout(() => {
    editModalApi.open();
  }, 100);
}

// 获取引擎列表
async function getEngineList() {
  const { data } = await getTableDataApi('ai_engine', {
    page: 1,
    page_size: 1000,
    group_conditions: {
      class_name: 'string',
      field_identifier: 'ai_service_provider',
      value: route.params.id,
    },
  });

  engineList.value = data.data.map((item) => {
    return {
      engine_name: item.engine_name,
      engine_key_name: item.engine_key_name.name,
      id: item.id,
    };
  });
}

const serverMessage = reactive({
  provider_name: null,
  ai_service_key: null,
  server_id: null,
});
// 获取AI服务信息
async function getAiServiceInfo() {
  try {
    const { data } = await getTableItemApi(
      'ai_service_provider',
      route.params.id,
    );
    serverMessage.ai_service_key = data.ai_service_key;
    serverMessage.provider_name = data.provider_name;
    serverMessage.server_id = data.server_id.name;
  } catch {}
}

// 保存AI服务信息
async function saveServer() {
  try {
    saveLoading.value = true;
    await editTableItemApi(
      'ai_service_provider',
      route.params.id,
      serverMessage,
    );
    saveLoading.value = false;
    message.success('保存成功');
  } catch {
    saveLoading.value = false;
  }
}

// 删除引擎记录
async function deletedRecord(id) {
  try {
    deletedLoading.value = true;
    await deleteTableItemApi('ai_engine', [id]);
    deletedLoading.value = false;
    message.success('删除成功');
    getEngineList();
  } catch {
    deletedLoading.value = false;
  }
}

onMounted(() => {
  getEngineList();
  getAiServiceInfo();
});
</script>

<template>
  <div class="h-full p-2">
    <Card title="AI服务信息">
      <template #extra>
        <Button :loading="saveLoading" type="primary" @click="saveServer">
          保存
        </Button>
      </template>
      <div class="mb-2 flex gap-5">
        <FormItem class="m-0" label="服务商名称">
          <Input
            v-model:value="serverMessage.provider_name"
            class="w-[250px]"
          />
        </FormItem>
        <FormItem class="m-0" label="AI服务Key">
          <Input
            v-model:value="serverMessage.ai_service_key"
            class="w-[250px]"
          />
        </FormItem>
        <FormItem class="m-0" label="服务商ID">
          <Input
            v-model:value="serverMessage.server_id"
            class="w-[250px]"
            disabled
          />
        </FormItem>
      </div>
    </Card>
    <Table
      :columns="engineColumns"
      :data-source="engineList"
      :pagination="false"
      bordered
      class="mt-4"
      size="small"
    >
      <template #title>
        <div class="flex items-center justify-between px-4">
          <p class="font-bold">服务商可用引擎列表</p>
          <Button type="primary" @click="AddmodalApi.open()">新增</Button>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'edit'">
          <div class="flex-center gap-2">
            <Button type="primary" @click="openEdit(record.id)">修改</Button>

            <Popconfirm
              cancel-text="否"
              ok-text="是"
              title="您确认删除该数据?"
              @confirm="deletedRecord(record.id)"
            >
              <Button :loading="deletedLoading" danger type="primary">
                删除
              </Button>
            </Popconfirm>
          </div>
        </template>
      </template>
    </Table>

    <AddModal
      object-id="526ed889-1f3e-498e-8be0-e6d6720b92de"
      object-name="ai_engine"
      @clear-filter="() => getEngineList()"
    />

    <EditModal
      :edit-recard-id="editRecardId"
      object-id="526ed889-1f3e-498e-8be0-e6d6720b92de"
      object-name="ai_engine"
      @refresh="() => getEngineList()"
    />
  </div>
</template>

<style></style>
