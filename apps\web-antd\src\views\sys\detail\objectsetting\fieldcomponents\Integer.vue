<script setup>
import { computed, onMounted, ref, watch } from 'vue';

import { VbenSpinner } from '@vben/common-ui';

import { Button, Card, Form, FormItem, message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { fieldObjEditApi, getFieldMsgApi } from '#/api';
import {
  Checkbox,
  InputInteger,
  InputNumber,
  InputText,
  InputTextArea,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const props = defineProps({
  fieldId: {
    type: String,
    default: null,
  },
  twoacceptData: {
    type: Object,
    default: () => ({}),
  },
  formDatanew: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['cancelEdit', 'toThree', 'toOne']);

const { isLoading, startLoading, stopLoading } = useLoading();

// 表单数据对象
const formData = ref({});
// 字段id
const fielded = ref('');
// 字段类型
const fieldType = ref('');
// 是否是新建页面
const newIf = ref(false);
// 显示表单
const showForm = ref(false);
onMounted(async () => {
  fielded.value = props.fieldId || '';
  if (props.twoacceptData && Object.keys(props.twoacceptData).length > 0) {
    fieldType.value = props.twoacceptData?.type;
    newIf.value = props.twoacceptData?.new;
    formData.value = props.formDatanew;
    formData.value.Field_type = fieldType.value;
    stopLoading();
    createForm();
    showForm.value = true;
  } else {
    await getFieldDetail();
  }
});

// 获取字段详情信息
async function getFieldDetail() {
  startLoading();
  const { data } = await getFieldMsgApi(fielded.value || '');
  formData.value = data;
  createForm();
  stopLoading();
  showForm.value = true;
}

// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'Checkbox') return Checkbox;
  if (type === 'integer') return InputInteger;
}
// 表单校验
const formRef = ref();
const rules = {
  Field_name: [{ required: true, message: '请输入字段名!', trigger: 'blur' }],
  Field_id: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (!value) {
          throw new Error('请输入字段标签API!');
        }
        const regex = /^[A-Z]\w*$/i;
        if (!regex.test(value)) {
          throw new Error('格式错误!');
        }
      },
      trigger: 'blur',
    },
  ],
  Field_length: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
  Field_orderby: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
};

// ==============表单属性==========
const formList = ref([]);
const modelForm = computed(() => {
  // 处理modalForm值
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
// 同步更新fromData对象值
watch(
  () => formList.value,
  (newData) => {
    newData.forEach((item) => {
      if (formData.value[item.field_api] !== undefined) {
        formData.value[item.field_api] = item.value;
      }
    });
  },
  { deep: true }, // 深度监听
);

async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: true,
      Field_Help_Text: '字段类型',
      value: newIf.value ? fieldType.value : formData.value.Field_type.id,
      title: '字段类型',
      field_api: '',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入字段名称',
      value: formData.value.Field_name,
      title: '字段名称',
      field_api: 'Field_name',
      sort: 2,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入以字母和下划线组合的字符',
      value: formData.value.Field_id,
      title: '字段标签API',
      field_api: 'Field_id',
      sort: 3,
    },
    {
      type: 'integer',
      field_readonly: false,
      Field_Help_Text: '请输入长度',
      value: formData.value.Field_length,
      title: '长度',
      field_api: 'Field_length',
      sort: 10,
    },
    {
      type: 'integer',
      field_readonly: false,
      Field_Help_Text: '请输入序号',
      value: formData.value.Field_orderby,
      title: '字段排序',
      field_api: 'Field_orderby',
      sort: 20,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_Active,
      title: '字段启用',
      field_api: 'Field_Active',
      sort: 20,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: formData.value.Field_Description,
      title: '描述',
      Field_Help_Text: '请输入...',
      field_api: 'Field_Description',
      sort: 30,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.Field_Help_Text,
      title: '帮助文本',
      field_api: 'Field_Help_Text',
      sort: 40,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_Required,
      title: '必需',
      field_api: 'Field_Required',
      Field_Help_Text: '将此字段设置为针对外部系统的唯一记录标识符',
      sort: 50,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_readonly,
      title: '只读',
      field_api: 'Field_readonly',
      sort: 60,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_compsite_id,
      title: '外部ID',
      field_api: 'Field_compsite_id',
      sort: 80,
      Field_Help_Text: '此字段中总是需要一个值来保存记录',
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.Field_Default_Value,
      title: '默认值',
      field_api: 'Field_Default_Value',
      sort: 90,
    },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
}

// 保存编辑
function containsChinese(str) {
  const pattern = /[\u4E00-\u9FA5]/; // 中文字符的起始编码到结束编码
  return pattern.test(str);
}
const serveData = async () => {
  const data = cloneDeep(formData.value);
  for (const key in data) {
    if (data[key] !== null && typeof data[key] === 'object') {
      data[key] = containsChinese(data[key].name)
        ? data[key].id
        : data[key].name;
    }
  }
  await fieldObjEditApi(data);
  message.success('保存成功');
  emits('cancelEdit');
};

async function saveEdit() {
  formRef.value
    .validateFields() // 触发校验
    .then(() => {
      serveData();
    })
    .catch(() => {});
}

// 取消编辑
async function cancelEdit() {
  emits('cancelEdit');
}
// 上一步
async function toOne() {
  emits('toOne');
}

// 下一步
async function toThree() {
  formData.value.Field_type = fieldType.value;
  formRef.value
    .validateFields() // 触发校验
    .then(() => {
      emits('toThree', formData.value);
    })
    .catch(() => {});
}
</script>

<template>
  <div>
    <Card :title="newIf ? '输入字段详细信息' : '编辑自定义字段'">
      <template #extra>
        <div v-if="!newIf" class="flex gap-6">
          <Button type="primary" @click="saveEdit"> 保存 </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
        <div v-if="newIf" class="flex gap-4">
          <Button type="primary" @click="toOne"> 上一步 </Button>
          <Button type="primary" @click="toThree"> 下一步 </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
      </template>
      <template v-if="showForm">
        <div
          :style="{ height: newIf ? '58vh' : '70vh' }"
          class="overflow-y-auto"
        >
          <Form
            ref="formRef"
            :label-col="{ span: 4 }"
            :model="modelForm"
            :rules="rules"
            :wrapper-col="{
              span: 14,
            }"
            name="nestMessages"
          >
            <template v-for="(item, index) in formList" :key="index">
              <FormItem
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <component
                  :is="getComponent(item.type)"
                  v-model="item.value"
                  :parameters="item"
                  class="col-span-6 w-[70%]"
                />
              </FormItem>
            </template>
          </Form>
        </div>
      </template>
    </Card>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
