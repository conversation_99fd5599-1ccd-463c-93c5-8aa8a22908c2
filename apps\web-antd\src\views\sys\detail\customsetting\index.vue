<script setup>
import { computed, onActivated, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { VbenSpinner } from '@vben/common-ui';

import { TabPane, Tabs } from 'ant-design-vue';

import { getTableColumnApi, getTableData<PERSON>pi, getTableItemApi } from '#/api';
import { useLoading } from '#/hook';

import AIHelper from '../../setup/AIHelper.vue';
import ValueDetail from './components/ValueDetail.vue';

const { isLoading, startLoading, stopLoading } = useLoading();

const route = useRoute();
const objId = ref('');

const activeKey = ref('1');
// 改变标签事件
const changeTab = () => {};
const tabClick = async () => {};

const columnList = ref([]);
const columnLists = ref([]);
// 获取所有的 group 类型字段
const groupFields = computed(() => {
  return columnList.value.filter((item) => item.type === 'group');
});
// 判断是否有 group 字段
const hasGroupFields = computed(() => groupFields.value.length > 0);
// 获取当前对象的名称
const currentObject = () => {
  const routePathString = route.path.split('/');
  const objectName = (routePathString[2] || '').toLowerCase();

  return {
    objectName,
  };
};
// 当前数据id
const recordId = ref(null);
const objectName = currentObject().objectName;
const dataSetting = reactive({
  page: 1,
  page_size: 10,
  keywords: '',
  order_by: '',
  sort_by: '',
  conditions: [],
  is_deleted: false,
});
// 是否还未新建过数据
const newIf = ref(false);
// 是否显示右侧模块
const showData = ref(false);
// 获取所有字段列表
async function getTableColumns(active) {
  try {
    const { data } = await getTableColumnApi(objectName);
    columnList.value = data.map((item) => ({
      id: item.id,
      key: item.key,
      name: item.name,
      type: item.type,
      value: null,
      field_related_to_table: item.field_related_to_table,
    }));

    const { data: data1 } = await getTableItemApi(objectName, recordId.value);
    if (data1) {
      columnList.value.forEach((item) => {
        item.value = data1[item.key] || null;
      });
    }

    // 如果 newIf 为 true，则不做分组处理，直接作为普通列表处理
    if (newIf.value) {
      columnLists.value = columnList.value;
      activeKey.value = '1'; // 设置默认 tab
      showData.value = true;
      stopLoading();
      return;
    }

    // 否则执行原有的分组逻辑
    columnLists.value = [];
    const defaultGroup = [];
    let currentGroupIndex = -1;

    columnList.value.forEach((item) => {
      if (item.type === 'group') {
        if (defaultGroup.length > 0) {
          columnLists.value.push([
            { type: 'group', name: '设置信息' },
            ...defaultGroup,
          ]);
          defaultGroup.length = 0;
          currentGroupIndex = columnLists.value.length - 1;
        }
        currentGroupIndex = columnLists.value.length;
        columnLists.value.push([item]);
        currentGroupIndex = columnLists.value.length - 1;
      } else {
        if (currentGroupIndex < 0) {
          defaultGroup.push(item);
        } else {
          columnLists.value[currentGroupIndex].push(item);
        }
      }
    });

    if (defaultGroup.length > 0 && currentGroupIndex < 0) {
      columnLists.value.push([
        { type: 'group', name: '设置信息' },
        ...defaultGroup,
      ]);
    }

    // 设置默认激活的 tab
    if (active) {
      activeKey.value = active;
    } else {
      activeKey.value = hasGroupFields.value ? 'group-0' : '1';
    }

    showData.value = true;
    stopLoading();
  } catch {
    showData.value = true;
    stopLoading();
  }
}

// 获取当前对象数据列表
const getTable = async (key) => {
  try {
    startLoading();
    showData.value = false;
    newIf.value = false;
    const { data } = await getTableDataApi(objectName, dataSetting);
    recordId.value = data?.data.length > 0 ? data.data[0].id : null;
    if (data?.data.length === 0) {
      newIf.value = true;
    }
    if (key) {
      getTableColumns(key);
    } else {
      getTableColumns();
    }
  } catch {
    stopLoading();
  }
};

// 编辑完成后需要刷新列表
const finishAdd = () => {
  getTable();
};
const finishEdit = () => {
  getTable(activeKey.value);
};

onMounted(() => {
  objId.value = route.meta?.objectId;
});
onActivated(() => {
  getTable();
});
</script>
<template>
  <div class="h-full p-2">
    <div class="card-box h-full">
      <Tabs
        v-model:active-key="activeKey"
        class="h-full p-2"
        tab-position="left"
        @change="changeTab"
        @tab-click="tabClick"
      >
        <template v-if="!newIf">
          <!-- 动态生成 Tab -->
          <TabPane
            v-for="(groupList, index) in columnLists"
            :key="`group-${index}`"
            :tab="groupList[0]?.name"
          >
            <ValueDetail
              v-if="showData"
              :column-lists="groupList"
              :object-name="objectName"
              :record-id="recordId"
              :tab-name="groupList[0]?.name"
              class="h-full"
              @finish-edit="finishEdit"
            />
          </TabPane>

          <!-- 如果没有 group，则显示默认 tab -->
          <TabPane v-if="!hasGroupFields" key="1" tab="设置信息">
            <ValueDetail
              v-if="activeKey === '1' && showData"
              :column-lists="columnLists"
              :object-name="objectName"
              :record-id="recordId"
              class="h-full"
              tab-name="设置信息"
              @finish-edit="finishEdit"
            />
          </TabPane>
        </template>
        <template v-else>
          <TabPane key="1" tab="配置信息">
            <ValueDetail
              v-if="activeKey === '1' && showData"
              :column-lists="columnLists"
              :new-if="newIf"
              :object-name="objectName"
              :record-id="recordId"
              class="h-full"
              tab-name="配置信息"
              @finish-add="finishAdd"
            />
          </TabPane>
        </template>
      </Tabs>
      <VbenSpinner :spinning="isLoading" />
    </div>
    <AIHelper />
  </div>
</template>

<style lang="scss" scoped></style>
