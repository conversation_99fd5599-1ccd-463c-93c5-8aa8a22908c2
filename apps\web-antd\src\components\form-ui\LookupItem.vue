<!-- eslint-disable no-use-before-define -->
<script setup>
import { computed, onMounted, reactive, ref, toRefs } from 'vue';

import { VbenIcon } from '@vben/common-ui';

import {
  Button,
  FormItemRest,
  InputSearch,
  Modal,
  Pagination,
  Select,
  Switch,
  Table,
} from 'ant-design-vue';
import { debounce } from 'lodash-es';

import { getTableHeaderColumnApi, getTableLookUpListApi } from '#/api';

const props = defineProps({
  modelValue: {
    type: [Object, String],
    default: () => ({
      name: null,
      id: null,
      field: null,
    }),
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
});
const emits = defineEmits(['update:modelValue', 'findChange']);
const selectValue = ref(null);
const changeValue = (value, option) => {
  if (!value) {
    state.selectedRowKeys = [];
  }

  emits('update:modelValue', option?.id);
  emits('findChange', option);
};
if (props.parameters.defaultValue) {
  selectValue.value = props.parameters.defaultValue.name;
  emits('update:modelValue', props.parameters.defaultValue.value);
}

const selectOption = ref([]);

const fetching = ref(false);
const searchValue = debounce(async (value) => {
  selectOption.value = [];
  if (!value) return;
  fetching.value = true;
  const { data } = await getSelectOptions(value, 1, 5);
  selectOption.value = data.map((item) => {
    return {
      label: item.name,
      value: item.name,
      ...item,
    };
  });
  fetching.value = false;
}, 500);

// 获取下拉选项列表
async function getSelectOptions(value, page, page_size) {
  selectOption.value = [];
  const object_id = props.objectId || '';
  const field = props.parameters.key;

  const dataParams = {
    conditions: [],
    field,
    object_id,
    keywords: value,
    page,
    page_size,
  };
  const { data } = await getTableLookUpListApi(
    props.objectName,
    field,
    dataParams,
  );
  return data;
}

const openMore = ref(false);
const moreParems = reactive({
  page: 1,
  page_size: 10,
  total: 0,
  keywords: null,
});

const moreDataSource = ref([]);
const moreColumns = ref([]);
const loading = ref(false);
const state = reactive({
  selectedRowKeys: [],
});

const rowSelection = computed(() => {
  return {
    type: 'radio',
    selectedRowKeys: state.selectedRowKeys,
    onChange: (selectedRowKeys) => {
      state.selectedRowKeys = selectedRowKeys;
    },
    onSelect: (record) => {
      selectValue.value = record.name;
      emits('update:modelValue', record.id);
      emits('findChange', record);
      openMore.value = false;
    },
  };
});

const customRow = (record) => {
  return {
    onClick: () => {
      state.selectedRowKeys = [record.key];
      selectValue.value = record.name;
      emits('update:modelValue', record.id);
      emits('findChange', record);
      openMore.value = false;
    },
  };
};

async function openMoreModal() {
  const object_id = props.objectId || '';
  const field = props.parameters.key;
  const { data } = await getTableHeaderColumnApi(object_id, field);
  moreColumns.value = data
    .filter((item) => item.key !== 'id')
    .map((item) => {
      return {
        title: item.name,
        key: item.key,
        dataIndex: item.key,
        type: item.type,
        width: 'auto',
        align: 'center',
      };
    });
  openMore.value = true;

  loading.value = true;
  moreParems.keywords = null;
  moreParems.page = 1;
  moreParems.page_size = 10;
  const dataSource = await getSelectOptions(null, 1, 10);
  moreDataSource.value = dataSource.data.map((item) => {
    return {
      ...item,
      key: item.id,
    };
  });

  moreParems.total = dataSource.total;
  loading.value = false;
}
async function onSearch(value) {
  if (!value) {
    moreParems.keywords = null;
  }
  if (value) {
    moreParems.keywords = value;
  }
  moreParems.page = 1;
  moreParems.page_size = 10;
  loading.value = true;
  const dataSource = await getSelectOptions(moreParems.keywords, 1, 10);
  moreDataSource.value = dataSource.data.map((item) => {
    return {
      ...item,
      key: item.id,
    };
  });

  moreParems.total = dataSource.total;
  loading.value = false;
}

async function pageChange(pageNum, pageSize) {
  loading.value = true;
  const dataSource = await getSelectOptions(
    moreParems.keywords,
    pageNum,
    pageSize,
  );
  moreDataSource.value = dataSource.data.map((item) => {
    return {
      ...item,
      key: item.id,
    };
  });

  moreParems.total = dataSource.total;
  loading.value = false;
}

// 如果有值
onMounted(async () => {
  const { modelValue } = toRefs(props);
  state.selectedRowKeys = [modelValue.value?.id];
  if (modelValue.value?.name) {
    selectValue.value = modelValue.value.name || '';
    emits('update:modelValue', modelValue.value.id);
  } else {
    const object_id = props.objectId || '';
    const field = props.parameters.key;
    const dataParams = {
      conditions: [],
      field,
      object_id,
      keywords: null,
      page: 1,
      page_size: 10,
    };
    const { data } = await getTableLookUpListApi(
      props.objectName,
      field,
      dataParams,
    );

    const findSelect = data.data.find(
      (item) => item.id === modelValue.value?.id,
    );
    if (findSelect && modelValue.value?.field) {
      selectValue.value = findSelect[modelValue.value.field];
    }

    selectValue.value = modelValue.value?.id || null;
    emits('update:modelValue', selectValue.value);
  }
});
</script>

<template>
  <div>
    <div class="flex gap-1">
      <Select
        v-model:value="selectValue"
        :disabled="props.parameters.field_readonly"
        :not-found-content="fetching ? undefined : null"
        :options="selectOption"
        allow-clear
        class="flex-1"
        placeholder="点击右侧图标可选择"
        show-search
        @change="changeValue"
        @search="searchValue"
      >
        <template v-if="selectOption.length === 0" #notFoundContent>
          <div class="flex-center">加载中...</div>
        </template>
      </Select>
      <Button
        :disabled="props.parameters.field_readonly"
        @click="openMoreModal()"
      >
        <VbenIcon class="size-4" icon="ep:right" />
      </Button>
    </div>
    <FormItemRest>
      <Modal
        v-model:open="openMore"
        :footer="null"
        title="查找相关可选列表"
        width="70%"
      >
        <InputSearch
          v-model:value="moreParems.keywords"
          allow-clear
          class="mb-4 w-[30%]"
          enter-button
          placeholder="输入关键字"
          @search="onSearch"
        />
        <Table
          :columns="moreColumns"
          :custom-row="customRow"
          :data-source="moreDataSource"
          :loading="loading"
          :pagination="false"
          :row-selection="rowSelection"
          :scroll="{ y: 500 }"
          bordered
          size="small"
        >
          <template #bodyCell="{ column, record }">
            <template
              v-if="
                column.type === 'dropdownlist' ||
                column.type === 'Lookup_Relationship'
              "
            >
              {{ record[column.dataIndex]?.name }}
            </template>
            <template v-if="column.type === 'Checkbox'">
              <!-- 行[列索引] , 获取行值 -->
              <Switch
                v-model:checked="record[column.dataIndex]"
                :disabled="true"
                size="small"
              />
            </template>
          </template>
          <template #footer>
            <div class="text-right">
              <Pagination
                v-model:current="moreParems.page"
                v-model:page-size="moreParems.page_size"
                :show-size-changer="true"
                :show-total="() => `共 ${moreParems.total} 条`"
                :total="moreParems.total"
                size="small"
                @change="pageChange"
              />
            </div>
          </template>
        </Table>
      </Modal>
    </FormItemRest>
  </div>
</template>

<style scoped></style>
