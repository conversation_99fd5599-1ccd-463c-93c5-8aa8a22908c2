<script setup>
import { computed, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { VbenIcon, VbenSpinner } from '@vben/common-ui';

import { Button, Card, message, Transfer } from 'ant-design-vue';

import { findFieldApi, getObjectDetailApi, updateObjApi } from '#/api';
import { useLoading } from '#/hook';

const { isLoading, startLoading, stopLoading } = useLoading();

const route = useRoute();

const mockData = ref([]);
const targetKeys = ref([]);
// 是否右侧正在搜索
const rightSearchValue = ref('');

// 获取列表
const getMock = async () => {
  const { data: dataDetail } = await getObjectDetailApi(route.params.id || '');
  const { data } = await findFieldApi(route.params.id);
  // 处理搜索布局字符串
  const arr = dataDetail.Object_SearchLayout?.includes('/')
    ? dataDetail.Object_SearchLayout.split('/').map((keyword) => keyword.trim())
    : [dataDetail.Object_SearchLayout?.trim()];
  // 映射字段列表
  const fieldMap = {};
  data.forEach((item) => {
    fieldMap[item.name.trim()] = item;
  });
  const keys = [];
  const mData = [];
  // 处理右侧列表
  arr.forEach((keyword) => {
    const matchedItem = Object.values(fieldMap).find(
      (item) => item.name.trim() === keyword,
    );
    if (matchedItem) {
      keys.push(matchedItem.id);
      mData.push({
        key: matchedItem.id,
        title: matchedItem.name,
        chosen: true,
        num: keys.length,
        description: matchedItem.name,
      });
      delete fieldMap[matchedItem.name.trim()];
    }
  });
  // 处理左侧列表
  Object.values(fieldMap).forEach((item) => {
    mData.push({
      key: item.id,
      title: item.name,
      chosen: false,
      num: mData.length + 1,
      description: item.name,
    });
  });
  mockData.value = mData;
  targetKeys.value = keys;
  stopLoading();
};
const filterOption = (inputValue, option) => {
  return option.description.includes(inputValue);
};
// 修改右侧搜索处理方式
const handleSearch = (dir, value) => {
  // 当是右侧搜索时更新状态
  if (dir === 'right') {
    rightSearchValue.value = value;
  }
};
// 新增：复选框选中的数据（无论左右）
const selectedKeys = ref([]);
// 上移选中项
const moveUp = () => {
  const newTargetKeys = [...targetKeys.value];
  const selected = selectedKeys.value.filter((key) =>
    targetKeys.value.includes(key),
  );
  // 找到选中项的最小索引
  const minIndex = Math.min(
    ...selected.map((key) => newTargetKeys.indexOf(key)),
  );
  // 如果已经在最顶部，则不移动
  if (minIndex <= 0) return;
  // 移动选中项
  selected.forEach((key) => {
    const index = newTargetKeys.indexOf(key);
    if (index > 0) {
      // 与上一项交换位置
      [newTargetKeys[index], newTargetKeys[index - 1]] = [
        newTargetKeys[index - 1],
        newTargetKeys[index],
      ];
    }
  });
  targetKeys.value = newTargetKeys;
};

// 下移选中项
const moveDown = () => {
  const newTargetKeys = [...targetKeys.value];
  const selected = selectedKeys.value.filter((key) =>
    targetKeys.value.includes(key),
  );
  // 找到选中项的最大索引
  const maxIndex = Math.max(
    ...selected.map((key) => newTargetKeys.indexOf(key)),
  );
  // 如果已经在最底部，则不移动
  if (maxIndex >= newTargetKeys.length - 1) return;
  // 从下往上移动，避免顺序问题
  [...selected].reverse().forEach((key) => {
    const index = newTargetKeys.indexOf(key);
    if (index < newTargetKeys.length - 1) {
      // 与下一项交换位置
      [newTargetKeys[index], newTargetKeys[index + 1]] = [
        newTargetKeys[index + 1],
        newTargetKeys[index],
      ];
    }
  });
  targetKeys.value = newTargetKeys;
};

// 计算属性：判断是否有复选框选中的数据
const hasSelectedData = computed(() => {
  const rightSelectedKeys = selectedKeys.value.filter((key) =>
    targetKeys.value.includes(key),
  );
  return rightSelectedKeys.length > 0;
});
// 保存事件
const onSave = async () => {
  startLoading();
  const choseNames = targetKeys.value
    .map((key) => {
      const foundItem = mockData.value.find((item) => item.key === key);
      return foundItem ? foundItem.title : '';
    })
    .filter(Boolean); // 过滤掉可能不存在的项
  if (choseNames.length === 0) {
    const updateData = {
      id: route.params.id,
      Object_SearchLayout: null,
    };
    stopLoading();
    await updateObjApi(updateData);
    message.success('保存成功');
  } else {
    const toData = choseNames.join('/');
    const updateData = {
      id: route.params.id,
      Object_SearchLayout: toData,
    };
    stopLoading();
    await updateObjApi(updateData);
    message.success('保存成功');
  }
};
onMounted(() => {
  startLoading();
  getMock();
});
</script>
<template>
  <div>
    <Card title="搜索布局">
      <template #extra>
        <div class="flex gap-2">
          <Button type="primary" @click="onSave"> 保存 </Button>
        </div>
      </template>
      <div class="flex flex-col gap-3 p-2">
        <div>
          编辑搜索布局：选择要包括到此搜索布局中的字段。（请注意，所做的选择只决定搜索结果的显示，而不会影实际被搜索的字段。）
        </div>
        <div class="mt-6 flex w-[92%] justify-between pl-8 pr-8">
          <div class="w-[50%] text-center">
            <div class="inline-block rounded bg-gray-100 px-4 py-1">
              可用字段
            </div>
          </div>
          <div class="w-[50%] text-center">
            <div class="inline-block rounded bg-gray-100 px-4 py-1">
              选取的字段
            </div>
          </div>
        </div>
        <div class="mb-5 flex !w-[100%] justify-center">
          <Transfer
            v-model:selected-keys="selectedKeys"
            v-model:target-keys="targetKeys"
            :data-source="mockData"
            :filter-option="filterOption"
            :render="(item) => item.title"
            class="!w-[78%]"
            show-search
            @search="handleSearch"
          />
          <div class="ml-8 flex flex-col justify-center gap-4">
            <Button
              :disabled="!hasSelectedData || rightSearchValue !== ''"
              class="flex h-6 w-6 items-center justify-center p-0"
              type="primary"
              @click="moveUp"
            >
              <VbenIcon class="size-4" icon="tabler:chevron-up" />
            </Button>
            <Button
              :disabled="!hasSelectedData || rightSearchValue !== ''"
              class="flex h-6 w-6 items-center justify-center p-0"
              type="primary"
              @click="moveDown"
            >
              <VbenIcon class="size-4" icon="tabler:chevron-down" />
            </Button>
          </div>
        </div>
      </div>
    </Card>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
<style lang="scss" scoped>
:deep(.ant-transfer-list) {
  width: 45% !important;
  height: 500px !important;
}

:deep(.ant-transfer-operation) {
  gap: 16px !important;
  margin: 0 32px;
}
</style>
