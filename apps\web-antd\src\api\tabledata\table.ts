import { requestClient } from '#/api/request';

/**
 *
 * @returns 获取表格列数据
 */
export async function getTableColumnApi(objectName: string) {
  return requestClient.get(`${objectName}/label/detail`);
}

/**
 *
 * @returns 获取表格数据
 */
export async function getTableDataApi(objectName: string, data: any) {
  return requestClient.post(`/${objectName}/get_${objectName}s`, data);
}

/**
 * @returns 获取筛选器数据
 */
export async function getFilterColumnsApi(objectName: string) {
  return requestClient.get(`${objectName}/jsonstr`);
}

/**
 * @returns 获取赛选器中下拉选项的数据
 */
export async function getFilterProSelectApi(object_id: string, field: string) {
  return requestClient.get(`/select_hand/get_selects`, {
    params: {
      object_id,
      field,
    },
  });
}

/**
 * @returns 获取表格记录添加或者修改项
 */
export async function getTableAddOrEditListApi(objectName: string) {
  return requestClient.get(`${objectName}/label/edit`);
}

/**
 * @returns 获取下载日志数据
 */
export async function getDownloadLogApi(address: string) {
  return requestClient.get(`log/download_zip?path_str=${address}`, {
    responseType: 'blob',
  });
}

/**
 * @returns 获取下载模版Excel
 */

export async function getDownloadExcelApi(objectName: string) {
  return requestClient.get(`/${objectName}/download_excel`, {
    responseType: 'blob',
  });
}

/**
 * @returns 上传excel模版文件
 */

export async function uploadExcelApi(objectName: string, data: any) {
  return requestClient.post(`/${objectName}/upload_excel`, data, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * @returns 获取表格详细页绑定按钮
 */

export async function getTableDetailButtonApi(
  page_type: string,
  object_id: string,
) {
  return requestClient.get(`/button_hand/rendering`, {
    params: {
      page_type,
      object_id,
    },
  });
}

/**
 * @returns 执行记录触发按钮作业
 */

export async function getActivityButtonApi(apiUrl: string, detailId: string) {
  return requestClient.get(`/${apiUrl}/${detailId}/`);
}

/**
 * @returns 执行表格操作功能按钮
 */
export async function postOperationWorkApi(apiUrl: string, data: any) {
  return requestClient.post(`/${apiUrl}`, data);
}

/**
 * @returns 看板列表
 */
export async function getStatusClassifyApi(object: string) {
  return requestClient.get(`/${object}/dropdownlist`);
}

/**
 * @returns 看板中查找相关字段
 */
export async function getRelationApi(object: string) {
  return requestClient.get(`/${object}/relation`);
}

/**
 * @returns 获取日期列表
 */
export async function getDateListApi(object: string) {
  return requestClient.get(`/${object}/datetime`);
}

/**
 * @returns 获取字段排序
 */
export async function getTableInfoApi(object: string) {
  return requestClient.get(`/field_hand/order_List/${object}`);
}

/**
 * @returns 获取字段排序
 */
export async function getOptionsByTypeApi(options_type: string) {
  return requestClient.get(`/options_hand/type/${options_type}`);
}

/**
 * @returns 获取位置信息
 */
export async function getPostion1(object: string) {
  return requestClient.get(`/${object}/location`);
}

/**
 * @returns AI智能填充表格
 */
export async function autoFillTableApi(aiParams: string) {
  return requestClient.post(`/ai/auto_fill_table`, aiParams);
}

/**
 * @returns AI智能填充报表
 */
export async function autoEchartsTableApi(aiParams: string) {
  return requestClient.post(`/ai/auto_echarts_table`, aiParams);
}

/**
 * @returns AI智能总结|AI助手
 */
export async function autoAiAgentApi(aiParams: string) {
  return requestClient.post(`/ai/auto_ai_agent`, aiParams);
}
