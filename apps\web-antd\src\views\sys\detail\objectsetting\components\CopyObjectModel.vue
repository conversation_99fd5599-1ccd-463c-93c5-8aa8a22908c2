<script setup>
import { computed, ref, watch } from 'vue';

import { useVbenModal, VbenSpinner } from '@vben/common-ui';

import { Form, FormItem, message } from 'ant-design-vue';

import { copyObjapi, issueObjapi } from '#/api';
import {
  InputInteger,
  InputNumber,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const props = defineProps({
  objDetail: {
    type: Object,
    default: () => ({}),
  },
});

// 表数据
const objForm = ref({});
const { isLoading, startLoading, stopLoading } = useLoading();

// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'integer') return InputInteger;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
}

const formRef = ref();
const formList = ref([]);
async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入对象名称',
      value: objForm.value.Object_name,
      title: '对象名称',
      field_api: 'Object_name',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入以字母数字下划线组成的字符',
      value: objForm.value.Object_id,
      title: '对象API',
      field_api: 'Object_id',
      sort: 2,
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择对象类型',
      value: objForm.value.Object_type.id,
      title: '对象类型',
      field_api: 'Object_type',
      key: 'Object_type',
      sort: 5,
    },
    {
      type: 'integer',
      field_readonly: false,
      Field_Help_Text: '请输入序号',
      value: objForm.value.Object_orderby,
      title: '对象排序',
      field_api: 'Object_orderby',
      sort: 10,
    },
    {
      type: 'Lookup_Relationship',
      field_readonly: false,
      Field_Help_Text: '请选择应用包',
      value: objForm.value.Object_app_id,
      title: '所属应用包',
      field_api: 'Object_app_id',
      sort: 15,
      key: 'Object_app_id',
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: objForm.value.Object_Description,
      title: '对象描述',
      Field_Help_Text: '请输入...',
      field_api: 'Object_Description',
      sort: 17,
    },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
}

const rules = {
  Object_name: [{ required: true, message: '请输入字段名!', trigger: 'blur' }],
  Object_id: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (!value) {
          throw new Error('请输入对象API!');
        }
        const regex = /^[A-Z]\w*$/i;
        if (!regex.test(value)) {
          throw new Error('格式错误!');
        }
      },
      trigger: 'blur',
    },
  ],
  Object_orderby: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
  Object_app_id: [
    { required: true, message: '请选择应用包!', trigger: 'blur' },
  ],
  Object_type: [
    { required: true, message: '请选择对象类型!', trigger: 'blur' },
  ],
};
const isFirstLoad = ref(true);
const modelForm = computed(() => {
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
watch(
  () => formList.value,
  (newData) => {
    if (isFirstLoad.value === true) {
      isFirstLoad.value = false; // 更新标志变量以表示已初始化
      return; // 初次加载时不执行任何操作
    }
    newData.forEach((item) => {
      objForm.value[item.field_api] = item.value;
    });
  },
  { deep: true }, // 深度监听
);

const [Modal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[600px]',
  contentClass: 'p-4',
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    formRef.value
      .validateFields() // 触发校验
      .then(async () => {
        startLoading();
        setTimeout(() => {
          stopLoading();
        }, 2000);
        const { data } = await copyObjapi(objForm.value);
        const res = await issueObjapi(data?.object_id);
        if (res === 200) {
          stopLoading();
          message.success('发布成功');
          modalApi.close();
        } else {
          stopLoading();
          message.success('发布失败');
        }
      })
      .catch(() => {});
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      formList.value = [];
      objForm.value = props?.objDetail
        ? {
            Object_name: `${props.objDetail.Object_name}-副本`,
            Object_id: `${props.objDetail.Object_id}_copy`,
            Object_type: props.objDetail.Object_type,
            Object_orderby: props.objDetail.Object_orderby,
            Object_app_id: props.objDetail.Object_app_id,
            Object_Description: props.objDetail.Object_Description,
            Object_Deployment_Status:
              props.objDetail.Object_Deployment_Status?.id,
            id: props.objDetail.id,
          }
        : {
            Object_name: null,
            Object_id: null,
            Object_type: 'L',
            Object_orderby: null,
            Object_app_id: null,
            Object_Description: null,
            Object_Deployment_Status: null,
          };
      isFirstLoad.value = true;
      createForm();
    }
    stopLoading();
  },
});
</script>

<template>
  <Modal title="对象复制">
    <Form
      ref="formRef"
      :label-col="{ span: 6 }"
      :model="modelForm"
      :rules="rules"
      :wrapper-col="{ span: 18 }"
      autocomplete="off"
      name="basic"
    >
      <div class="grid">
        <template v-for="(item, index) in formList" :key="index">
          <FormItem :label="item.title" :name="item.field_api" class="m-[2%]">
            <component
              :is="getComponent(item.type)"
              v-model="item.value"
              :parameters="item"
              class="col-span-6 w-[100%]"
              object-id="Object"
              object-name="object"
            />
          </FormItem>
        </template>
      </div>
    </Form>
    <VbenSpinner :spinning="isLoading" />
  </Modal>
</template>

<style></style>
