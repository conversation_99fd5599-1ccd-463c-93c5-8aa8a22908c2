<script lang="ts" setup>
import { ref } from 'vue';

import {
  <PERSON>benchHeader,
  WorkbenchQuickNav,
  type WorkbenchQuickNavItem,
} from '@vben/common-ui';
import { iconExists } from '@vben/icons';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';

const userStore = useUserStore();
const accessStore = useAccessStore();

const quickNavItems = ref<WorkbenchQuickNavItem[]>([]);

quickNavItems.value = accessStore.menuObjectList.map((item) => {
  return {
    title: item.name,
    children: item.children.map((item_child: any) => {
      return {
        color: getRandomColor(),
        icon: iconExists(item_child.ico) ? item_child.ico : 'ci:list-unordered',
        title: item_child.name,
        path: item_child.path,
      };
    }),
  };
});

// 生成随机颜色
function getRandomColor() {
  const letters = '0123456789ABCDEF';
  let color = '#';
  for (let i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}
</script>

<template>
  <div class="p-5">
    <WorkbenchHeader
      :avatar="userStore.userInfo?.avatar || preferences.app.defaultAvatar"
    >
      <template #title>
        您好, {{ userStore.userInfo?.realName }}, 开始您一天的工作吧！
      </template>
    </WorkbenchHeader>

    <div class="mt-5 flex w-full flex-col gap-5">
      <template v-for="item in quickNavItems" :key="item.title">
        <WorkbenchQuickNav
          :items="item.children"
          :title="item.title"
          class="mt-5 lg:mt-0"
        />
      </template>
    </div>
  </div>
</template>
