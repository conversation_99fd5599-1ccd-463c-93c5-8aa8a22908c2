{
  "tailwindCSS.experimental.configFile": "internal/tailwind-config/src/index.ts",
  // workbench
  "workbench.list.smoothScrolling": true,
  "workbench.startupEditor": "newUntitledFile",
  "workbench.tree.indent": 10,
  "workbench.editor.highlightModifiedTabs": true,
  "workbench.editor.closeOnFileDelete": true,
  "workbench.editor.limit.enabled": true,
  "workbench.editor.limit.perEditorGroup": true,
  "workbench.editor.limit.value": 5,

  // editor
  "editor.tabSize": 2,
  "editor.detectIndentation": false,
  "editor.cursorBlinking": "expand",
  "editor.largeFileOptimizations": false,
  "editor.accessibilitySupport": "off",
  "editor.cursorSmoothCaretAnimation": "on",
  "editor.guides.bracketPairs": "active",
  "editor.inlineSuggest.enabled": true,
  "editor.suggestSelection": "recentlyUsedByPrefix",
  "editor.acceptSuggestionOnEnter": "smart",
  "editor.suggest.snippetsPreventQuickSuggestions": false,
  "editor.stickyScroll.enabled": true,
  "editor.hover.sticky": true,
  "editor.suggest.insertMode": "replace",
  "editor.bracketPairColorization.enabled": true,
  "editor.autoClosingBrackets": "beforeWhitespace",
  "editor.autoClosingDelete": "always",
  "editor.autoClosingOvertype": "always",
  "editor.autoClosingQuotes": "beforeWhitespace",
  "editor.wordSeparators": "`~!@#%^&*()=+[{]}\\|;:'\",.<>/?",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit",
    "source.fixAll.stylelint": "explicit",
    "source.organizeImports": "never"
  },
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // extensions
  "extensions.ignoreRecommendations": true,

  // terminal
  "terminal.integrated.cursorBlinking": true,
  "terminal.integrated.persistentSessionReviveProcess": "never",
  "terminal.integrated.tabs.enabled": true,
  "terminal.integrated.scrollback": 10000,
  "terminal.integrated.stickyScroll.enabled": true,

  // files
  "files.eol": "\n",
  "files.insertFinalNewline": true,
  "files.simpleDialog.enable": true,
  "files.associations": {
    "*.ejs": "html",
    "*.art": "html",
    "**/tsconfig.json": "jsonc",
    "*.json": "jsonc",
    "package.json": "json"
  },

  "files.exclude": {
    "**/.eslintcache": true,
    "**/bower_components": true,
    "**/.turbo": true,
    "**/.idea": true,
    "**/tmp": true,
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.stylelintcache": true,
    "**/.DS_Store": true,
    "**/vite.config.mts.*": true,
    "**/tea.yaml": true
  },
  "files.watcherExclude": {
    "**/.git/objects/**": true,
    "**/.git/subtree-cache/**": true,
    "**/.vscode/**": true,
    "**/node_modules/**": true,
    "**/tmp/**": true,
    "**/bower_components/**": true,
    "**/dist/**": true,
    "**/yarn.lock": true
  },

  // search
  "search.searchEditor.singleClickBehaviour": "peekDefinition",
  "search.followSymlinks": false,
  // 在使用搜索功能时，将这些文件夹/文件排除在外
  "search.exclude": {
    "**/node_modules": true,
    "**/*.log": true,
    "**/*.log*": true,
    "**/bower_components": true,
    "**/dist": true,
    "**/elehukouben": true,
    "**/.git": true,
    "**/.github": true,
    "**/.gitignore": true,
    "**/.svn": true,
    "**/.DS_Store": true,
    "**/.vitepress/cache": true,
    "**/.idea": true,
    "**/.vscode": false,
    "**/.yarn": true,
    "**/tmp": true,
    "*.xml": true,
    "out": true,
    "dist": true,
    "node_modules": true,
    "CHANGELOG.md": true,
    "**/pnpm-lock.yaml": true,
    "**/yarn.lock": true
  },

  "debug.onTaskErrors": "debugAnyway",
  "diffEditor.ignoreTrimWhitespace": false,
  "npm.packageManager": "pnpm",

  "css.validate": false,
  "less.validate": false,
  "scss.validate": false,

  // extension
  "emmet.showSuggestionsAsSnippets": true,
  "emmet.triggerExpansionOnTab": false,

  "errorLens.enabledDiagnosticLevels": ["warning", "error"],
  "errorLens.excludeBySource": ["cSpell", "Grammarly", "eslint"],

  "stylelint.enable": true,
  "stylelint.packageManager": "pnpm",
  "stylelint.validate": ["css", "less", "postcss", "scss", "vue"],
  "stylelint.snippet": ["css", "less", "postcss", "scss", "vue"],

  "typescript.inlayHints.enumMemberValues.enabled": true,
  "typescript.preferences.preferTypeOnlyAutoImports": true,
  "typescript.preferences.includePackageJsonAutoImports": "on",

  "eslint.validate": [
    "javascript",
    "typescript",
    "javascriptreact",
    "typescriptreact",
    "vue",
    "html",
    "markdown",
    "json",
    "jsonc",
    "json5"
  ],

  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]
  ],

  "github.copilot.enable": {
    "*": true,
    "markdown": true,
    "plaintext": false,
    "yaml": false
  },

  "cssVariables.lookupFiles": ["packages/core/base/design/src/**/*.css"],

  "i18n-ally.localesPaths": [
    "packages/locales/src/langs",
    "playground/src/locales/langs",
    "apps/*/src/locales/langs"
  ],
  "i18n-ally.pathMatcher": "{locale}.json",
  "i18n-ally.enabledParsers": ["json", "ts", "js", "yaml"],
  "i18n-ally.sourceLanguage": "en",
  "i18n-ally.displayLanguage": "zh-CN",
  "i18n-ally.enabledFrameworks": ["vue", "react"],

  // 控制相关文件嵌套展示
  "explorer.fileNesting.enabled": true,
  "explorer.fileNesting.expand": false,
  "explorer.fileNesting.patterns": {
    "*.ts": "$(capture).test.ts, $(capture).test.tsx, $(capture).spec.ts, $(capture).spec.tsx, $(capture).d.ts",
    "*.tsx": "$(capture).test.ts, $(capture).test.tsx, $(capture).spec.ts, $(capture).spec.tsx,$(capture).d.ts",
    "*.env": "$(capture).env.*",
    "README.md": "README*,CHANGELOG*,LICENSE,CNAME",
    "package.json": "pnpm-lock.yaml,pnpm-workspace.yaml,.gitattributes,.gitignore,.gitpod.yml,.npmrc,.browserslistrc,.node-version,.git*,.tazerc.json",
    "eslint.config.mjs": ".eslintignore,.prettierignore,.stylelintignore,.commitlintrc.*,.prettierrc.*,stylelint.config.*,.lintstagedrc.mjs,cspell.json",
    "tailwind.config.mjs": "postcss.*"
  },
  "commentTranslate.hover.enabled": false,
  "i18n-ally.keystyle": "nested",
  "commentTranslate.multiLineMerge": true,
  "vue.server.hybridMode": true,
  "typescript.tsdk": "node_modules/typescript/lib",
  "references.preferredLocation": "view"
}
