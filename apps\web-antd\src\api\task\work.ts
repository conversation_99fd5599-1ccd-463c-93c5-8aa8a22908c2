import { requestClient } from '#/api/request';

/**
 *
 * @returns 获取作业任务列表
 */
export async function getTaskListApi(workId: string) {
  return requestClient.get(`/task/work/${workId}`);
}

/**
 *
 * @returns 获取作业执行监控接口
 */
export async function getLogDataListApi(
  workID: string,
  page: string,
  pageSize: string,
) {
  return requestClient.post(
    `/job_execution_monitoring/get_job_execution_monitorings`,
    {
      page,
      page_size: pageSize,
      order_by: 'CreatedDate',
      sort_by: 'DESC',
      conditions: [
        {
          field_identifier: 'Job_execution_monitoring_no',
          operator: 'EQUALS',
          value: [workID],
          class_name: 'string',
          format: 'input',
          to_value: null,
        },
      ],
    },
  );
}

/**
 * @returns 获取外部作业调用列表
 */
export async function getExternalWorkListApi(workId: string) {
  return requestClient.get(`/works/external_works/${workId}`);
}

/**
 * @returns 获取作业循环任务
 */

export async function getWorkLoopTaskApi(workId: string, task_id: string) {
  return requestClient.get(`/task/circle?work_id=${workId}&task_id=${task_id}`);
}

/**
 * @returns 获取作业决策任务
 */

export async function getWorkRepetitionTaskApi(
  workId: string,
  task_id: string,
) {
  return requestClient.get(
    `/task/type?work_id=${workId}&task_type=decision_condition&task_id=${task_id}`,
  );
}

/**
 * @returns 添加作业任务
 *
 */
export async function addWorkTaskApi(data: any) {
  return requestClient.post(`/task/add`, data);
}

/**
 * @returns 删除任务步骤
 */
export async function deleteWorkStepTaskApi(id: string) {
  return requestClient.delete(`/task/${id}`);
}

/**
 * @returns 获取调用清单接口
 */
export async function getBtnListApi(data: object) {
  return requestClient.post(`/button_hand/list_by_work`, data);
}

/**
 *
 * @returns 获取父作业列表
 */
export async function getParentWorkListApi(workID: string) {
  return requestClient.get(`works/parent_works/${workID}`);
}

/**
 * @returns 作业任务步骤提交
 */

export async function postWorkTaskApi(data: any) {
  return requestClient.post(`/task/modify`, data);
}

/**
 * @returns 获取设置变量列表
 */

export async function getSetVariableListApi(workId: string) {
  return requestClient.get(`/rtc/task_variable?workId=${workId}`);
}

/**
 * @returns 获取作业详情
 */
export async function getWorkDetailApi(workID: string) {
  return requestClient.get(`processautomation/detail/${workID}`);
}

/**
 *
 * @returns 修改作业
 */
export async function putModifyWorkApi(workId: string, data: object) {
  return requestClient.put(`/processautomation/edit/${workId}`, data);
}

/**
 * @returns 获取子作业列表
 */

export async function getSubWorkListApi(params: any) {
  return requestClient.get(`works/var`, {
    params,
  });
}

/**
 * @returns 作业校验SQL
 */

export async function getWorkCheckSqlApi(params: any) {
  return requestClient.get(`/rtc/check_sql_syntax`, {
    params,
  });
}

/**
 * @returns 测试程序
 */
export async function checkPythonStrApi(workId: string, taskId: string) {
  return requestClient.get(
    `rtc/check_python_str?workId=${workId}&taskId=${taskId}`,
  );
}

/**
 * @returns 获取外部任务子作业可选列表
 */

export async function getExternalWorkChildListApi(id: string) {
  return requestClient.get(`/external_interface/get_cascade_menus`, {
    params: {
      external_interface_id: id,
    },
  });
}

/**
 * @returns 获取外部作业详情
 */

export async function getExternalWorkDetailApi(id: string, idFn: string) {
  return requestClient.get(`/external_interface/info`, {
    params: {
      external_interface_id: id,
      external_interface_function_id: idFn,
    },
  });
}

/**
 * @returns 获取操作说明
 */

export async function getDocumentInstrApi(workId: string) {
  return requestClient.get(`/rtc/generate_api_desc`, {
    params: {
      workId,
    },
  });
}

/**
 * @returns 复制作业
 */
export async function postCopyWorkApi(data: any) {
  return requestClient.post(`/works/copy`, data);
}

/**
 * @returns 获取流程图编码
 */

export async function getFlowCodeApi(data: any) {
  return requestClient.post(`/ai/auto_generate_mermaid`, data);
}
