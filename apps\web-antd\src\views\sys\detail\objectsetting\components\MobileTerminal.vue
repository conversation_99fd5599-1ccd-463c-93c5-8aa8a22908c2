<script setup>
import { reactive } from 'vue';

import { Button, FormItem, Select } from 'ant-design-vue';

const formData = reactive({
  url: null,
});
</script>

<template>
  <div class="p-2">
    <FormItem label="选择移动端页面">
      <div class="flex gap-3">
        <Select v-model:value="formData.url" :options="[]" show-search />
        <Button type="primary">保存</Button>
      </div>
    </FormItem>
    <iframe
      v-if="formData.url"
      :src="formData.url"
      allowfullscreen
      class="h-[700px] w-full"
      frameborder="0"
    ></iframe>
  </div>
</template>

<style></style>
