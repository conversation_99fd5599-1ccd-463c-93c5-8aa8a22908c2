<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { VbenIcon, VbenSpinner } from '@vben/common-ui';
import { Icon } from '@vben/icons';

import {
  Button,
  Checkbox,
  CheckboxGroup,
  Form,
  FormItem,
  Input,
  message,
  Radio,
  RadioGroup,
  Select,
  Table,
  Tooltip,
} from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

// import { findFieldApi, getObjectDetailApi, getWorkListApi } from '#/api';
import {
  editObjApi,
  findFieldApi,
  getObjectDetailApi,
  getWorkListApi,
} from '#/api';
import {
  InputInteger,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const props = defineProps({
  fromState: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['cancelEdit', 'newSave']);

const { isLoading, startLoading, stopLoading } = useLoading();

const route = useRoute();

// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'integer') return InputInteger;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
}

// 对象类型
const object_type = ref('object');

// 对象信息
const formState = ref({
  Object_name: null,
  Object_id: null,
  Object_app_id: {
    id: null,
    name: null,
  },
  Object_type: { id: null, name: null },
  Object_column_id_data_type: { id: null, name: null },
  Object_column_id_Display_Format: null,
  Object_delivery_class: { id: null, name: null },
  Object_orderby: null,
  Object_Description: null,
  OwnerId: null,
  Object_Allow_Reports: null,
  Object_Allow_Search: null,
  Object_Deployment_Status: { id: null, name: null },
  Object_Correlation_Work: null,
  Object_structure_type: { id: null, name: null },
  Object_Original_Language: { id: null, name: null },
  Object_ICO: { id: null, name: null },
});
// 获取对象详情
async function getObjectDetail() {
  startLoading();
  const { data } = await getObjectDetailApi(route.params.id || '');
  formState.value = data;
  stopLoading();
}
const formRef = ref();
const rules = {
  Object_id: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (!value) {
          throw new Error('请输入对象标签API!');
        }
        const regex = /^[A-Z]\w*$/i;
        if (!regex.test(value)) {
          throw new Error('请输入以字母为开头的字符!');
        }
      },
      trigger: 'blur',
    },
  ],
  Object_name: [
    { required: true, message: '请输入对象名称!', trigger: 'blur' },
  ],
  Object_orderby: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
  Object_type: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (!value) {
          throw new Error('请选择对象类别!');
        }
        if (typeof value === 'string' && value.trim() === '') {
          throw new Error('请选择对象类别!');
        }
      },
      trigger: 'blur',
    },
  ],
  Object_app_id: [
    {
      required: true,
      validator: async (_rule, value) => {
        // 检查是否为空
        if (!value) {
          throw new Error('请选择应用包!');
        }
        if (typeof value.name === 'string' && value.name.trim() === '') {
          throw new Error('请选择应用包!');
        }
      },
      trigger: 'blur',
    },
  ],
};

// 获取作业列表
const work_list = ref([]);
const getWorks = async () => {
  const { data } = await getWorkListApi();
  work_list.value =
    data && data.length > 0 ? data.filter((item) => item.id) : [];
};

// ====================================允许登陆对象
const checksList = [
  {
    label: '允许报表',
    value: 'Object_Allow_Reports',
  },
  {
    label: '允许搜索',
    value: 'Object_Allow_Search',
  },
  {
    label: '允许登录对象',
    value: 'Object_allow_user_login',
    tip: '( 勾选后需发布该对象 )',
  },
];

// 定义 CheckboxGroup 的绑定值
const checksValue = computed({
  get() {
    // 将 formData 中的布尔值转换为数组
    return checksList
      .filter((item) => formState.value[item.value])
      .map((item) => item.value);
  },
  set(newValues) {
    // 将数组值同步回 formState
    checksList.forEach((item) => {
      formState.value[item.value] = newValues.includes(item.value);
    });
  },
});
const Field_Related_To_Display_Field_options = ref([]);
// 获取关联字段内容
const findFieldList = async () => {
  const { data } = await findFieldApi(route.params.id);
  Field_Related_To_Display_Field_options.value = data.map((item) => {
    return {
      value: item.key,
      label: item.name,
      id: item.id,
    };
  });
};
// 外部用户登录角色
const External_user_login_role = ref([
  {
    id: null,
    name: null,
  },
]);
// 外部用户登录ID
const External_system_login_ID = ref([
  {
    id: null,
    name: null,
  },
]);
// 表格
const defaultValue = ref(null);
const columns = [
  {
    title: '编号',
    dataIndex: 'a',
    key: 'a',
    align: 'center',
    width: 50,
  },
  {
    title: '字段',
    dataIndex: 'b',
    key: 'b',
    align: 'center',
  },
  {
    title: '是否启用默认值',
    dataIndex: 'c',
    key: 'c',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'edit',
    key: 'edit',
    align: 'center',
    width: 50,
  },
];
const dataSource = ref([
  {
    b: {
      id: null,
      name: null,
      value: null,
    },
  },
]);

function addItem() {
  dataSource.value.push({
    b: { id: null, name: null, value: null },
  });
}

// 下拉选项改变事件
const changeField = (selectedValue, i) => {
  const selectedOption = Field_Related_To_Display_Field_options.value.find(
    (option) => option.value === selectedValue,
  );
  if (selectedOption) {
    dataSource.value[i].b.id = selectedOption.id;
    dataSource.value[i].b.name = selectedOption.label;
    dataSource.value[i].b.value = selectedOption.value;
  }
};
const changeLoginRole = (selectedValue) => {
  const selectedOption = Field_Related_To_Display_Field_options.value.find(
    (option) => option.value === selectedValue,
  );
  if (selectedOption) {
    External_user_login_role.value[0].name = selectedOption.value;
    External_user_login_role.value[0].id = selectedOption.id;
  }
};
const changeLoginId = (selectedValue) => {
  const selectedOption = Field_Related_To_Display_Field_options.value.find(
    (option) => option.value === selectedValue,
  );
  if (selectedOption) {
    External_system_login_ID.value[0].name = selectedOption.value;
    External_system_login_ID.value[0].id = selectedOption.id;
  }
};

const delField = (i) => {
  if (i === defaultValue.value) {
    defaultValue.value = null;
  }
  dataSource.value.splice(i, 1);
};
// 是否从新建页面跳转
const newTo = ref(false);
// 保存
function containsChinese(str) {
  const pattern = /[\u4E00-\u9FA5]/; // Unicode 范围：中文字符的起始编码到结束编码
  return pattern.test(str);
}
const saveData = async () => {
  formRef.value
    .validateFields() // 触发校验
    .then(async () => {
      let toData = cloneDeep(formState.value);
      for (const key in toData) {
        if (toData[key] !== null && typeof toData[key] === 'object') {
          toData[key] = containsChinese(toData[key].name)
            ? toData[key].id
            : toData[key].name;
        }
      }
      // 处理字段表格
      const data = {
        External_login_reference_field: {
          quoteField: {
            fieldList: [],
          },
        },
        External_login_role_id: {
          external_customer_role: {},
        },
        External_login_system_id: {},
        Object_allow_user_login: checksValue.value.includes(
          'Object_allow_user_login',
        )
          ? 1
          : 0,
      };
      dataSource.value.forEach((item, index) => {
        if (item.b.value) {
          if (index === defaultValue.value) {
            const obj = {
              order_number: index + 1,
              field_id: item.b.value,
              is_actived: true,
              is_default: true,
            };
            data.External_login_reference_field.quoteField.fieldList.push(obj);
          } else {
            const obj = {
              order_number: index + 1,
              field_id: item.b.value,
              is_actived: true,
              is_default: false,
            };
            data.External_login_reference_field.quoteField.fieldList.push(obj);
          }
        }
      });
      External_user_login_role.value.forEach((item) => {
        if (item.name) {
          const obj = {
            id: item.id,
            field_id: item.name,
          };
          data.External_login_role_id.external_customer_role = obj;
        }
      });

      External_system_login_ID.value.forEach((item) => {
        if (item.name) {
          const obj = {
            id: item.id,
            external_system_id: item.name,
          };
          data.External_login_system_id = obj;
        }
      });

      data.External_login_reference_field = JSON.stringify(
        data.External_login_reference_field,
      );
      data.External_login_role_id = JSON.stringify(data.External_login_role_id);
      data.External_login_system_id = JSON.stringify(
        data.External_login_system_id,
      );
      toData = Object.assign(toData, data);

      await editObjApi(toData);
      message.success('保存成功');
      const emitData = {
        objectType: toData.Object_type,
        newTo: newTo.value,
      };
      emits('newSave', emitData);
      emits('cancelEdit');
    })
    .catch(() => {});
};

const formList = ref([]);
async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入对象名称',
      value: formState.value.Object_name,
      title: '对象名称',
      field_api: 'Object_name',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入以字母数字下划线组成的字符',
      value: formState.value.Object_id,
      title: '标签API',
      field_api: 'Object_id',
      sort: 2,
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择对象类别',
      value: formState.value.Object_type.id,
      title: '对象类别',
      field_api: 'Object_type',
      key: 'Object_type',
      sort: 5,
    },
    {
      type: 'integer',
      field_readonly: false,
      Field_Help_Text: '请输入序号',
      value: formState.value.Object_orderby,
      title: '对象排序',
      field_api: 'Object_orderby',
      sort: 10,
    },
    {
      type: 'Lookup_Relationship',
      field_readonly: false,
      Field_Help_Text: '请选择应用包',
      value: formState.value.Object_app_id,
      title: '所属应用包',
      field_api: 'Object_app_id',
      sort: 15,
      key: 'Object_app_id',
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择对象结构类型',
      value: formState.value.Object_structure_type.id,
      title: '对象结构类型',
      field_api: 'Object_structure_type',
      key: 'Object_structure_type',
      clear: true,
      sort: 20,
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择初始语言',
      value: formState.value.Object_Original_Language.id,
      title: '初始语言',
      field_api: 'Object_Original_Language',
      key: 'Object_Original_Language',
      clear: true,
      sort: 25,
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择图标',
      value: formState.value.Object_ICO.id,
      title: '图标',
      field_api: 'Object_ICO',
      key: 'Object_ICO',
      clear: true,
      sort: 26,
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择主键类型',
      value: formState.value.Object_column_id_data_type.id,
      title: '主键类型',
      field_api: 'Object_column_id_data_type',
      key: 'Object_column_id_data_type',
      clear: true,
      sort: 27,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入主键显示格式',
      value: formState.value.Object_column_id_Display_Format,
      title: '主键显示格式',
      field_api: 'Object_column_id_Display_Format',
      sort: 28,
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择交互类型',
      value: formState.value.Object_delivery_class.id,
      title: '交互类型',
      field_api: 'Object_delivery_class',
      key: 'Object_delivery_class',
      clear: true,
      sort: 29,
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择部署状态',
      value: formState.value.Object_Deployment_Status.id,
      title: '部署状态',
      field_api: 'Object_Deployment_Status',
      key: 'Object_Deployment_Status',
      clear: true,
      sort: 30,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: formState.value.Object_Description,
      title: '描述',
      Field_Help_Text: '请输入...',
      field_api: 'Object_Description',
      sort: 32,
    },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
}
const modelForm = computed(() => {
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
const isFirstLoad = ref(true);
watch(
  () => formList.value,
  (newData) => {
    if (isFirstLoad.value === true) {
      isFirstLoad.value = false; // 更新标志变量以表示已初始化
      return; // 初次加载时不执行任何操作
    }
    newData.forEach((item) => {
      formState.value[item.field_api] = item.value;
    });
  },
  { deep: true }, // 深度监听
);

const openMoreIcon = () => {
  window.open('https://icon-sets.iconify.design/solar/page-2.html', '_blank');
};

onMounted(async () => {
  if (route.params?.object) {
    object_type.value = route.params.object;
  }
  if (props?.fromState === 'addobj') {
    newTo.value = true;
  }

  await getObjectDetail();
  await getWorks();
  await createForm();
});
</script>

<template>
  <div>
    <div
      class="card-box flex items-center justify-between p-3 text-[16px] font-bold"
    >
      <div>
        <span>当前编辑对象：</span>
        <span>{{ formState.Object_name }}</span>
        <span class="ml-2 text-sm font-normal">
          (
          单数和复数标签用于选项卡，页面布局和报表中。更改名称或标签务必谨慎，因为可能会影响现有的集成或合并模版。
          )
        </span>
      </div>
      <div>
        <Button class="mr-3" type="primary" @click="saveData()">保存</Button>
        <Button @click="emits('cancelEdit')">取消</Button>
      </div>
    </div>
    <div class="card-box mt-3 p-3">
      <Form
        ref="formRef"
        :label-col="{ span: 6 }"
        :model="modelForm"
        :rules="rules"
        :wrapper-col="{ span: 18 }"
        autocomplete="off"
        name="basic"
      >
        <div class="grid grid-cols-2 items-center gap-3">
          <template v-for="(item, index) in formList" :key="index">
            <FormItem
              v-if="item.field_api !== 'Object_ICO'"
              :label="item.title"
              :name="item.field_api"
              class="m-[2%]"
            >
              <component
                :is="getComponent(item.type)"
                v-model="item.value"
                :object-id="object_type"
                :parameters="item"
                :place-holder="item.Field_Help_Text"
                class="col-span-6 w-[100%]"
                object-name="object"
              />
            </FormItem>
            <!-- 图标选择特殊处理 -->
            <FormItem
              v-else
              :label="item.title"
              :name="item.field_api"
              class="m-[2%]"
            >
              <div class="flex gap-1">
                <Input
                  v-model:value="item.value"
                  placeholder="例如：material-symbols:wb-auto"
                >
                  <template #suffix>
                    <VbenIcon :icon="item.value" class="size-4" />
                  </template>
                </Input>
                <Tooltip>
                  <template #title>前往图标库</template>
                  <Button @click="openMoreIcon()">
                    <VbenIcon class="size-4" icon="ep:right" />
                  </Button>
                </Tooltip>
              </div>
            </FormItem>
          </template>
        </div>
      </Form>
    </div>
    <div
      class="card-box mt-3 flex max-h-[45vh] flex-col gap-2 overflow-y-auto p-3"
    >
      <div>
        <span class="font-bold">关联屏幕界面作业：</span>
        <Select
          v-model:value="formState.Object_Correlation_Work"
          :allow-clear="true"
          :field-names="{ label: 'ProcessAutomationName', value: 'id' }"
          :options="work_list"
          class="w-52"
          placeholder="请选择作业"
        />
      </div>
    </div>
    <div
      class="card-box mt-3 flex max-h-[45vh] flex-col gap-2 overflow-y-auto p-3"
    >
      <div>
        <span class="font-bold">可选功能：</span>
        <CheckboxGroup v-model:value="checksValue" name="checkboxgroup">
          <div
            v-for="(item, index) in checksList"
            :key="index"
            class="mb-2 flex items-center"
          >
            <Checkbox :value="item.value">{{ item.label }}</Checkbox>
            <span v-if="item.tip" class="text-xs text-gray-600/75">{{
              item.tip
            }}</span>
          </div>
        </CheckboxGroup>
      </div>
      <div
        v-if="checksValue.includes('Object_allow_user_login')"
        class="flex flex-col gap-3"
      >
        <div class="flex items-center gap-5">
          <div>
            <span>外部用户登录角色：字段：</span>
            <Select
              v-model:value="External_user_login_role[0].name"
              :allow-clear="true"
              :options="Field_Related_To_Display_Field_options"
              class="w-[200px]"
              placeholder="请选择..."
              @change="changeLoginRole"
              @click="findFieldList"
            />
          </div>
          <div>
            <span>外部系统登录ID：字段：</span>
            <Select
              v-model:value="External_system_login_ID[0].name"
              :allow-clear="true"
              :options="Field_Related_To_Display_Field_options"
              class="w-[200px]"
              placeholder="请选择..."
              @change="changeLoginId"
              @click="findFieldList"
            />
          </div>
        </div>
        <Table
          :columns="columns"
          :data-source="dataSource"
          :pagination="false"
          :scroll="{ y: 126 }"
          bordered
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'a'">
              {{ index + 1 }}
            </template>
            <template v-if="column.dataIndex === 'b'">
              <Select
                v-model:value="record.b.name"
                :options="Field_Related_To_Display_Field_options"
                class="w-full"
                placeholder="请选择条件变量名称"
                @change="changeField($event, index)"
                @click="findFieldList"
              />
            </template>
            <template v-if="column.dataIndex === 'c'">
              <RadioGroup v-model:value="defaultValue">
                <Radio :value="index" style="padding-top: 5px"> 启用 </Radio>
              </RadioGroup>
            </template>

            <template v-if="column.dataIndex === 'edit'">
              <div class="flex-center" @click="delField(index)">
                <Icon class="size-5" icon="carbon:subtract-alt" />
              </div>
            </template>
          </template>
        </Table>
        <div class="flex-center mt-4">
          <Button type="dashed" @click="addItem"> + 新增引用字段</Button>
        </div>
      </div>
    </div>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>

<style lang="scss" scoped>
.ant-form-item {
  margin-bottom: 0;
}
</style>
