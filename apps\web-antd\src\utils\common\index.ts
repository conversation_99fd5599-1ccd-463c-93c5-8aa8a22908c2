function isValidArray(arr: Array<any>, keyName: string) {
  return (
    Array.isArray(arr) &&
    arr.every(
      (item) => typeof item === 'object' && item !== null && keyName in item,
    )
  );
}

/**
 *
 * @param arr 判断的数组
 * @param keyName 判断依据的键名
 * @returns 返回键名重复的数组索引
 */
export function findDuplicateNameIndices(arr: Array<any>, keyName: string) {
  if (!isValidArray(arr, keyName)) {
    return [];
  }
  const nameIndexMap: Record<string, number> = {};
  const duplicateIndices: number[] = [];

  arr.forEach((item, index) => {
    const name = item[keyName];
    if (typeof name === 'string' || typeof name === 'number') {
      if (name in nameIndexMap) {
        const firstIndex = nameIndexMap[name];
        if (
          firstIndex !== undefined &&
          !duplicateIndices.includes(firstIndex)
        ) {
          duplicateIndices.push(firstIndex);
        }
        duplicateIndices.push(index);
      } else {
        nameIndexMap[name as string] = index;
      }
    }
  });

  return duplicateIndices;
}
