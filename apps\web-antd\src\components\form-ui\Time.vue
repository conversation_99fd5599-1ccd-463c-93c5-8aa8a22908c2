<script setup>
import { ref, watch } from 'vue';

import { TimePicker } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const timeValue = ref(null);
timeValue.value = props.modelValue ? dayjs(props.modelValue, 'HH:mm:ss') : null;

watch(timeValue, (newValue) => {
  if (newValue) {
    emits('update:modelValue', newValue);
  } else {
    emits('update:modelValue', null);
  }
});
</script>

<template>
  <TimePicker
    v-model:value="timeValue"
    :disabled="props.parameters.field_readonly"
  />
</template>

<style></style>
