import { requestClient } from '#/api/request';

/**
 * @returns 获取赛批量操作记录数据
 */
export async function getOperateEditApi(data: any) {
  return requestClient.post(`/data_export_records_hand/list`, data);
}

/**
 * @returns 重新计算公式接口
 */

export async function getRecalculateApi(objectName: string) {
  return requestClient.get(`/${objectName}/update_formula_filed`);
}

/**
 * @returns 获取文件
 */
export async function getFileApi(recordID: string) {
  return requestClient.get(`/data_export_records_hand/download/${recordID}`, {
    responseType: 'blob',
  });
}

/**
 * @returns 获取重新计算公式进度
 */
export async function getFormulaFieldStateApi(data: any) {
  return requestClient.post(`/formula_update/list`, data);
}

/**
 * @returns 获取批量作业执行状态
 */
export async function getWorkExecuteApi(data: any) {
  return requestClient.post(`/async_work/list`, data);
}

/**
 * @returns 删除表格记录
 */
export async function deleteTableRecordApi(objectName: string, selectIds: any) {
  return requestClient.delete(`${objectName}/delete`, {
    data: selectIds,
  });
}

/**
 * @returns 批量导出数据
 */

export async function batchExportDataApi(
  objectName: string,
  exportQueryParams: any,
) {
  return requestClient.post(`${objectName}/export`, exportQueryParams, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * @returns 批量导出数据(携带图片)
 */
export async function batchExportDataWithImageApi(
  objectName: string,
  exportQueryParams: any,
) {
  return requestClient.post(`${objectName}/export_image`, exportQueryParams, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * @returns 批量导出数据(带附件)
 */
export async function batchExportDataWithFileApi(
  objectName: string,
  exportQueryParams: any,
) {
  return requestClient.post(`${objectName}/export_file`, exportQueryParams, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * @returns 批量导出数据(带子表)
 */
export async function batchExportDataWithSubTableApi(
  objectName: string,
  exportQueryParams: any,
) {
  return requestClient.post(
    `${objectName}/export_children`,
    exportQueryParams,
    {
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
}

/**
 * @returns 批量修改字段
 */

export async function batchChangeFieldApi(objectName: string, data: any) {
  return requestClient.post(`${objectName}/batch_update`, data);
}

/**
 * @returns 批量还原
 */
export async function batchRestoreApi(objectName: string, data: any) {
  return requestClient.post(`${objectName}/restore`, data);
}
