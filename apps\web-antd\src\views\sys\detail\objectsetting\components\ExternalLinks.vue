<script setup>
import { reactive } from 'vue';
import { useRoute } from 'vue-router';

import { Button, FormItem, Input, message, Select } from 'ant-design-vue';

import { removeTableRelationApi } from '#/api';

const route = useRoute();

const formData = reactive({
  customUrl: null,
  mobileUrl: null,
});

const mobileUrlList = [
  {
    label: 'http:123.com',
    value: 'http:123.com',
  },
];

// 保存自定义页面配置
const saveCustomConfig = async () => {
  const dataParams = {
    id: route.params.object_uid,
    Object_h5_config: JSON.stringify(formData),
  };

  await removeTableRelationApi('object', dataParams);
  message.success('保存成功');
};
</script>

<template>
  <div class="w-full p-2">
    <FormItem label="外部链接地址">
      <div class="flex gap-3">
        <Input v-model:value="formData.customUrl" />
        <Button type="primary">预览</Button>
      </div>
    </FormItem>
    <FormItem label="选择移动端页面">
      <div class="flex gap-3">
        <Select
          v-model:value="formData.mobileUrl"
          :options="mobileUrlList"
          show-search
        />
        <Button type="primary">预览</Button>
      </div>
    </FormItem>
    <div><Button type="primary" @click="saveCustomConfig">保存</Button></div>
  </div>
</template>

<style></style>
