<script setup>
import { computed, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenModal, VbenIcon, VbenSpinner } from '@vben/common-ui';
import { Icon } from '@vben/icons';

import {
  Button,
  DatePicker,
  Form,
  FormItem,
  Input,
  InputNumber,
  InputSearch,
  message,
  Modal,
  Pagination,
  Radio,
  RadioGroup,
  Select,
  Skeleton,
  Table,
  Textarea,
  TimePicker,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import { debounce } from 'lodash-es';

import {
  findCorrelationApi,
  findHeaderApi,
  getObjectListApi,
  getWorkDetailApi,
  postAddWorkApi,
  postCopyWorkApi,
  putModifyWorkApi,
} from '#/api';
import { useLoading } from '#/hook';

import { rules, timesOptions } from './addFunction';

const props = defineProps({
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
  type: {
    type: String,
    default: '',
  },
  workId: {
    type: String,
    default: '',
  },
  fromObj: {
    type: String,
    default: '',
  },
  objectType: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['clearFilter', 'refreshJob']);

const { isLoading, startLoading, stopLoading } = useLoading();

const timesOption = ref([...timesOptions]);
stopLoading();

const router = useRouter();
const route = useRoute();

// 应用包弹窗显示控制
const openPackage = ref(false);
const columns = ref([]);
const dataSource = ref([]);
const settingMeg = reactive({
  page: 1,
  page_size: 10,
  total: 0,
  keywords: null,
});
const workList = ref([
  {
    key: 1,
    name: '记录触发流',
    information: '在创建、更新或删除记录时启动。此自动启动流会在后台运行。',
    icon: 'material-symbols:box-edit',
    checked: false,
    mode: '记录触发',
  },
  {
    key: 2,
    name: '计划触发流',
    information: '在创建、更新或删除记录时启动。此自动启动流会在后台运行。',
    icon: 'vaadin:time-backward',
    checked: false,
    mode: '定时触发',
  },
  {
    key: 3,
    name: '调用触发流',
    information: '在创建、更新或删除记录时启动。此自动启动流会在后台运行。',
    icon: 'fluent:slide-search-28-filled',
    checked: true,
    mode: '调用触发',
  },
  {
    key: 4,
    name: '自动化测试触发流',
    information: '在自动化测试任务中被调用触发。',
    icon: 'material-symbols:automation-outline',
    checked: false,
    mode: '自动化测试触发',
  },
]);
const currentWorkType = ref('调用触发');
let formState = reactive({
  ProcessAutomationActive: 'true',
  ProcessAutomationApp: null,
  ProcessAutomationDescription: '',
  ProcessAutomationFrequency: '',
  ProcessAutomationGenerateError: '',
  ProcessAutomationIsToken: 'true',
  ProcessAutomationMethod: 'get',
  ProcessAutomationName: null,
  ProcessAutomationObject: null,
  ProcessAutomationStartDate: dayjs().format('YYYY-MM-DD'),
  ProcessAutomationStartTime: dayjs().format('HH:mm:ss'),
  ProcessAutomationTriggerMode: '',
  ProcessAutomationUrl: '',
  ProcessAutomation_timeout_duration: '15.0',
  id: '',
  is_script: 'false',
  is_timeout: 'false',
  refuse_duplicate_submit: 'false',
});
const tableDataOption = ref([]);
const allData = ref([]);
const objListOption = ref([]);

const oRules = ref({ ...rules });
const formRef = ref(null);
const loading = ref(false);
const appName = ref(null);
const settingMegLoading = ref(false);
const state = reactive({
  selectedRowKeys: [],
});
const workName = ref('');

const [mainModal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[900px]',
  contentClass: 'p-4',
  appendToMain: true,
  confirmText: props.type,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
    initFormState();
  },
  onConfirm() {
    formState.ProcessAutomationTriggerMode = currentWorkType.value;
    formRef.value
      .validateFields()
      .then(() => {
        workName.value = formState.ProcessAutomationName;
        createWork();
        setTimeout(() => {
          emits('clearFilter');
        });
      })
      .catch(() => {
        message.warn('请填写带 * 的字段');
      });
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      getObjList();
      if (props.type !== '添加') {
        changeWorkDefault();
      }
    }
  },
  onBeforeClose() {
    initFormState();
  },
});

const rowSelection = computed(() => {
  return {
    type: 'radio',
    selectedRowKeys: state.selectedRowKeys,
    onChange: (selectedRowKeys) => {
      state.selectedRowKeys = selectedRowKeys;
    },
    onSelect: (record) => {
      formState.ProcessAutomationApp = record.id;
      appName.value = record.name;
      openPackage.value = false;
    },
  };
});

// 重置表单
function initFormState() {
  if (props.type === '添加') {
    appName.value = null;
    formState = reactive({
      ProcessAutomationActive: 'true',
      ProcessAutomationApp: null,
      ProcessAutomationDescription: '',
      ProcessAutomationFrequency: '1',
      ProcessAutomationGenerateError: '',
      ProcessAutomationIsToken: 'true',
      ProcessAutomationMethod: 'get',
      ProcessAutomationName: null,
      ProcessAutomationObject: null,
      ProcessAutomationStartDate: dayjs().format('YYYY-MM-DD'),
      ProcessAutomationStartTime: dayjs().format('HH:mm:ss'),
      ProcessAutomationTriggerMode: '',
      ProcessAutomationUrl: '',
      ProcessAutomation_timeout_duration: '15.0',
      id: '',
      is_script: 'false',
      is_timeout: 'false',
      refuse_duplicate_submit: 'false',
    });
  }
}

// 新建/修改任务
function createWork() {
  const data = formState;

  switch (data.ProcessAutomationTriggerMode) {
    case '记录触发': {
      data.ProcessAutomationStartDate = dayjs().format('YYYY-MM-DD');
      data.ProcessAutomationStartTime = dayjs().format('HH:mm:ss');
      data.ProcessAutomationFrequency = '';
      data.ProcessAutomationMethod = '';
      data.ProcessAutomationUrl = '';
      break;
    }
    case '调用触发': {
      data.ProcessAutomationStartDate = dayjs().format('YYYY-MM-DD');
      data.ProcessAutomationStartTime = dayjs().format('HH:mm:ss');
      data.ProcessAutomationFrequency = '';
      data.ProcessAutomationObject = '';
      break;
    }
    case '定时触发': {
      data.ProcessAutomationMethod = '';
      data.ProcessAutomationUrl = '';
      data.ProcessAutomationObject = '';
      break;
    }
  }
  if (props.type === '添加') {
    postAddWorkApi(data).then((res) => {
      startLoading();
      message.success('创建成功');
      const objId = ref(route.meta.objectId);

      if (props.fromObj) {
        objId.value = props.objectId;
      }
      router.push({
        name: 'taskdetail',
        params: {
          id: res.data.processAutomation_id,
          object_uid: objId.value || '',
        },
      });
      stopLoading();
    });
  } else {
    // 需要将formState按作业类型存值
    data.id = props.workId;
    if (props.type === '修改') {
      putModifyWorkApi(props.workId, data).then(() => {
        message.success('修改成功');
        emits('refreshJob');
      });
    } else if (props.type === '复制') {
      postCopyWorkApi(data).then((res) => {
        message.success('复制成功');
        router.push({
          name: 'taskdetail',
          params: {
            id: res.data.work_id,
            object_uid: route.params.object_uid || '',
          },
        });
      });
    }
  }
  initFormState();
  modalApi.close();
}

function checkedWork(item, index) {
  currentWorkType.value = item.mode;
  workList.value.forEach((item) => {
    item.checked = false;
  });
  workList.value[index].checked = true;
  initFormState();
}

// 获取应用包数据
async function getTableData() {
  settingMegLoading.value = true;
  const arr = [];
  const { data } = await findCorrelationApi('object', 'Object_app_id', {
    page: settingMeg.page,
    page_size: settingMeg.page_size,
    object_id: 'Object',
    field: 'Object_app_id',
  });
  settingMeg.total = data.total;
  data.data.forEach((item) => {
    const obj = {};

    columns.value.forEach((column) => {
      obj[column.key] = '';
    });

    // 然后为 obj 设置具体的数据
    obj.name = item.name;
    obj.app_ICO = item.app_ICO.id;
    obj.app_IsActive = item.app_IsActive ? '是' : '否';
    obj.app_orderby = item.app_orderby;
    obj.app_id = item.app_id;
    obj.id = item.id;
    obj.key = item.id;
    arr.push(obj);
  });
  dataSource.value = arr;
  settingMegLoading.value = false;
}

// 获取并处理对象列表
async function getObjList() {
  const { data } = await getObjectListApi();
  data.items.shift();
  objListOption.value = data.items.map((input) => ({
    label: input.name,
    options: input.objects.map((item) => ({
      value: item.id,
      label: item.name,
    })),
  }));

  columns.value = [];
  dataSource.value = [];
  allData.value = [];
  const fieldType = ref('ProcessAutomationApp');
  const objId = ref(props.objectId);
  if (props.fromObj) {
    fieldType.value = props.fromObj;
    objId.value = props.objectType;
  }
  const { data: data1 } = await findHeaderApi(objId.value, fieldType.value);
  data1.forEach((item, index) => {
    if (index !== 0) {
      columns.value.push({
        title: item.name,
        dataIndex: item.key,
        key: item.key,
      });
    }
  });
}

// 分页
const changeTableData = (e) => {
  loading.value = true;
  settingMeg.page = e;
  getTableData();
  loading.value = false;
};

// 搜索应用包
const handleSearch = debounce(async (value) => {
  tableDataOption.value = [];
  if (!value) return;
  settingMeg.keywords = value;
  settingMeg.page = 1;
  settingMeg.page_size = 10;

  const { data } = await findCorrelationApi(
    'object',
    'Object_app_id',
    settingMeg,
  );
  tableDataOption.value = data.data.map((item) => {
    return {
      label: item.name,
      value: item.name,
      id: item.id,
    };
  });
}, 500);

const changeValue = (value, option) => {
  formState.ProcessAutomationApp = option.id;
};

// 打开选择应用包弹窗
const openAutomationApp = () => {
  openPackage.value = true;
  tableDataOption.value = allData.value.map((item) => ({
    value: item.name,
    label: item.name,
    id: item.id,
  }));
  getTableData();
};

// 表格搜索
async function onSearch(value) {
  if (!value) {
    settingMeg.keywords = null;
  }
  if (value) {
    settingMeg.keywords = value;
  }
  settingMeg.page = 1;
  settingMeg.page_size = 10;
  loading.value = true;
  const { data } = await findCorrelationApi(
    'object',
    'Object_app_id',
    settingMeg,
  );
  dataSource.value = data.data.map((item) => {
    return {
      ...item,
      key: item.id,
    };
  });

  settingMeg.total = data.total;
  loading.value = false;
}

// 表格点击
const customRow = (record) => {
  return {
    onClick: () => {
      formState.ProcessAutomationApp = record.id;
      appName.value = record.name;
      state.selectedRowKeys = [record.id];
      openPackage.value = false;
    },
  };
};

// 修改作业默认值
async function changeWorkDefault() {
  const { data } = await getWorkDetailApi(props.workId);
  currentWorkType.value = data.ProcessAutomationTriggerMode.id;

  workList.value.forEach((item) => {
    item.checked = item.mode.includes(data.ProcessAutomationTriggerMode.id);
  });
  formState.ProcessAutomationName =
    props.type === '复制'
      ? `${data.ProcessAutomationName}-副本`
      : data.ProcessAutomationName;
  formState.ProcessAutomationApp = data.ProcessAutomationApp.id;
  appName.value = data.ProcessAutomationApp.name;
  formState.ProcessAutomationActive = `${data.ProcessAutomationActive}`;
  formState.ProcessAutomationIsToken = `${data.ProcessAutomationIsToken}`;
  formState.ProcessAutomation_timeout_duration =
    data.ProcessAutomation_timeout_duration;
  formState.is_timeout = `${data.is_timeout}`;
  formState.is_script = `${data.is_script}`;
  formState.refuse_duplicate_submit = `${data.refuse_duplicate_submit}`;
  formState.ProcessAutomationDescription = `${data.ProcessAutomationDescription}`;
  formState.ProcessAutomationObject = `${data.ProcessAutomationObject.id}`;
  formState.ProcessAutomationUrl = `${data.ProcessAutomationUrl}`;
  formState.ProcessAutomationMethod = data.ProcessAutomationMethod.name
    ? `${data.ProcessAutomationMethod.name}`
    : 'get';
  formState.ProcessAutomationStartDate = `${data.ProcessAutomationStartDate}`;
  formState.ProcessAutomationStartTime = `${data.ProcessAutomationStartTime}`;
  formState.ProcessAutomationFrequency =
    data.ProcessAutomationFrequency.id || '1';

  state.selectedRowKeys = data.ProcessAutomationApp.id;
  rowSelection.value.selectedRowKeys = data.ProcessAutomationApp.id;
}

function filterOption(inputValue, option) {
  return option.label.includes(inputValue);
}
</script>

<template>
  <div>
    <mainModal :title="`${props.type}作业`">
      <div class="flex flex-col gap-3 p-2">
        <span class="font-bold">作业类型</span>
        <div class="grid grid-cols-2 gap-4">
          <div
            v-for="(item, index) in workList"
            :key="item.key"
            :class="{ 'border-theme-color': item.checked }"
            class="hover:border-theme-color relative flex h-[100px] gap-2 overflow-hidden rounded-[5px] border-2 p-2 shadow-md"
            @click="checkedWork(item, index)"
          >
            <div class="icon flex-center w-[80px] border-r">
              <Icon :icon="item.icon" class="size-[38px]" />
            </div>
            <div class="message flex flex-1 flex-col justify-center gap-2">
              <span class="font-bold">{{ item.name }}</span>
              <span class="text-[14px] opacity-[0.6]">{{
                item.information
              }}</span>
            </div>
            <!-- 右上角标记 -->
            <div
              v-show="item.checked"
              class="bg-theme-color absolute right-[-28px] top-[-40px] h-[100px] w-[48px] rotate-[130deg]"
            ></div>
            <Icon
              v-show="item.checked"
              class="absolute right-[2px] top-[2px] size-5 text-[#fff]"
              icon="material-symbols:bookmark-sharp"
            />
          </div>
        </div>

        <span class="font-bold">作业设置</span>
        <Form
          ref="formRef"
          :label-col="{ span: 6 }"
          :model="formState"
          :rules="oRules"
          :validate-trigger="['blur', 'change']"
          :wrapper-col="{ span: 18 }"
          autocomplete="off"
          name="basic"
        >
          <div class="grid grid-cols-2">
            <FormItem label="作业名称" name="ProcessAutomationName">
              <Input
                v-model:value="formState.ProcessAutomationName"
                placeholder="请输入作业名称"
              />
            </FormItem>

            <FormItem label="所属应用包" name="ProcessAutomationApp">
              <div class="flex gap-2">
                <Select
                  v-model:value="appName"
                  :default-active-first-option="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :options="tableDataOption"
                  :show-arrow="false"
                  placeholder="请点击右侧按钮选择"
                  show-search
                  @blur="() => (settingMeg.keywords = null)"
                  @change="changeValue"
                  @search="handleSearch"
                />
                <Button @click="openAutomationApp">
                  <VbenIcon class="size-4" icon="ep:right" />
                </Button>
              </div>
            </FormItem>
            <FormItem label="是否启用" name="ProcessAutomationActive">
              <RadioGroup
                v-model:value="formState.ProcessAutomationActive"
                name="radioGroup"
              >
                <Radio value="true">启用</Radio>
                <Radio value="false">禁用</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="是否令牌验证" name="ProcessAutomationIsToken">
              <RadioGroup
                v-model:value="formState.ProcessAutomationIsToken"
                name="radioGroup"
              >
                <Radio value="false">验证</Radio>
                <Radio value="true">跳过</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              label="超时时长"
              name="ProcessAutomation_timeout_duration"
            >
              <InputNumber
                v-model:value="formState.ProcessAutomation_timeout_duration"
              />
            </FormItem>
            <FormItem label="是否超时">
              <RadioGroup
                v-model:value="formState.is_timeout"
                disabled
                name="radioGroup"
              >
                <Radio value="true">是</Radio>
                <Radio value="false">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="后台执行" name="is_script">
              <RadioGroup v-model:value="formState.is_script" name="radioGroup">
                <Radio value="true">开启</Radio>
                <Radio value="false">关闭</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem label="防止重复提交" name="refuse_duplicate_submit">
              <RadioGroup
                v-model:value="formState.refuse_duplicate_submit"
                name="radioGroup"
              >
                <Radio value="true">是</Radio>
                <Radio value="false">否</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              :label-col="{ span: 3 }"
              :wrapper-col="{ span: 21 }"
              class="col-span-2"
              label="作业描述"
            >
              <Textarea
                v-model:value="formState.ProcessAutomationDescription"
                :rows="2"
                placeholder="请输入作业描述"
              />
            </FormItem>

            <!-- 记录触发 -->
            <FormItem
              v-if="currentWorkType.includes('记录触发')"
              label="选择对象"
              name="ProcessAutomationObject"
            >
              <Select
                v-model:value="formState.ProcessAutomationObject"
                :filter-option="filterOption"
                :options="objListOption"
                placeholder="搜索对象"
                show-search
              />
            </FormItem>

            <!-- 调用触发 || 自动化测试触发-->
            <FormItem
              v-if="
                currentWorkType.includes('调用触发') ||
                currentWorkType.includes('自动化测试触发')
              "
              label="请求方法"
              name="ProcessAutomationMethod"
            >
              <RadioGroup
                v-model:value="formState.ProcessAutomationMethod"
                name="radioGroup"
              >
                <Radio value="get">GET</Radio>
                <Radio value="post">POST</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              v-if="
                currentWorkType.includes('调用触发') ||
                currentWorkType.includes('自动化测试触发')
              "
              label="请求地址"
              name="ProcessAutomationUrl"
            >
              <Input
                v-model:value="formState.ProcessAutomationUrl"
                placeholder="请输入作业请求地址"
              />
            </FormItem>

            <!-- 计划触发 -->
            <FormItem
              v-if="currentWorkType.includes('定时触发')"
              label="开始日期"
              name="ProcessAutomationStartDate"
            >
              <DatePicker
                v-model:value="formState.ProcessAutomationStartDate"
                value-format="YYYY-MM-DD"
              />
            </FormItem>
            <FormItem
              v-if="currentWorkType.includes('定时触发')"
              label="开始时间"
              name="ProcessAutomationStartTime"
            >
              <TimePicker
                v-model:value="formState.ProcessAutomationStartTime"
                value-format="HH:mm:ss"
              />
            </FormItem>
            <FormItem
              v-if="currentWorkType.includes('定时触发')"
              label="频率"
              name="ProcessAutomationFrequency"
            >
              <Select
                v-model:value="formState.ProcessAutomationFrequency"
                :options="timesOption"
                show-search
              />
            </FormItem>
          </div>
        </Form>
      </div>
    </mainModal>
    <Modal
      v-model:open="openPackage"
      :footer="null"
      title="选择应用包"
      width="1000px"
    >
      <InputSearch
        v-model:value="settingMeg.keywords"
        allow-clear
        class="mb-4 w-[30%]"
        enter-button
        placeholder="输入关键字"
        @search="onSearch"
      />
      <Skeleton :loading="settingMegLoading" active>
        <Table
          :columns="columns"
          :custom-row="customRow"
          :data-source="dataSource"
          :loading="loading"
          :pagination="false"
          :row-selection="rowSelection"
          size="small"
        >
          <template #footer>
            <div class="text-right">
              <Pagination
                v-model:current="settingMeg.page"
                v-model:page-size="settingMeg.page_size"
                :show-total="(total) => `共 ${total} 条`"
                :total="settingMeg.total"
                show-size-changer
                size="small"
                @change="changeTableData"
              />
            </div>
          </template>
        </Table>
      </Skeleton>
    </Modal>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>

<style lang="scss" scoped></style>
