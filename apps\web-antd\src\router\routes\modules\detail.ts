import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layout-dashboard',
      order: 100,
      title: '详细',
      hideInMenu: true,
    },
    name: 'Detail',
    path: '/detail',
    children: [
      {
        name: 'knowledge',
        path: '/detail/knowledge/:id',
        component: () =>
          import('#/views/sys/detail/knowledge/knowledgeDetail.vue'),
        meta: {
          icon: 'lucide:layout-dashboard',
          keepAlive: true,
          title: '知识库详情',
        },
      },
      {
        name: 'report',
        path: '/detail/report/',
        component: () =>
          import('#/views/sys/detail/reportdetail/ReportDetail.vue'),
        meta: {
          icon: 'lucide:layout-dashboard',
          keepAlive: true,
          title: '报表显示',
        },
      },
      {
        name: 'aireport',
        path: '/detail/aireport/',
        component: () =>
          import('#/views/sys/detail/reportdetail/AIReportDetail.vue'),
        meta: {
          icon: 'lucide:layout-dashboard',
          keepAlive: true,
          title: 'AI报表编辑',
        },
      },
      {
        name: 'reportEdit',
        path: '/edit/report/',
        component: () =>
          import('#/views/sys/detail/reportdetail/ReportEdit.vue'),
        meta: {
          icon: 'lucide:layout-dashboard',
          keepAlive: true,
          title: '报表编辑',
        },
      },
      {
        name: 'objectsetting',
        path: '/detail/objectsetting/:object/:id/:object_uid/:objectType?/:state?',
        component: () => import('#/views/sys/detail/objectsetting/index.vue'),
        meta: {
          icon: 'tdesign:setting',
          keepAlive: true,
          title: '对象设置',
        },
      },
      {
        name: 'perpheralinterface',
        path: '/detail/interface/:id/:name',
        component: () =>
          import('#/views/sys/detail/perpheralinterface/index.vue'),
        meta: {
          icon: 'pajamas:api',
          keepAlive: true,
          title: '外部接口详情',
        },
      },
      {
        name: 'taskdetail',
        path: '/detail/taskdetail/:id/:object_uid',
        component: () => import('#/views/sys/detail/task/index.vue'),
        meta: {
          icon: 'mingcute:task-2-line',
          keepAlive: true,
          title: '任务详情',
        },
      },
      {
        name: 'SelectEdit',
        path: 'SelectEdit/:id',
        component: () => import('#/views/sys/detail/globaloptions/index.vue'),
        meta: {
          icon: 'ph:pencil-fill',
          keepAlive: true,
          title: '选项列表编辑',
        },
      },
      {
        name: 'aiSetting',
        path: 'aiSetting/:id',
        component: () => import('#/views/sys/detail/aiinterface/index.vue'),
        meta: {
          icon: 'ri:ai-generate-2',
          keepAlive: true,
          title: 'AI服务详情',
        },
      },
      {
        name: 'searchSetting',
        path: '/searchSetting/:object/:name/:keyword',
        component: () => import('#/views/sys/header/SearchIndex.vue'),
        meta: {
          icon: 'ic:round-search',
          keepAlive: true,
          title: '全局搜索',
          maxNumOfOpenTab: 1,
        },
      },
      // ...其他详细模块
    ],
  },
];

export default routes;
