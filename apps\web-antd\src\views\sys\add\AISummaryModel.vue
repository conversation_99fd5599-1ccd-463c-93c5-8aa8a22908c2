<script setup>
import { ref } from 'vue';

import { useVbenModal, VbenIcon } from '@vben/common-ui';

import { fetchEventSource } from '@microsoft/fetch-event-source';
import { RadioButton, RadioGroup, Select, Tooltip } from 'ant-design-vue';
import dayjs from 'dayjs';

import { getTableDataApi } from '#/api';

import { useAccessStore } from '../../../../../../packages/stores/src/modules/access';

defineOptions({ name: 'AXUseXChatBasicSetup' });

const props = defineProps({
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
});

const accessStore = useAccessStore();

const fetchCancel = ref(null);
const loading = ref(false);
const isStreamLoad = ref(false);

const chatRef = ref(null);
// 倒序渲染
const chatList = ref([]);
const startRender = ref(false);
const modelList = ref([]);
const chooseModel = ref('');
const dateValue = ref('近七天');
const renderContent = ref('对数据进行总结');
// 获取大模型类型
async function getModelList() {
  const { data } = await getTableDataApi('ai_engine');
  modelList.value = data.data.map((item) => {
    return item.engine_name === 'deepseek-r1'
      ? {
          label: `${item.engine_name}(深度思考)`,
          value: item.engine_key_name.id,
        }
      : {
          label: item.engine_name,
          value: item.engine_key_name.id,
        };
  });

  chooseModel.value = modelList.value[0].value;
}

// 清空消息
const clearConfirm = function () {
  chatList.value = [];
};

const onStop = function () {
  if (fetchCancel.value) {
    fetchCancel.value.controller.close();
    loading.value = false;
    isStreamLoad.value = false;
  }
};

const inputEnter = function (inputValue) {
  if (isStreamLoad.value) {
    return;
  }
  if (!inputValue) return;
  const params = {
    avatar: 'https://tdesign.gtimg.com/site/avatar.jpg',
    name: '自己',
    datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    content: inputValue,
    role: 'user',
  };
  chatList.value.unshift(params);
  // 空消息占位
  const params2 = {
    avatar: 'https://tdesign.gtimg.com/site/chat-avatar.png',
    name: 'AI助手',
    datetime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    content: '',
    role: 'assistant',
  };
  chatList.value.unshift(params2);
  handleData(inputValue);
};

async function handleData(inputValue) {
  loading.value = true;
  isStreamLoad.value = true;
  startRender.value = true;
  renderContent.value = '';
  const AIparams = {
    object_id: props.objectName,
    input_content: inputValue,
    ai_mode: chooseModel.value,
    datetime_range: dateValue.value,
  };

  const ctrl = new AbortController();
  let typeInterval; // 声明在外部
  const bufferText = ref('');

  await fetchEventSource('/basic-api/ai/auto_ai_agent', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${accessStore.accessToken}`,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      Connection: 'keep-alive',
      Pragma: 'no-cache',
    },
    body: JSON.stringify(AIparams),
    openWhenHidden: true,
    reconnect: false,
    signal: ctrl.signal,
    async onmessage(event) {
      // 将新数据添加到缓冲区
      bufferText.value += event.data;

      if (typeInterval) clearInterval(typeInterval);

      // 启动打字效果
      typeInterval = setInterval(() => {
        if (bufferText.value.length > 0) {
          const char = bufferText.value[0];
          bufferText.value = bufferText.value.slice(1);

          chatList.value[0].content += char;
        } else {
          clearInterval(typeInterval);
        }
      }, 30);

      isStreamLoad.value = false;
      loading.value = false;
    },
    async onerror() {
      chatList.value[0].role = 'error';
      chatList.value[0].content = '系统繁忙请重试';
      isStreamLoad.value = false;
      loading.value = false;
    },
  });
}

const [Modal] = useVbenModal({
  class: 'w-max-[1000px] w-[60vw]',
  contentClass: 'p-8',
  closeOnClickModal: false,
  footer: false,
  onOpenChange(isOpen) {
    if (isOpen) {
      getModelList();
    }
  },
});
</script>

<template>
  <Modal>
    <template #title>
      <div class="flex gap-2">
        <span>AI智能总结</span>
      </div>
    </template>
    <div class="chat-box">
      <t-chat
        ref="chatRef"
        :data="chatList"
        :is-stream-load="isStreamLoad"
        clear-history
        style="height: 600px; max-height: 600px"
        @clear="clearConfirm"
        @scroll="handleChatScroll"
      >
        <!-- eslint-disable vue/no-unused-vars -->
        <template #content="{ item, index }">
          <t-chat-loading
            v-if="!item.content && item.role === 'assistant'"
            animation="moving"
            class="ml-[10px]"
            text="思考中..."
          />
          <div
            v-else-if="item.content && item.role === 'assistant'"
            style="display: flex; align-items: center; margin-left: 10px"
          >
            <VbenIcon
              class="size-5 text-[green]"
              icon="ant-design:check-circle-outlined"
            />
            <span>思考完成</span>
          </div>
          <t-chat-content :content="item.content" />
        </template>
        <template #actions="{ item, index }">
          <t-chat-action
            :content="item.content"
            :operation-btn="['copy']"
            @operation="handleOperation"
          />
        </template>
        <template #footer>
          <t-chat-sender
            v-model="renderContent"
            :stop-disabled="isStreamLoad"
            :textarea-props="{
              placeholder: '请输入消息...',
            }"
            @send="inputEnter"
            @stop="onStop"
          >
            <template #prefix>
              <div class="model-select">
                <Tooltip
                  v-model:visible="allowToolTip"
                  content="切换模型"
                  trigger="hover"
                >
                  <Select
                    v-model:value="chooseModel"
                    :options="modelList"
                    class="absolute bottom-[10px] w-[200px]"
                  />
                </Tooltip>
              </div>
              <RadioGroup v-model:value="dateValue" button-style="solid">
                <RadioButton value="近七天">近七天</RadioButton>
                <RadioButton value="近两周">近两周</RadioButton>
                <RadioButton value="近一个月">近一个月</RadioButton>
                <RadioButton value="近一个季度">近一个季度</RadioButton>
                <RadioButton value="近半年">近半年</RadioButton>
                <RadioButton value="近一年">近一年</RadioButton>
              </RadioGroup>
            </template>
          </t-chat-sender>
        </template>
      </t-chat>
    </div>
  </Modal>
</template>

<style lang="scss" scoped></style>
