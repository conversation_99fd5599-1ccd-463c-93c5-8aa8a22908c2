<script setup>
import { computed, h, ref, unref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Table } from 'ant-design-vue';

import { getTableColumnApi } from '#/api';

import 'vue3-colorpicker/style.css';

const props = defineProps({
  choseData: {
    type: Object,
    default: () => {},
  },
  dataType: {
    type: Object,
    default: () => {},
  },
});

const emits = defineEmits(['choseOpts']);

const columns = [
  {
    title: '字段',
    dataIndex: 'field_name',
  },
  {
    title: '字段类型',
    dataIndex: 'typeName',
  },
];

// 选择的类型
const type = ref('');

const tableData = ref([]);
const loading = ref(false);

// 获取查找相关字段列表
const getFieldlist = async (id) => {
  loading.value = true;
  tableData.value = [];
  const { data } = await getTableColumnApi(id);

  tableData.value = data.map((item) => {
    return {
      id: item.id,
      field_id: item.key,
      field_name: item.name,
      field_type: item.type,
    };
  });
  tableData.value = tableData.value.filter(
    (item, index, self) =>
      index ===
        self.findIndex(
          (t) => t.field_name === item.field_name && t.id === item.id,
        ) && item.field_type !== 'group',
  );
  const fieldTypeMapping = {
    Text: '文本类型',
    Number: '数字类型',
    Date: '日期类型',
    Date_Time: '日期和时间类型',
    Checkbox: '单选框类型',
    dropdownlist: '下拉选择框类型',
    Text_Area: '多行文本类型',
    Text_Area_Rich: '富文本类型',
    integer: '整数类型',
    group: '分组类型',
    Lookup_Relationship: '查找相关类型',
    Time: '时间类型',
  };
  tableData.value.forEach((item) => {
    item.typeName = fieldTypeMapping[item.field_type] || item.field_type;
  });
  loading.value = false;
};

const selectedRowKeys = ref([]);
const selectedData = computed(() => {
  return tableData.value.filter((item) =>
    selectedRowKeys.value.includes(item.field_id),
  );
});

const onSelectChange = (changableRowKeys) => {
  selectedRowKeys.value = changableRowKeys;
};
// 是否是查看按钮
const btnType = ref(props.dataType?.btnType);
const rowSelection = computed(() => {
  if (btnType.value === 'look') {
    // 查看模式下：显示选中列，但禁用操作
    return {
      selectedRowKeys: unref(selectedRowKeys),
      onChange: () => {}, // 禁止触发变化
      hideDefaultSelections: true, // 隐藏全选等操作
      type: 'checkbox',
      fixed: true, // 固定左侧
      columnWidth: 50, // 设置宽度
      renderCell: (checked, record, index, node) => {
        return h('div', { style: { opacity: 0.5 } }, node); // 禁用样式
      },
    };
  }

  // 编辑模式下保持原有逻辑
  return {
    selectedRowKeys: unref(selectedRowKeys),
    onChange: onSelectChange,
    hideDefaultSelections: true,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  };
});

const [mainModal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[680px]',
  contentClass: 'p-4',
  appendToMain: true,
  confirmText: '确定',
  closeOnClickModal: false,
  footer: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    if (btnType.value === 'edit') {
      const isAll = selectedRowKeys.value.length === tableData.value.length;
      const filteredSelectedData = selectedData.value.map((item) => {
        const { typeName: _typeName, ...rest } = item;
        return rest;
      });

      emits('choseOpts', filteredSelectedData, isAll, type.value);
    }
    modalApi.close();
  },
  onOpenChange: (isOpen) => {
    if (isOpen && props.choseData) {
      selectedRowKeys.value = [];
      type.value = props.dataType?.type;
      btnType.value = props.dataType?.btnType;
      // 动态更新按钮显示状态
      modalApi.setState({
        footer: btnType.value === 'edit',
      });
      if (
        props.choseData?.fields_content &&
        props.choseData?.fields_content.length > 0
      ) {
        selectedRowKeys.value = props.choseData.fields_content.map(
          (item) => item.field_id,
        );
      }
      getFieldlist(props.choseData?.object_id.toLowerCase());
    }
  },
});
</script>

<template>
  <div>
    <mainModal class="w-[60%]" title="对象字段">
      <div class="flex flex-col gap-3 p-2">
        <div
          v-if="selectedData?.length > 0"
          class="ant-select ant-select-multiple !w-[100%]"
          style="width: 100% !important"
        >
          <div
            class="flex min-h-6 cursor-default items-center rounded-[6px] border border-gray-300 px-1 py-px hover:border-gray-300"
          >
            <span
              class="my-1 flex max-h-[100px] flex-wrap gap-1 overflow-y-auto"
            >
              <template v-for="(item, i) in selectedData" :key="i">
                <span
                  class="flex h-[22px] items-center rounded-[6px] border border-gray-300 bg-gray-100 px-2 text-[13px]"
                >
                  <span class="ant-selection-item-content">{{
                    item.field_name
                  }}</span>
                </span>
              </template>
            </span>
          </div>
        </div>
        <Table
          :columns="columns"
          :data-source="tableData"
          :loading="loading"
          :row-key="(record) => record.field_id"
          :row-selection="rowSelection"
          :scroll="{ y: 340 }"
          class="mt-2"
        />
      </div>
    </mainModal>
  </div>
</template>

<style lang="scss" scoped>
.select-search-group {
  position: relative;
  display: inline-flex;
  width: 100% !important;
}

.custom-select :deep(.ant-select-selector) {
  // width: 80% !important;
  border-right: none !important;
  border-radius: 4px 0 0 4px !important;
  transition: all 0.3s;
}

:deep(.ant-select) {
  width: 80% !important;
}

.search-trigger {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  border-left: none !important;
  border-radius: 0 4px 4px 0 !important;
  box-shadow: none;
}

.select-search-group:hover .custom-select :deep(.ant-select-selector),
.select-search-group:hover .search-trigger {
  border-color: #40a9ff !important;
}

.custom-select:focus-within + .search-trigger {
  border-color: #40a9ff !important;
  box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
}
</style>
