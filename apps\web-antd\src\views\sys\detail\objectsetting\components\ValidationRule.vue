<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import {
  Button,
  Card,
  message,
  Pagination,
  Popconfirm,
  Switch,
  Table,
} from 'ant-design-vue';

import { delRuleApi, ruleListApi } from '#/api';

import EditRule from './EditRule.vue';

const route = useRoute();

const fieldLoading = ref(false);
const dataSource = ref([]);
const fieldTotal = ref(0);
const dataColumns = [
  {
    title: '规则名称',
    dataIndex: 'name',
    key: 'name',
    align: 'center',
  },
  {
    title: '错误位置',
    dataIndex: 'error_location',
    key: 'error_location',
    align: 'center',
  },
  {
    title: '错误消息',
    dataIndex: 'error_message',
    key: 'error_message',
    align: 'center',
  },
  {
    title: '启用',
    dataIndex: 'status',
    key: 'activity',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
  },
];

// 传递参数
const choseData = ref({});

const dataParams = reactive({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
});
// 获取列表
async function getList(data) {
  fieldLoading.value = true;
  const { data: resData } = await ruleListApi(data);
  dataSource.value = resData.data.map((item, i) => {
    item.error_location = JSON.parse(item.error_location);
    item.error_location[0]
      ? (item.error_location[0] = '首页')
      : (item.error_location[0] = '');
    item.error_location[1]
      ? (item.error_location[1] = '字段')
      : (item.error_location[1] = '');
    item.error_location = item.error_location.join(' ');
    item.key = i;
    return item;
  });
  fieldTotal.value = resData.total;
  fieldLoading.value = false;
}

const showNew = ref(false);
// 新建
const addRule = () => {
  choseData.value = {};
  showNew.value = true;
};

const showEdit = ref(false);

// 编辑
async function editRule(data) {
  choseData.value = data;
  showNew.value = true;
}

// 删除
async function deletedRule(id) {
  await delRuleApi(id);
  message.success('删除成功');
  getList(dataParams);
}

// 改变页数
async function pageChange(page, pageSize) {
  dataParams.page = page;
  dataParams.page_size = pageSize;
  getList(dataParams); // 重新调用
}

// 新建或编辑后返回列表页
const changeRule = () => {
  showNew.value = false;
  getList(dataParams);
};

onMounted(() => {
  getList(dataParams);
});
</script>

<template>
  <div>
    <Card v-if="!showEdit && !showNew" title="验证规则">
      <template #extra>
        <div class="flex gap-2">
          <Button type="primary" @click="addRule"> 新建 </Button>
        </div>
      </template>
      <Table
        :columns="dataColumns"
        :data-source="dataSource"
        :loading="fieldLoading"
        :pagination="false"
        :scroll="{ y: 500 }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'activity'">
            <Switch v-model:checked="record.status" disabled />
          </template>
          <template v-if="column.key === 'operation'">
            <div class="flex justify-center gap-6">
              <Button type="primary" @click="editRule(record)">编辑</Button>
              <Popconfirm
                cancel-text="取消"
                ok-text="确定"
                title="您确定要删除该规则吗？"
                @confirm="deletedRule(record.id)"
              >
                <Button danger type="primary"> 删除 </Button>
              </Popconfirm>
            </div>
          </template>
        </template>
        <template #footer>
          <div class="text-right">
            <Pagination
              v-model:current="dataParams.page"
              v-model:page-size="dataParams.page_size"
              :show-size-changer="true"
              :show-total="(fieldTotal) => `共 ${fieldTotal} 条`"
              :total="fieldTotal"
              @change="pageChange"
            />
          </div>
        </template>
      </Table>
    </Card>
    <!-- 编辑，新建规则 -->
    <EditRule
      v-if="showNew"
      :chose-data="choseData"
      @cancel-edit-rule="showNew = false"
      @change-rule="changeRule"
    />
  </div>
</template>

<style></style>
