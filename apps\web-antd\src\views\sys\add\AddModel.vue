<!-- eslint-disable no-use-before-define -->
<script setup>
import { computed, provide, ref } from 'vue';

import { useVbenModal, VbenIcon } from '@vben/common-ui';

import {
  Button,
  Form,
  FormItem,
  Input,
  message,
  Skeleton,
  Tooltip,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import { createTableDataApi, getTableAddOrEditListApi } from '#/api';
import {
  Audio,
  Checkbox,
  ColorPicker,
  Date,
  DateTime,
  Formula,
  InputInteger,
  InputNumber,
  InputPassword,
  InputPercent,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
  Time,
  UploadFile,
  UploadImg,
  UploadVideo,
} from '#/components/form-ui';

const props = defineProps({
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
  // 详情页添加使用的参数
  addDefult: {
    type: Object,
    default: () => null,
    required: false,
  },
  // 详情页添加使用的参数
  columnList: {
    type: Array,
    default: () => [],
    required: false,
  },
});

const emits = defineEmits(['clearFilter', 'afterAdd']);

const formRef = ref(null);
const rules = ref({});
const isEdit = ref(false);

const [Modal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[60vw]',
  contentClass: 'p-8',
  confirmText: '保存',
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    onFinish();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      components.value = [];
      getAddItemMessage();
    }
  },
});

const skeletonLoading = ref(true);

const components = ref([]);

// 提供 components 给子组件使用
provide('parentComponents', components);

const modelForm = computed(() => {
  // 处理modalForm值
  const fromValues = {};
  components.value
    .filter((item) => item.type !== 'group' && item.value)
    .forEach((item) => {
      fromValues[item.key] = item.value;
    });

  return fromValues;
});

async function getAddItemMessage() {
  const { data } = await getTableAddOrEditListApi(props.objectName);
  components.value = data.filter(
    (item) =>
      item.field_visible === true &&
      item.key !== 'system_information' &&
      item.key !== 'OwnerId',
  );

  const addDefultData = modalApi.getData();
  components.value.forEach((item) => {
    item.value = null;
  });
  if (addDefultData?.addDefultMessage) {
    components.value.forEach((item) => {
      if (item.key === addDefultData.addDefultMessage.field) {
        item.value = {
          name: null,
          id: addDefultData.addDefultId,
          field: addDefultData.addDefultMessage.main_table_display_field,
        };
      }
    });
  }

  skeletonLoading.value = false;

  // 处理自定义校验规则
  components.value
    .filter((item) => item.type !== 'group' && item.field_required)
    .forEach((item) => {
      rules.value[item.key] = [
        {
          required: true,
          message: `${item.name}不能为空`,
          trigger: ['blur'],
        },
      ];
    });
}

function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'Text_Area') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'Password') return InputPassword;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
  if (type === 'Percent') return InputPercent;
  if (type === 'Date') return Date;
  if (type === 'upload_audio') return Audio;
  if (type === 'Date_Time') return DateTime;
  if (type === 'Time') return Time;
  if (type === 'Checkbox') return Checkbox;
  if (type === 'upload_pic') return UploadImg;
  if (type === 'integer') return InputInteger;
  if (type === 'Formula') return Formula;
  if (type === 'upload_video') return UploadVideo;
  if (type === 'upload_file') return UploadFile;
  if (type === 'color_picker') return ColorPicker;
}

async function onFinish() {
  // 过滤出填写的字段
  const changeField = components.value
    .filter((item) => item.type !== 'group' && item.value)
    .map((item) => {
      return {
        key: item.key,
        type: item.type,
        value: item.value,
      };
    });

  const dataParams = {};

  changeField.forEach((item) => {
    switch (item.type) {
      case 'Date': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'YYYY-MM-DD').format('YYYY-MM-DD')
          : null;

        break;
      }
      case 'Date_Time': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'YYYY-MM-DD HH:mm:ss').format(
              'YYYY-MM-DD HH:mm:ss',
            )
          : null;

        break;
      }
      case 'Time': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'HH:mm:ss').format('HH:mm:ss')
          : null;

        break;
      }
      case 'upload_audio': {
        dataParams[item.key] = item.value?.url ?? null;
        break;
      }
      default: {
        dataParams[item.key] = item.value ?? null;
      }
    }
  });
  formRef.value.validate().then(async () => {
    await createTableDataApi(props.objectName, dataParams);
    message.success('添加成功');
    if (props.addDefult) {
      emits('afterAdd');
    } else {
      emits('clearFilter');
    }

    modalApi.close();
  });
}

const openMoreIcon = () => {
  window.open('https://icon-sets.iconify.design/solar/page-2.html', '_blank');
};
</script>

<template>
  <Modal>
    <template #title>
      <div class="flex gap-2">
        <span>添加</span>
      </div>
    </template>
    <Skeleton :loading="skeletonLoading" active />
    <Form
      ref="formRef"
      :label-col="{ span: 9 }"
      :model="modelForm"
      :rules="rules"
      :wrapper-col="{ span: 15 }"
      name="basic"
    >
      <div class="grid grid-cols-1 xl:grid-cols-2">
        <template v-for="item in components" :key="item.key">
          <div
            v-if="item.type === 'group'"
            class="card-box bg-theme-color col-span-1 mb-3 mt-3 p-1 font-bold text-[#fff] opacity-80 xl:col-span-2"
          >
            <span class="opacity-100">{{ item.name }}</span>
          </div>
          <FormItem
            v-else-if="item.key !== 'app_ICO'"
            :label="item.name"
            :name="item.key"
          >
            <component
              :is="getComponent(item.type)"
              v-model="item.value"
              :is-edit="isEdit"
              :object-id="props.objectId"
              :object-name="props.objectName"
              :parameters="item"
              class="w-[100%]"
            />
          </FormItem>
          <!-- 图标选择特殊处理 -->
          <FormItem v-else :label="item.name" :name="item.key">
            <div class="flex gap-1">
              <Input
                v-model:value="item.value"
                placeholder="例如：material-symbols:wb-auto"
                size="small"
              >
                <template #suffix>
                  <VbenIcon :icon="item.value" class="size-4" />
                </template>
              </Input>
              <Tooltip>
                <template #title>前往图标库</template>
                <Button @click="openMoreIcon">
                  <VbenIcon class="size-4" icon="ep:right" />
                </Button>
              </Tooltip>
            </div>
          </FormItem>
        </template>
      </div>
    </Form>
  </Modal>
</template>

<style></style>
