<!-- eslint-disable no-use-before-define -->
<script setup>
import { computed, ref } from 'vue';

import { useVbenModal, VbenIcon } from '@vben/common-ui';

import {
  Button,
  Form,
  FormItem,
  Input,
  message,
  Skeleton,
  Tooltip,
} from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  createTableDataApi,
  editTableItemApi,
  getTableAddOrEditListApi,
  getTableItemApi,
} from '#/api';
import {
  Checkbox,
  ColorPicker,
  Date,
  DateTime,
  Formula,
  InputInteger,
  InputNumber,
  InputPassword,
  InputPercent,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
  Time,
  UploadFile,
  UploadImg,
  UploadVideo,
} from '#/components/form-ui';

const props = defineProps({
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
  editRecardId: {
    type: String,
    default: '',
  },
  columnList: {
    type: Array,
    default: () => [],
    required: false,
  },
  newIf: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['afterAdd', 'afterEdit']);

const formRef = ref(null);
const rules = ref({});

// eslint-disable-next-line vue/no-dupe-keys
const newIf = ref(false);

const [Modal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[60vw]',
  contentClass: 'p-8',
  confirmText: '保存',
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    onFinish();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      newIf.value = props.newIf;
      components.value = [];
      getAddItemMessage();
    }
  },
});

const skeletonLoading = ref(true);

const components = ref([]);

const modelForm = computed(() => {
  // 处理modalForm值
  const fromValues = {};
  components.value
    .filter((item) => item.type !== 'group' && item.value)
    .forEach((item) => {
      fromValues[item.key] = item.value;
    });

  return fromValues;
});
const showEdit = ref(false);

async function getAddItemMessage() {
  const { data } = await getTableAddOrEditListApi(props.objectName);
  components.value = data.filter(
    (item) =>
      item.field_visible === true &&
      item.key !== 'system_information' &&
      item.key !== 'OwnerId',
  );

  components.value = components.value.map((item) => {
    const matchInColumnList = props.columnList.some(
      (column) => column.id === item.id,
    );
    return {
      ...item,
      showName: matchInColumnList,
    };
  });

  const addDefultData = modalApi.getData();
  components.value.forEach((item) => {
    item.value = null;
  });
  if (addDefultData?.addDefultMessage) {
    components.value.forEach((item) => {
      if (item.key === addDefultData.addDefultMessage.field) {
        item.value = {
          name: null,
          id: addDefultData.addDefultId,
          field: addDefultData.addDefultMessage.main_table_display_field,
        };
      }
    });
  }

  skeletonLoading.value = false;

  // 处理自定义校验规则
  components.value
    .filter((item) => item.type !== 'group' && item.field_required)
    .forEach((item) => {
      rules.value[item.key] = [
        {
          required: true,
          message: `${item.name}不能为空`,
          trigger: ['blur'],
        },
      ];
    });
  if (newIf.value) {
    showEdit.value = true;
  } else {
    getEditFieldValue();
  }
}

async function getEditFieldValue() {
  showEdit.value = false;
  const { data } = await getTableItemApi(props.objectName, props.editRecardId);
  components.value.forEach((item) => {
    if (item.key === 'app_ICO') {
      item.value = data[item.key] ? data[item.key].id : null;
      return;
    }
    switch (item.type) {
      case 'dropdownlist': {
        item.value = data[item.key] ? data[item.key].name : null;

        break;
      }
      case 'Date': {
        item.value = data[item.key]
          ? dayjs(data[item.key], 'YYYY-MM-DD')
          : null;

        break;
      }
      case 'Date_Time': {
        item.value = data[item.key]
          ? dayjs(data[item.key], 'YYYY-MM-DD HH:mm:ss')
          : null;

        break;
      }
      case 'Time': {
        item.value = data[item.key] ? dayjs(data[item.key], 'HH:mm:ss') : null;

        break;
      }
      default: {
        item.value = data[item.key] ?? null;
      }
    }
    showEdit.value = true;
  });
}

function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'Text_Area') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'Password') return InputPassword;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
  if (type === 'Percent') return InputPercent;
  if (type === 'Date') return Date;
  if (type === 'Date_Time') return DateTime;
  if (type === 'Time') return Time;
  if (type === 'Checkbox') return Checkbox;
  if (type === 'upload_pic') return UploadImg;
  if (type === 'integer') return InputInteger;
  if (type === 'Formula') return Formula;
  if (type === 'upload_video') return UploadVideo;
  if (type === 'upload_file') return UploadFile;
  if (type === 'color_picker') return ColorPicker;
}

async function onFinish() {
  // 过滤出填写的字段
  let changeField = [];
  changeField = newIf.value
    ? components.value
        .filter((item) => item.type !== 'group' && item.value)
        .map((item) => {
          return {
            key: item.key,
            type: item.type,
            value: item.value,
          };
        })
    : components.value
        .filter(
          (item) =>
            (item.type !== 'group' && item.value) || item.type === 'Checkbox',
        )
        .map((item) => {
          return {
            key: item.key,
            type: item.type,
            value: item.value,
          };
        });

  let dataParams = {};
  if (!newIf.value) {
    dataParams = {
      id: props.editRecardId,
    };
  }

  changeField.forEach((item) => {
    switch (item.type) {
      case 'Date': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'YYYY-MM-DD').format('YYYY-MM-DD')
          : null;

        break;
      }
      case 'Date_Time': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'YYYY-MM-DD HH:mm:ss').format(
              'YYYY-MM-DD HH:mm:ss',
            )
          : null;

        break;
      }
      case 'Time': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'HH:mm:ss').format('HH:mm:ss')
          : null;

        break;
      }
      default: {
        dataParams[item.key] = item.value ?? null;
      }
    }
  });
  formRef.value.validate().then(async () => {
    if (newIf.value) {
      await createTableDataApi(props.objectName, dataParams);
      message.success('添加成功');
      emits('afterAdd');
    } else {
      await editTableItemApi(props.objectName, props.editRecardId, dataParams);
      message.success('修改成功');
      emits('afterEdit');
    }

    modalApi.close();
  });
}

const openMoreIcon = () => {
  window.open('https://icon-sets.iconify.design/solar/page-2.html', '_blank');
};
</script>

<template>
  <Modal>
    <template #title>
      <div class="flex gap-2">
        <span v-if="newIf">新建</span>
        <span v-else>编辑</span>
      </div>
    </template>
    <Skeleton :loading="skeletonLoading" active />
    <Form
      ref="formRef"
      :label-col="{ span: 9 }"
      :model="modelForm"
      :rules="rules"
      :wrapper-col="{ span: 15 }"
      name="basic"
    >
      <div class="grid grid-cols-1 xl:grid-cols-2">
        <template v-for="item in components" :key="item.key">
          <template v-if="item.showName">
            <div
              v-if="item.type === 'group' && newIf"
              class="card-box bg-theme-color col-span-1 mb-3 mt-3 p-1 font-bold text-[#fff] opacity-80 xl:col-span-2"
            >
              <span class="opacity-100">{{ item.name }}</span>
            </div>
            <FormItem
              v-if="item.key !== 'app_ICO' && item.type !== 'group'"
              :label="item.name"
              :name="item.key"
            >
              <component
                :is="getComponent(item.type)"
                v-if="showEdit"
                v-model="item.value"
                :object-id="props.objectId"
                :object-name="props.objectName"
                :parameters="item"
                class="w-[100%]"
              />
            </FormItem>
            <!-- 图标选择特殊处理 -->
            <FormItem
              v-if="item.key === 'app_ICO'"
              :label="item.name"
              :name="item.key"
            >
              <div class="flex gap-1">
                <Input
                  v-model:value="item.value"
                  placeholder="例如：material-symbols:wb-auto"
                  size="small"
                >
                  <template #suffix>
                    <VbenIcon :icon="item.value" class="size-4" />
                  </template>
                </Input>
                <Tooltip>
                  <template #title>前往图标库</template>
                  <Button @click="openMoreIcon">
                    <VbenIcon class="size-4" icon="ep:right" />
                  </Button>
                </Tooltip>
              </div>
            </FormItem>
          </template>
        </template>
      </div>
    </Form>
  </Modal>
</template>

<style></style>
