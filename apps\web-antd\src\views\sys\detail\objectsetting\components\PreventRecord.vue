<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { VbenSpinner } from '@vben/common-ui';

import {
  Button,
  Card,
  Checkbox,
  Form,
  FormItem,
  message,
  Select,
} from 'ant-design-vue';

import { findFieldApi, getObjectDetailApi, updateObjApi } from '#/api';
import { useLoading } from '#/hook';

const { isLoading, startLoading, stopLoading } = useLoading();

const route = useRoute();

// 字段选项
const objfieldOption = ref([]);

const formState = ref({
  field: [],
  checked: false,
});

// 获取字段列表
const getField = async () => {
  const { data } = await findFieldApi(route.params.id);
  objfieldOption.value = data;
};

// 获取对象详情
async function getObjectDetail() {
  const { data } = await getObjectDetailApi(route.params.id || '');
  formState.value.checked = data.is_not_repeat_submit;
  formState.value.field = data.repeat_submit_association_fields
    ? data.repeat_submit_association_fields.split(',')
    : [];
}

const changeCheck = () => {
  if (formState.value.checked === false) {
    formState.value.field = [];
  }
};

const formRef = ref();

const rules = computed(() => ({
  field: formState.value.checked
    ? [{ required: true, message: '请选择字段!', trigger: 'blur' }]
    : [],
  checked: [],
}));
// 监听 checked 变化
watch(
  () => formState.value.checked,
  () => {
    if (formRef.value) {
      // 清空校验
      formRef.value.clearValidate(['field']);
    }
  },
);

const submit = () => {
  formRef.value
    .validateFields()
    .then(async () => {
      startLoading();
      const updateData = {
        id: route.params.id,
        is_not_repeat_submit: formState.value.checked,
        repeat_submit_association_fields: formState.value.field.join(','),
      };
      stopLoading();
      await updateObjApi(updateData);
      message.success('提交成功');
    })
    .catch(() => {});
};

onMounted(async () => {
  startLoading();
  await getField();
  await getObjectDetail();
  stopLoading();
});
</script>

<template>
  <div>
    <Card title="防止记录重复">
      <template #extra>
        <div class="flex gap-2">
          <Button type="primary" @click="submit"> 提交 </Button>
        </div>
      </template>
      <div class="flex flex-col gap-3 p-2">
        <Form
          ref="formRef"
          :label-col="{ span: 5 }"
          :model="formState"
          :rules="rules"
          :wrapper-col="{ span: 15 }"
          autocomplete="off"
          name="basic"
        >
          <div class="grid">
            <FormItem
              class="m-[2%]"
              label="是否开启防止记录重复"
              name="checked"
            >
              <Checkbox
                v-model:checked="formState.checked"
                @change="changeCheck"
              />
            </FormItem>
            <FormItem class="m-[2%]" label="选择字段" name="field">
              <Select
                v-model:value="formState.field"
                :disabled="!formState.checked"
                :field-names="{ label: 'name', value: 'key' }"
                :options="objfieldOption"
                class="!w-[70%]"
                mode="multiple"
                placeholder="请选择字段"
              />
            </FormItem>
          </div>
        </Form>
      </div>
    </Card>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
