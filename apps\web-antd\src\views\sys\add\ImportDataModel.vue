<!-- eslint-disable vue/no-v-html -->
<script setup>
import { reactive, ref, toRaw } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { Icon } from '@vben/icons';

import { Button, message, Result, Steps, UploadDragger } from 'ant-design-vue';

import {
  getDownloadExcelApi,
  getFilterColumnsApi,
  uploadExcelApi,
} from '#/api';
import { useTableStore } from '#/store';

const props = defineProps({
  objectName: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['clearFilter']);

const tableStore = useTableStore();
const stepsItem = [
  {
    title: '选择导入模版',
  },
  {
    title: '上传数据文件',
  },
  {
    title: '上传结果展示',
  },
];
const currentStep = ref(0);

const fileList = ref([]);
const importResult = reactive({
  status: 'success',
  title: 'Excel导入数据库成功',
});

const [Modal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[850px] ',
  contentClass: 'p-4',
  confirmText: '保存',
  closeOnClickModal: false,
  showConfirmButton: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      currentStep.value = 0;
      fileList.value = [];
      // eslint-disable-next-line no-use-before-define
      errorHtmlText.value = null;
      getFelidsMessage();
    }
  },
});

function nextStep(number) {
  currentStep.value = number;
}

const felidList = ref([]);
// 获取字段模版信息
async function getFelidsMessage() {
  const { data } = await getFilterColumnsApi(props.objectName);
  felidList.value = data.data.slice(0, -4) || []; // slice操作去除后面几项，例如：修改人，操作时间等等
  felidList.value.forEach((item) => {
    switch (item.type) {
      case 'Text': {
        item.typeName = '请填写文本';
        break;
      }
      case 'Number': {
        item.typeName = '请填写数字';
        break;
      }
      case 'Date': {
        item.typeName = '请填写日期';
        break;
      }
      case 'Date_Time': {
        item.typeName = '请填写日期和时间';
        break;
      }
      case 'Checkbox': {
        item.typeName = '类型为单选框';
        break;
      }
      case 'dropdownlist': {
        item.typeName = '类型为下拉选择框';
        break;
      }
      case 'Text_Area': {
        item.typeName = '类型为多行文本';
        break;
      }
      case 'Text_Area_Rich': {
        item.typeName = '类型为富文本';
        break;
      }
      case 'integer': {
        item.typeName = '请填写整数';
        break;
      }
      case 'group': {
        item.typeName = '类型为分组';
        break;
      }
      case 'Lookup_Relationship': {
        item.typeName = '类型为查找相关';
        break;
      }
      default: {
        item.typeName = item.type;
      }
      // No default
    }
  });
}

// 下载模版
async function downloadTemplate() {
  try {
    const data = await getDownloadExcelApi(props.objectName);
    const fileName = `导出模版-${props.objectName}`;
    const link = document.createElement('a');
    link.download = fileName;
    link.style.display = 'none';
    link.href = URL.createObjectURL(data);
    document.body.append(link);
    link.click();
    URL.revokeObjectURL(link.href);
    link.remove();
    message.success('下载成功');
  } catch (error) {
    throw new Error(error);
  }
}

// 上传文件
async function onBeforeUpload(file) {
  // eslint-disable-next-line no-use-before-define
  errorHtmlText.value = null;
  // 上传限制大小20M
  if (file.size / 1024 / 1024 > 20) {
    message.error('上传文件大小不能超过20M!');
    return false;
  }
  fileList.value.push(file);
  return false;
}

const errorHtmlText = ref(null);
// 开始导入
async function startImport() {
  if (fileList.value.length === 0) {
    return message.info('请先上传文件');
  }
  const formData = new FormData();
  formData.append('file', toRaw(fileList.value[0].originFileObj));
  await uploadExcelApi(props.objectName, formData);

  currentStep.value = 2;
  emits(
    'clearFilter',
    {
      page: 1,
      page_size: 10,
      keywords: '',
      order_by: '',
      sort_by: '',
      conditions: [],
      is_deleted: tableStore.isDelete,
    },
    'clear',
  );
}
</script>

<template>
  <Modal title="导入数据">
    <Steps :current="currentStep" :items="stepsItem" />
    <div v-show="currentStep === 0" class="my-5 flex h-[500px] flex-col p-1">
      <div
        class="color-theme-color flex h-[40px] cursor-pointer items-center"
        @click="downloadTemplate"
      >
        <Icon class="size-5" icon="foundation:download" />
        <span>下载模版</span>
      </div>
      <div
        class="bg-theme-color flex h-[40px] items-center font-bold text-[#fff]"
      >
        <span class="field-name w-[200px] text-center">属性</span>
        <span class="field-information">
          填写说明（若不填写有默认值的工作属性，系统会填写该默认值）
        </span>
      </div>
      <div class="h-[420px] overflow-y-auto border p-1">
        <div
          v-for="item in felidList"
          :key="item.name"
          class="flex h-[40px] items-center"
        >
          <span class="filed-name w-[200px] text-center">{{ item.name }}</span>
          <span class="filed-information">{{ item.typeName }}</span>
        </div>
      </div>
    </div>
    <div v-show="currentStep === 1" class="my-5">
      <UploadDragger
        v-model:file-list="fileList"
        :before-upload="onBeforeUpload"
        :max-count="1"
        accept=".xls,.xlsx"
        list-type="picture"
        name="file"
      >
        <div class="flex flex-col items-center justify-center">
          <Icon class="color-theme-color size-12" icon="icon-park:upload-one" />
          <p class="text-[16px] font-bold">请选择数据文件导入</p>
          <p class="ant-upload-hint">
            格式仅支持.xls和.xlsx，系统会按照工作表里的格式读取数据，请保证文件格式正确
          </p>
          <p class="ant-upload-hint">
            工作项数量限制10000条，数据文件大小限制为10M
          </p>
        </div>
      </UploadDragger>
      <div
        v-if="errorHtmlText"
        class="card-box mt-3 h-[100px] overflow-y-auto p-3 text-[red]"
      >
        <!-- 使用 v-text 替代 v-html 以避免 XSS 攻击 -->
        <div><span v-html="errorHtmlText"></span></div>
      </div>
    </div>
    <div v-show="currentStep === 2" class="my-5 overflow-scroll">
      <Result :status="importResult.status" :title="importResult.title" />
    </div>
    <template #append-footer>
      <Button v-show="currentStep === 0" type="primary" @click="nextStep(1)">
        下一步
      </Button>
      <Button v-show="currentStep === 1" type="primary" @click="nextStep(0)">
        上一步
      </Button>
      <Button v-show="currentStep === 1" type="primary" @click="startImport">
        开始导入
      </Button>
      <Button
        v-show="currentStep === 2"
        type="primary"
        @click="() => modalApi.close()"
      >
        确定
      </Button>
    </template>
  </Modal>
</template>

<style></style>
