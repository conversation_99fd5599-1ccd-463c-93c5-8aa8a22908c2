import { requestClient } from '#/api/request';

/**
 * 获取话务系统请求秘钥地址
 */

export async function loginTCCCApi(data: any) {
  return requestClient.get('/tencent/loginTCCC', {
    params: {
      ...data,
    },
  });
}

/**
 * 获取电话服务记录
 */

export async function getPhoneRecordApi(data: any) {
  return requestClient.get('/tencent/tele_record', {
    params: {
      ...data,
    },
  });
}

/**
 * 获取语音识别临时秘钥
 */
export async function getDiscernTokenApi() {
  return requestClient.post('/tencent/get_signature');
}

/**
 * 获取语音全文总结和音频全文
 */
export async function getAudioSummaryApi(data: any) {
  return requestClient.get('/ai/convert_voice_text', {
    params: {
      ...data,
    },
  });
}
