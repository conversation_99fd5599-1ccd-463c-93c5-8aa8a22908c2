import { requestClient } from '#/api/request';

/**
 * @returns 获取查找相关选项数据
 */
export async function getTableLookUpListApi(
  objectName: string,
  field: string,
  data: any,
) {
  return requestClient.post(`/${objectName}/relationship/${field}/`, data);
}

/**
 * @returns 获取查找相关表头数据
 */
export async function getTableHeaderColumnApi(objectId: string, field: string) {
  return requestClient.get(`/relationship/table_header`, {
    params: {
      object_id: objectId,
      field,
    },
  });
}

/**
 * @returns 新建表格记录
 */
export async function createTableDataApi(objectName: string, data: any) {
  return requestClient.post(`/${objectName}/add`, data);
}

/**
 * @returns 删除表格记录
 */
export async function deleteTableItemApi(objectName: string, data: any) {
  return requestClient.delete(`/${objectName}/delete`, {
    data,
  });
}

/**
 * @returns 获取表格记录详情
 */

export async function getTableItemApi(objectName: string, id: string) {
  return requestClient.get(`/${objectName}/detail/${id}`);
}

/**
 * @returns 编辑表格记录
 */

export async function editTableItemApi(objectName: string, id: any, data: any) {
  return requestClient.put(`/${objectName}/edit/${id}`, { ...data });
}

/**
 * @returns 获取表格关联的子表
 */
export async function getTableRelationApi(object_uid: string) {
  return requestClient.get(`/relationship/`, {
    params: {
      object_uid,
    },
  });
}

/**
 * @returns 获取表格关联的子表的值
 */
export async function getTableRelationValueApi(object: string, data: any) {
  return requestClient.post(`/${object}/get_${object}s`, data);
}

/**
 * @returns 获取表格详情的操作日志
 */

export async function getEditTableLogApi(data: any) {
  return requestClient.post(`/operation_log_hand/get_operation_log_list`, data);
}

/**
 * @returns 数据表格复制
 */

export async function copyTableRecordApi(objectName: string, data: any) {
  return requestClient.post(`/${objectName}/copy`, data);
}

/**
 * @returns 数据解除关联接口
 */
export async function removeTableRelationApi(objectName: string, data: any) {
  return requestClient.post(`/${objectName}/update`, data);
}

/**
 * @returns 获取对象视图列表
 */
export async function getTableViewListApi(objectId: string) {
  return requestClient.get(`/tableview_hand/owner_tableview/${objectId}`);
}
