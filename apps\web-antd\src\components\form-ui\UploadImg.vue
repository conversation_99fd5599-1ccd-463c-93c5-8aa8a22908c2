<script setup>
import { ref, watch } from 'vue';

import { VbenIcon } from '@vben/common-ui';

import { message, Upload } from 'ant-design-vue';

import { UploadImgApi } from '#/api/common';

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const fileList = ref([]);
props.modelValue &&
  fileList.value.push({
    url: props.modelValue,
  });

async function customRequest(file) {
  const fileParams = new FormData();
  fileParams.append('file', file.file);
  fileParams.append('file_rel_id', 'id_test');
  fileParams.append('file_rel_Object', 'object_test');
  fileParams.append('file_max_num', 1);

  try {
    const { data } = await UploadImgApi(fileParams);
    fileList.value = [{ url: data.url }];
    message.success('上传成功');
  } catch {
    fileList.value = [];
  }
}

watch(fileList, (newValue) => {
  if (newValue.length === 0) {
    emits('update:modelValue', null);
  } else {
    emits('update:modelValue', newValue[0].url);
  }
});
</script>

<template>
  <div>
    <Upload
      v-model:file-list="fileList"
      :custom-request="customRequest"
      :disabled="props.parameters.field_readonly"
      accept="image/*"
      action
      list-type="picture-card"
      name="avatar"
    >
      <div
        v-if="fileList.length === 0"
        class="flex flex-col items-center justify-center"
      >
        <VbenIcon icon="proicons:add" />
        <div style="margin-top: 8px">上传</div>
      </div>
    </Upload>
  </div>
</template>

<style></style>
