import { requestClient } from '#/api/request';
/**
 *
 * @returns 审批流-列表数据
 */
export async function approvalListapi(id: string) {
  return requestClient.get(`/approval_flows/object/${id}`);
}

/**
 *
 * @returns 获取审批流详情
 */
export async function getApprovedetailapi(id: string) {
  return requestClient.get(`/approval_flows/${id}`);
}

/**
 *
 * @returns 审批流-重新排序
 */
export async function changeOrderapi(data: any) {
  return requestClient.patch(`/approval_flows/sort`, data);
}

/**
 *
 * @returns 审批流-启用未启用
 */
export async function changeStatusapi(data: any) {
  return requestClient.post(`/approval_flows/change_status`, data);
}

/**
 *
 * @returns 审批流-删除未启用
 */
export async function delApproveapi(id: string) {
  return requestClient.delete(`/approval_flows/${id}`);
}

/**
 *
 * @returns 步骤一-判断是否是唯一名称
 */
export async function ifOnlynameapi(data: any) {
  return requestClient.post(`/approval_flows/check_name_exists`, data);
}

/**
 *
 * @returns 步骤一-新增、保存数据
 */
export async function changeApprovedapi(data: any) {
  return requestClient.post(`/approval_flows/save`, data);
}

/**
 *
 * @returns 步骤三、四-获取人员列表
 */
export async function getuserlistapi(data: any) {
  return requestClient.post(`/user_hand/get_user_list`, data);
}

/**
 *
 * @returns 步骤四-获取部门负责人列表
 */
export async function getmanagerlistapi(data: any) {
  return requestClient.post(`/user_hand/get_manager_list`, data);
}

/**
 *
 * @returns 步骤五-获取现有作业列表
 */
export async function getapproveWorklistapi(data: any) {
  return requestClient.post(`/works/list`, data);
}

/**
 *
 * @returns 待办列表-我发起的待办
 */
export async function getmainGetbacklogapi(data: any) {
  return requestClient.post(`/backlog_hand/get_initiator_views`, data);
}

/**
 *
 * @returns 待办列表-我审批的待办
 */
export async function getmainApproveapi(data: any) {
  return requestClient.post(`/backlog_hand/get_approval_views`, data);
}

/**
 *
 * @returns 待办列表-执行审批接口
 */
export async function toApproveapi(data: any) {
  return requestClient.post(`/backlog_hand/approval`, data);
}

/**
 *
 * @returns 获取对象合适的审批流
 */
export async function getApprovalprocessapi(
  object_id: string,
  data_id: string,
) {
  return requestClient.get(`/backlog_hand/get_meet_approval`, {
    params: {
      object_id,
      data_id,
    },
  });
}

/**
 *
 * @returns 发起审批接口
 */
export async function sendApproveapi(data: any) {
  return requestClient.post(`/backlog_hand/initiate_approval`, data);
}

/**
 *
 * @returns 获取审批流程视图
 */
export async function viewApproveapi(data: any) {
  return requestClient.post(`/backlog_hand/approval_flow_views`, data);
}
