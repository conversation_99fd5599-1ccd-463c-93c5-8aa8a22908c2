import { requestClient } from '#/api/request';

/**
 *
 * @returns 对象--获取对象详情
 */

export async function getObjectDetailApi(object_id: string) {
  return requestClient.get(`/object_hand/detail/${object_id}`);
}

/**
 *
 * @returns 字段-- 获取字段列表
 */
export async function getFiledListApi(data: any) {
  return requestClient.post(`/field_hand/list`, data);
}

/**
 *
 * @returns 字段--删除字段
 */
export async function delFiledApi(id: string) {
  return requestClient.delete(`/field_hand/${id}`);
}

/**
 *
 * @returns 字段--编辑页字段详情
 */
export async function getFieldMsgApi(id: string) {
  return requestClient.get(`/field_hand/detail/${id}`);
}

/**
 *
 * @returns 对象编辑、角色权限--获取对象类型下拉选项
 */
export async function getObjSelectApi(object_id: string, field: string) {
  return requestClient.get(`/select_hand/get_selects`, {
    params: {
      object_id,
      field,
    },
  });
}

/**
 *
 * @returns 对象编辑--获取关联屏幕界面作业
 */
export async function getWorkListApi() {
  return requestClient.get(`/works/screen`);
}

/**
 *
 * @returns 对象编辑--获取查找相关表头
 */
export function findobjectHeaderApi(data: any) {
  return requestClient.get(`/relationship/table_header`, {
    params: data,
  });
}

/**
 *
 * @returns 对象编辑--获取应用包表格
 */
export function findobjCorrelationApi(
  object: string,
  field: string,
  data: any,
) {
  return requestClient.post(`/${object}/relationship/${field}/`, data);
}

/**
 *
 * @returns 对象编辑--关联字段
 */
export function findFieldApi(id: string) {
  return requestClient.get(`/relationship/fields/${id}`);
}

/**
 *
 * @returns 对象编辑--保存对象
 */
export function editObjApi(data: any) {
  return requestClient.post(`/object_hand/update`, data);
}

/**
 *
 * @returns 角色权限--获取角色
 */
export function getRoleApi(data: any) {
  return requestClient.post(`/permission_hand/object/list`, data);
}

/**
 *
 * @returns 角色权限--获取按钮
 */
export function getObjetBtnApi(id: string) {
  return requestClient.get(`/button_hand/object/${id}`);
}

/**
 *
 * @returns 角色权限--修改权限
 */
export function changeRolepowerApi(data: any) {
  return requestClient.post(`/permission_hand/update`, data);
}

/**
 *
 * @returns 新建字段--步骤三获取角色
 */
export function getObjRolelistApi(data: any) {
  return requestClient.post(`/role/get_roles`, data);
}

/**
 *
 * @returns 新建字段--新建字段保存
 */
export function fieldObjSaveApi(data: any) {
  return requestClient.post(`/field_hand/add`, data);
}

/**
 *
 * @returns 新建字段--公式检查语法
 */
export function checkPythonApi(data: any) {
  return requestClient.post(`/field_hand/check_python`, data);
}

/**
 *
 * @returns 新建字段获取全局选项列表
 */
export function getObjGlobalOptionsApi(val: string) {
  return requestClient.get(`/global_select_hand/all`, {
    params: {
      keywords: val,
    },
  });
}

/**
 *
 * @returns 保存字段
 */
export function fieldObjEditApi(data: any) {
  return requestClient.post(`/field_hand/update`, data);
}

/**
 *
 * @returns 按钮列表
 */
export function btnObjListApi(data: any) {
  return requestClient.post(`/button_hand/list`, data);
}

/**
 *
 * @returns 按钮列表删除
 */
export function btnObjDelApi(id: string) {
  return requestClient.delete(`/button_hand/${id}`);
}

/**
 *
 * @returns 字段权限-表头
 */
export async function getObjFieldPowerHeadApi(id: string) {
  return requestClient.get(`/field_permission_hand/header/${id}`);
}

/**
 *
 * @returns 字段权限-数据
 */
export async function getObjFieldPowerApi(data: any) {
  return requestClient.post(`/field_permission_hand/list`, data);
}

/**
 *
 * @returns 字段权限-更新
 */
export async function updateObjFieldPowerApi(data: any) {
  return requestClient.post(`/field_permission_hand/update`, data);
}

/**
 *
 * @returns 按钮-对象作业列表
 */
export async function getBtnObjWorkApi(id: string) {
  return requestClient.get(`/works/select_works/${id}`);
}

/**
 *
 * @returns 按钮-新建按钮
 */
export async function addObjBtnApi(data: any) {
  return requestClient.post(`/button_hand/add`, data);
}

/**
 *
 * @returns 按钮-按钮详情
 */
export async function getObjBtnDetailApi(id: string) {
  return requestClient.get(`/button_hand/detail/${id}`);
}

/**
 *
 * @returns 按钮-编辑按钮
 */
export async function updateObjBtnApi(data: any) {
  return requestClient.post(`/button_hand/update`, data);
}

/**
 *
 * @returns 作业-对象作业列表
 */
export async function getObjWorkListApi(data: any) {
  return requestClient.post(`/works/list`, data);
}

/**
 *
 * @returns 列表-新建对象
 */
export async function newObjapi(data: any) {
  return requestClient.post(`/object_hand/add`, data);
}

/**
 *
 * @returns 对象-发布对象
 */
export async function issueObjapi(id: string) {
  return requestClient.get(`/rtc/create_objects`, {
    params: {
      objectid: id,
    },
  });
}

/**
 *
 * @returns 对象-复制对象
 */
export async function copyObjapi(data: any) {
  return requestClient.post(`/object_hand/copy`, data);
}

/**
 *
 * @returns 作业-对象被作业使用清单
 */
export async function getObjWorkUseListApi(data: any) {
  return requestClient.post(`/works/list_pages`, data);
}

/**
 *
 * @returns 对象-提交防止记录重复、搜索布局
 */
export async function updateObjApi(data: any) {
  return requestClient.post(`/object/update`, data);
}

/**
 *
 * @returns 验证规则-列表
 */
export async function ruleListApi(data: any) {
  return requestClient.post(`/validation_rules/list`, data);
}

/**
 *
 * @returns 验证规则-删除
 */
export async function delRuleApi(id: string) {
  return requestClient.delete(`/validation_rules/${id}`);
}

/**
 *
 * @returns 验证规则-新增
 */
export async function addRuleApi(data: any) {
  return requestClient.post(`/validation_rules/save`, data);
}

/**
 *
 * @returns 验证规则-单条数据详情
 */
export async function ruleDetailApi(id: string) {
  return requestClient.get(`/validation_rules/${id}`);
}

/**
 *
 * @returns 数据筛选-列表
 */
export async function filterListApi(id: string) {
  return requestClient.post(`/filtering_conditions/object/${id}`);
}
