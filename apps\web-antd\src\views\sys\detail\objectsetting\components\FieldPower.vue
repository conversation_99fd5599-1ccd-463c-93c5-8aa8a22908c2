<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { Card, message, Pagination, Select, Table } from 'ant-design-vue';

import {
  getObjFieldPowerApi,
  getObjFieldPowerHeadApi,
  updateObjFieldPowerApi,
} from '#/api';

const route = useRoute();

const btnLoading = ref(false);
const dataSource = ref([]);
const btnTotal = ref(0);
const dataColumns = ref([
  {
    title: '角色名称',
    dataIndex: 'role_name',
    key: 'role_name',
  },
]);

// 下拉选项
const powerOpt = [
  { label: '可编辑', value: 'FieldPermissions_Permissions_Edit' },
  { label: '隐藏', value: 'FieldPermissions_Permissions_hide' },
  { label: '只读', value: 'FieldPermissions_Permissions_Readonly' },
  { label: '必填', value: 'FieldPermissions_Permissions_Required' },
];

const dataParams = reactive({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
});
// 获取字段权限表格
const getPowerData = async () => {
  btnLoading.value = true;
  const { data } = await getObjFieldPowerApi(dataParams);
  dataSource.value = data.data;
  btnTotal.value = data.total;
  btnLoading.value = false;
};
// 获取按钮列表
async function getBtnList() {
  btnLoading.value = true;
  const { data } = await getObjFieldPowerHeadApi(route.params.id);
  dataColumns.value = data;
  if (Array.isArray(data) && data.length > 0) {
    dataColumns.value = data.map((item) => {
      return {
        dataIndex: item.key,
        title: item.name,
        key: item.key,
        width: 120,
        align: 'center',
      };
    });
  }

  dataColumns.value.unshift({
    title: '角色名称',
    dataIndex: 'role_name',
    key: 'role_name',
    width: 120,
    fixed: 'left',
  });
  await getPowerData();
}

// 改变页数
async function pageChange(page, pageSize) {
  dataParams.page = page;
  dataParams.page_size = pageSize;
  getPowerData(); // 重新调用
}

// 更新数据
const powerUpdate = async (e, item) => {
  await updateObjFieldPowerApi({
    id: item.field_permission_id,
    FieldPermissions_Permissions: e,
  });
  message.success('修改成功');
};

onMounted(() => {
  getBtnList();
});
</script>

<template>
  <div>
    <Card title="字段权限">
      <Table
        :columns="dataColumns"
        :data-source="dataSource"
        :loading="btnLoading"
        :pagination="false"
        :scroll="{ y: 500 }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex !== 'role_name'">
            <Select
              v-model:value="record[column.dataIndex].field_permission"
              :options="powerOpt"
              @change="powerUpdate($event, record[column.dataIndex])"
            />
          </template>
        </template>
        <template #footer>
          <div class="text-right">
            <Pagination
              v-model:current="dataParams.page"
              v-model:page-size="dataParams.page_size"
              :show-size-changer="true"
              :show-total="(btnTotal) => `共 ${btnTotal} 条`"
              :total="btnTotal"
              @change="pageChange"
            />
          </div>
        </template>
      </Table>
    </Card>
  </div>
</template>

<style></style>
