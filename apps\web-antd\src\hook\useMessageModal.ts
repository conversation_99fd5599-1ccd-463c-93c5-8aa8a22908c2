import { ref } from 'vue';

import { Modal } from 'ant-design-vue';

interface ModalOptions {
  title?: string;
  content?: string;
  confirmText?: string;
  cancelText?: string;
  okType?: 'dashed' | 'default' | 'link' | 'primary' | 'text';
  cancelType?: 'dashed' | 'default' | 'link' | 'primary' | 'text';
  // 可以添加更多自定义配置项，比如是否可关闭等，根据实际需求扩展
  closable?: boolean;
  onConfirm?: () => void; // 新增确认按钮的回调函数
}

export function useMessageModal(options: ModalOptions) {
  // 用于存储模态框用户操作的结果，确认返回 true，取消返回 false
  const result = ref<boolean | null>(null);
  const {
    title = '提示',
    content = '',
    confirmText = '确定',
    cancelText = '取消',
    okType = 'primary',
    cancelType = 'default',
    closable = true,
    onConfirm = () => {}, // 默认为空函数
  } = options;

  // 打开模态框的函数，通过调用Ant Design Vue的Modal组件来弹出模态框
  const openModal = () => {
    const modalProps = {
      title,
      open: true,
      onOk: async () => {
        await onConfirm();
        result.value = false;
      },
      onCancel: () => {
        result.value = false;
      },
      okButtonProps: { type: okType },
      cancelButtonProps: { type: cancelType },
      closable,
      content,
    };
    Modal.confirm(modalProps);
  };

  return {
    result,
    openModal,
    title,
    content,
    confirmText,
    cancelText,
    okType,
    cancelType,
    closable,
  };
}
