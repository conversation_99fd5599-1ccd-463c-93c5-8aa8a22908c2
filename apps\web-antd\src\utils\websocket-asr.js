// WebSocket语音识别工具类
export class WebSocketASR {
  constructor() {
    this.socket = null;
    this.audioContext = null;
    this.processor = null;
    this.microphone = null;
    this.isRecording = false;
    this.isWaitingResult = false; // 新增：等待结果状态
    this.audioBuffer = [];
    this.sendInterval = null;
    this.onResult = null;
    this.onError = null;
    this.onStatusChange = null;
    this.onLoadingChange = null; // 新增：loading状态回调

    // 音频配置
    this.config = {
      SAMPLE_RATE: 16_000,
      CHANNELS: 1,
      CHUNK_DURATION_MS: 100,
      get SAMPLES_PER_CHUNK() {
        return Math.floor((this.SAMPLE_RATE * this.CHUNK_DURATION_MS) / 1000);
      },
      PROCESSOR_BUFFER_SIZE: 4096,
    };
  }

  // 检查浏览器环境
  checkBrowserEnvironment() {
    const issues = [];

    // 检查HTTPS
    const isHttps = window.location.protocol === 'https:';
    const isLocalhost =
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1';

    if (!isHttps && !isLocalhost) {
      issues.push('需要HTTPS协议或localhost环境');
    }

    // 检查浏览器API支持
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      issues.push('浏览器不支持麦克风API');
    }

    if (!window.WebSocket) {
      issues.push('浏览器不支持WebSocket');
    }

    return issues;
  }

  // 检查浏览器环境
  checkBrowserEnvironment() {
    const issues = [];

    // 检查HTTPS
    const isHttps = window.location.protocol === 'https:';
    const isLocalhost =
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1';

    if (!isHttps && !isLocalhost) {
      issues.push('需要HTTPS协议或localhost环境');
    }

    // 检查浏览器API支持
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      issues.push('浏览器不支持麦克风API');
    }

    if (!window.WebSocket) {
      issues.push('浏览器不支持WebSocket');
    }

    return issues;
  }

  // 检查麦克风权限状态
  async checkMicrophonePermission() {
    try {
      // 检查浏览器是否支持权限API
      if (!navigator.permissions) {
        console.warn('浏览器不支持权限API，将直接尝试获取麦克风权限');
        return 'prompt'; // 假设需要用户授权
      }

      const permission = await navigator.permissions.query({
        name: 'microphone',
      });
      return permission.state; // 'granted', 'denied', 'prompt'
    } catch (error) {
      console.warn('权限检查失败:', error);
      return 'prompt'; // 默认假设需要用户授权
    }
  }

  // 检查麦克风权限状态
  async checkMicrophonePermission() {
    try {
      // 检查浏览器是否支持权限API
      if (!navigator.permissions) {
        console.warn('浏览器不支持权限API，将直接尝试获取麦克风权限');
        return 'prompt'; // 假设需要用户授权
      }

      const permission = await navigator.permissions.query({
        name: 'microphone',
      });
      return permission.state; // 'granted', 'denied', 'prompt'
    } catch (error) {
      console.warn('权限检查失败:', error);
      return 'prompt'; // 默认假设需要用户授权
    }
  }

  // 关闭WebSocket连接
  closeWebSocket() {
    if (this.socket) {
      if (
        this.socket.readyState === WebSocket.OPEN ||
        this.socket.readyState === WebSocket.CONNECTING
      ) {
        this.socket.close();
      }
      this.socket = null;
    }
  }

  // 清理资源
  destroy() {
    this.stopRecording();
    this.isWaitingResult = false;
    this.updateLoadingState(false);
    this.closeWebSocket();
  }

  // 获取WebSocket URL（根据vite配置动态生成）
  getWebSocketUrl() {
    // 从当前页面的host获取，这样会自动使用vite代理配置
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    return `${protocol}//${window.location.host}/basic-api/ai/asr`;
  }

  // 错误处理
  handleError(message) {
    if (this.onError) {
      this.onError(message);
    }
  }

  // 处理WebSocket消息
  handleSocketMessage(data) {
    switch (data.status) {
      case 'event': {
        // 只处理 status 为 'event' 的消息内容
        if (this.onResult && data.message) {
          this.onResult(data.message);
        }
        break;
      }
      case 'complete': {
        // 识别完成，停止loading状态并关闭连接
        this.isWaitingResult = false;
        this.updateLoadingState(false);
        this.updateStatus('识别完成');
        // 关闭WebSocket连接，为下次录音做准备
        this.closeWebSocket();
        break;
      }
      case 'error': {
        // 错误处理
        this.isWaitingResult = false;
        this.updateLoadingState(false);
        this.handleError(`识别错误: ${data.message}`);
        // 发生错误时也关闭连接
        this.closeWebSocket();
        break;
      }
      case 'status': {
        this.updateStatus(data.message);
        break;
      }
      default: {
        // 忽略其他状态消息
        if (import.meta.env.DEV) {
          // eslint-disable-next-line no-console
          console.log('收到WebSocket消息:', data);
        }
        break;
      }
    }
  }

  // 初始化WebSocket连接
  initWebSocket() {
    // 先关闭现有连接，确保每次都是新连接
    this.closeWebSocket();

    return new Promise((resolve, reject) => {
      try {
        this.socket = new WebSocket(this.getWebSocketUrl());

        this.socket.addEventListener('open', () => {
          this.updateStatus('WebSocket连接成功');
          resolve();
        });

        this.socket.addEventListener('close', () => {
          this.updateStatus('WebSocket连接已断开');
          if (this.sendInterval) {
            clearInterval(this.sendInterval);
            this.sendInterval = null;
          }
        });

        this.socket.addEventListener('error', (error) => {
          this.handleError(`WebSocket连接错误: ${error.message}`);
          reject(error);
        });

        this.socket.addEventListener('message', (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleSocketMessage(data);
          } catch (error) {
            console.error('WebSocket消息解析错误:', error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // 发送音频数据块
  sendAudioChunk() {
    if (
      !this.isRecording ||
      this.audioBuffer.length < this.config.SAMPLES_PER_CHUNK
    ) {
      return;
    }

    // 获取足够时长的音频数据
    const chunk = this.audioBuffer.slice(0, this.config.SAMPLES_PER_CHUNK);
    this.audioBuffer = this.audioBuffer.slice(this.config.SAMPLES_PER_CHUNK);

    // 发送PCM数据
    const pcmData = new Int16Array(chunk);
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(pcmData.buffer);
    }
  }

  // 设置回调函数
  setCallbacks({ onResult, onError, onStatusChange, onLoadingChange }) {
    this.onResult = onResult;
    this.onError = onError;
    this.onStatusChange = onStatusChange;
    this.onLoadingChange = onLoadingChange;
  }

  // 显示权限引导对话框
  showPermissionGuide() {
    const isHttps = window.location.protocol === 'https:';
    const isLocalhost =
      window.location.hostname === 'localhost' ||
      window.location.hostname === '127.0.0.1';

    let message = '麦克风权限被拒绝，请按以下步骤操作：\n\n';

    if (!isHttps && !isLocalhost) {
      message += '⚠️ 重要：麦克风功能需要HTTPS协议或localhost环境\n\n';
    }

    message += '1. 点击地址栏左侧的🔒或ℹ️图标\n';
    message += '2. 找到"麦克风"或"录音"权限设置\n';
    message += '3. 选择"允许"或"总是允许"\n';
    message += '4. 刷新页面后重试\n\n';
    message += '如果仍有问题，请检查：\n';
    message += '• 浏览器是否支持麦克风功能\n';
    message += '• 麦克风设备是否正常工作\n';
    message += '• 是否有其他应用占用麦克风';

    return message;
  }

  // 开始录音
  async startRecording() {
    if (this.isRecording) return;

    try {
      // 检查浏览器环境
      const envIssues = this.checkBrowserEnvironment();
      if (envIssues.length > 0) {
        throw new Error(`浏览器环境不支持：${envIssues.join(', ')}`);
      }

      this.updateStatus('正在检查麦克风权限...');

      // 检查权限状态
      const permissionState = await this.checkMicrophonePermission();

      if (permissionState === 'denied') {
        this.showPermissionGuide();
        throw new Error('麦克风权限被拒绝，请在浏览器设置中允许麦克风权限');
      }

      this.updateStatus('正在启动麦克风...');

      // 初始化WebSocket连接
      await this.initWebSocket();

      // 获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16_000,
          echoCancellation: false,
          noiseSuppression: false,
          autoGainControl: false,
        },
        video: false,
      });

      // 初始化音频上下文
      this.audioContext = new (window.AudioContext ||
        window.webkitAudioContext)({
        sampleRate: this.config.SAMPLE_RATE,
      });

      // 创建音频处理节点
      this.microphone = this.audioContext.createMediaStreamSource(stream);
      this.processor = this.audioContext.createScriptProcessor(
        this.config.PROCESSOR_BUFFER_SIZE,
        this.config.CHANNELS,
        this.config.CHANNELS,
      );

      // 重置状态
      this.audioBuffer = [];

      // 音频处理回调
      this.processor.onaudioprocess = (e) => {
        if (!this.isRecording) return;

        // 获取音频数据并转换为16位PCM
        const inputData = e.inputBuffer.getChannelData(0);
        const pcmData = new Int16Array(inputData.length);

        for (const [i, inputDatum] of inputData.entries()) {
          pcmData[i] = Math.max(-32_768, Math.min(32_767, inputDatum * 32_767));
        }

        // 添加到缓冲区
        this.audioBuffer.push(...pcmData);
      };

      // 连接处理节点
      this.microphone.connect(this.processor);
      this.processor.connect(this.audioContext.destination);

      // 设置定时发送
      this.sendInterval = setInterval(() => {
        this.sendAudioChunk();
      }, this.config.CHUNK_DURATION_MS);

      this.isRecording = true;
      this.updateStatus('录音中...');

      return true;
    } catch (error) {
      // 详细的错误处理
      let errorMessage = '录音启动失败';

      switch (error.name) {
        case 'NotAllowedError':
        case 'PermissionDeniedError': {
          errorMessage = '麦克风权限被拒绝';
          this.showPermissionGuide();

          break;
        }
        case 'NotFoundError':
        case 'DevicesNotFoundError': {
          errorMessage = '未找到麦克风设备，请检查设备连接';

          break;
        }
        case 'NotReadableError':
        case 'TrackStartError': {
          errorMessage = '麦克风被其他应用占用，请关闭其他录音应用后重试';

          break;
        }
        case 'OverconstrainedError':
        case 'ConstraintNotSatisfiedError': {
          errorMessage = '麦克风不支持所需的音频格式';

          break;
        }
        case 'NotSupportedError': {
          errorMessage = '浏览器不支持录音功能';

          break;
        }
        case 'AbortError': {
          errorMessage = '录音请求被中断';

          break;
        }
        default: {
          errorMessage = `录音启动失败: ${error.message}`;
        }
      }

      this.handleError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  // 停止录音
  stopRecording() {
    if (!this.isRecording) return;

    this.isRecording = false;

    // 清理定时器
    if (this.sendInterval) {
      clearInterval(this.sendInterval);
      this.sendInterval = null;
    }

    // 发送剩余缓冲区数据
    if (this.audioBuffer.length > 0) {
      const pcmData = new Int16Array(this.audioBuffer);
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        this.socket.send(pcmData.buffer);
      }
      this.audioBuffer = [];
    }

    // 断开音频节点
    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }
    if (this.microphone) {
      this.microphone.disconnect();
      this.microphone = null;
    }
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    // 设置等待结果状态
    this.isWaitingResult = true;
    this.updateLoadingState(true);
    this.updateStatus('正在处理语音识别结果...');
  }

  // 更新loading状态
  updateLoadingState(isLoading) {
    if (this.onLoadingChange) {
      this.onLoadingChange(isLoading);
    }
  }

  // 状态更新
  updateStatus(message) {
    if (this.onStatusChange) {
      this.onStatusChange(message);
    }
  }
}
