<!-- eslint-disable no-use-before-define -->
<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';

import {
  VbenButton,
  VbenIconButton,
  VbenPopover,
  VbenScrollbar,
} from '@vben/common-ui';
import { Bell, Icon, MailCheck } from '@vben/icons';
import { $t } from '@vben/locales';
import { useUserStore } from '@vben/stores';

import { useToggle } from '@vueuse/core';
import { Button, message, Spin } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  batchMessageApi,
  getUnreadMessagesApi,
  markMessageApi,
  websocketStatusApi,
} from '#/api';
import { useWebSocket } from '#/hook';

const userStore = useUserStore();
const [open, toggle] = useToggle();

const notifications = ref<any[]>([]);
const dataType = ref('全部通知');

async function changeType() {
  if (dataType.value === '全部通知') {
    dataType.value = '未读通知';
    getNotifications(0);
    return;
  }
  if (dataType.value === '未读通知') {
    dataType.value = '全部通知';
    getNotifications();
  }
}

const dot = computed(() => {
  return notifications.value.some((item) => !item.message_status);
});

// 标记全部已读
async function handleMakeAll() {
  await batchMessageApi({
    message_status: 1,
    user_id: userStore.userInfo?.userId,
  });
  message.success('操作成功');
  page.value = 1;
  notifications.value = [];
  getNotifications();
}

function handleClick(id: string) {
  notifications.value.forEach((item) => {
    if (item.id === id) {
      item.message_status = true;
    }
  });
  markMessageApi({
    id,
    message_status: 1,
  });
}

// 鼠标经过判断是否显示
function handleMouseEnter(id: string) {
  notifications.value.forEach((item) => {
    if (item.id === id) {
      item.showDeleted = true;
    }
  });
}

function handleMouseLeave(id: string) {
  notifications.value.forEach((item) => {
    if (item.id === id) {
      item.showDeleted = false;
    }
  });
}

// 删除
async function handleDelete(id: string) {
  notifications.value.splice(
    notifications.value.findIndex((item) => item.id === id),
    1,
  );

  await batchMessageApi({
    message_id: id,
    user_id: userStore.userInfo?.userId,
  });
  message.success('删除成功');
}

// 清空所有已读信息
async function handleClear() {
  await batchMessageApi({
    is_deleted: 1,
    message_status: 1,
    user_id: userStore.userInfo?.userId,
  });
  message.success('操作成功');
  page.value = 1;
  notifications.value = [];
  getNotifications();
}

const page = ref(1);
const total = ref(0);

// 获取消息通知列表
async function getNotifications(message_status: number = 1) {
  let dataParams = {};
  dataParams =
    message_status === 1
      ? {
          page: page.value,
          page_size: 10,
          user_id: userStore.userInfo?.userId,
        }
      : {
          message_status,
          page: page.value,
          page_size: 10,
          user_id: userStore.userInfo?.userId,
        };
  const { data } = await getUnreadMessagesApi(dataParams);
  const dataList = data.data.map((item: any) => {
    return {
      ...item,
      showDeleted: false,
    };
  });
  total.value = data.total;
  if (dataList.length === 0) {
    page.value -= 1;
  }

  notifications.value = [...notifications.value, ...dataList];
  spinloading.value = false;
}

const spinloading = ref(false);
function scrollAt(value: any) {
  if (spinloading.value) return; // 上一次请求没有完成直接跳出
  if (value.bottom) {
    if (notifications.value.length >= total.value) return;
    spinloading.value = true;
    page.value += 1;
    if (dataType.value === '全部通知') {
      getNotifications();
    } else {
      getNotifications(0);
    }
  }
}

watch(open, (newValue) => {
  if (newValue) {
    page.value = 1;
    notifications.value = [];
    getNotifications();
  }
});

onMounted(() => {
  getNotifications();
  verifyWebsocket();
});

// websocket连接
async function verifyWebsocket() {
  try {
    const { code } = await websocketStatusApi();
    if (code === 200) {
      // 认证通过
      useWebSocket({
        url: `/basic-api/backlog_hand/ws_ways_two?user_id=${userStore.userInfo?.userId}`,
        onMessage: (data) => {
          notifications.value = [data, ...notifications.value];
          // 在这里处理接收到的消息
        },
        onOpen: () => {
          console.warn('通知消息WebSocket 连接成功');
        },
        onClose: () => {
          console.warn('通知消息WebSocket 连接关闭');
        },
        onError: (error) => {
          console.error('通知消息WebSocket 错误:', error);
        },
        reconnectLimit: 10, // 最大重连次数
        reconnectInterval: 10_000, // 重连间隔10秒
        heartBeatInterval: 35_000, // 心跳间隔35秒
        heartBeatMessage: {
          // 自定义心跳消息
          userid: userStore.userInfo?.userId,
          access_token: `Bearer ${userStore.userInfo?.token}`,
        },
      });
    }
  } catch {}
}
</script>

<template>
  <VbenPopover
    v-model:open="open"
    content-class="relative right-2 w-[360px] p-0"
  >
    <template #trigger>
      <div class="flex-center mr-2 h-full" @click.stop="toggle()">
        <VbenIconButton class="bell-button text-foreground relative">
          <span
            v-if="dot"
            class="bg-primary absolute right-0.5 top-0.5 h-2 w-2 rounded"
          ></span>
          <Bell class="size-4" />
        </VbenIconButton>
      </div>
    </template>

    <div class="relative">
      <div class="flex items-center justify-between p-4 py-3">
        <!-- <div class="text-foreground">{{ $t('widgets.notifications') }}</div> -->
        <Button class="flex-center gap-1" type="primary" @click="changeType">
          <Icon class="size-4" icon="material-symbols:compare-arrows-rounded" />
          <span>{{ dataType }}</span>
        </Button>
        <VbenIconButton
          :disabled="notifications.length <= 0"
          :tooltip="$t('widgets.markAllAsRead')"
          @click="handleMakeAll"
        >
          <MailCheck class="size-4" />
        </VbenIconButton>
      </div>
      <VbenScrollbar v-if="notifications.length > 0" @scroll-at="scrollAt">
        <ul class="!flex max-h-[400px] w-full flex-col">
          <template v-for="item in notifications" :key="item.title">
            <li
              :class="
                item.message_status
                  ? 'hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t bg-[#f4f4f5] px-3 py-3'
                  : 'hover:bg-accent border-border relative flex w-full cursor-pointer items-start gap-5 border-t px-3 py-3'
              "
              @click="handleClick(item.id)"
              @mouseenter="handleMouseEnter(item.id)"
              @mouseleave="handleMouseLeave(item.id)"
            >
              <span
                v-if="!item.message_status"
                class="bg-primary absolute right-2 top-2 h-2 w-2 rounded"
              >
              </span>
              <Icon
                v-if="item.showDeleted && item.message_status"
                class="absolute right-2 top-2 size-5 text-[red]"
                icon="ic:baseline-close"
                @click="handleDelete(item.id)"
              />
              <div class="flex flex-col gap-1">
                <div class="flex items-center gap-1">
                  <Icon class="size-5" icon="gg:loadbar-doc" />
                  <span class="font-bold">
                    {{
                      `${
                        item.message_type === '2'
                          ? '抄送类'
                          : item.message_type === '1'
                            ? '审批类'
                            : item.message_type
                      }消息通知`
                    }}
                  </span>
                </div>
                <p class="text-muted-foreground my-1 line-clamp-2 text-xs">
                  {{ item.message_content }}
                </p>
                <p class="text-muted-foreground line-clamp-2 text-xs">
                  {{
                    dayjs(item.message_send_time).format('YYYY-MM-DD HH:mm:ss')
                  }}
                </p>
              </div>
            </li>
          </template>
          <li v-if="spinloading" class="flex-center gap-1">
            <Spin size="small" />
            <span class="text-muted-foreground text-xs">加载中</span>
          </li>
          <li v-else class="flex-center gap-1 py-2">
            <span class="text-muted-foreground text-xs">已加载全部</span>
          </li>
        </ul>
      </VbenScrollbar>

      <template v-else>
        <div class="flex-center text-muted-foreground min-h-[150px] w-full">
          {{ $t('common.noData') }}
        </div>
      </template>

      <div
        class="border-border flex items-center justify-between border-t px-4 py-3"
      >
        <VbenButton
          :disabled="notifications.length <= 0"
          size="sm"
          variant="ghost"
          @click="handleClear"
        >
          {{ $t('widgets.clearNotifications') }}
        </VbenButton>
        <!-- <VbenButton size="sm" @click="handleViewAll">
          {{ $t('widgets.viewAll') }}
        </VbenButton> -->
      </div>
    </div>
  </VbenPopover>
</template>

<style scoped>
:deep(.bell-button) {
  &:hover {
    svg {
      animation: bell-ring 1s both;
    }
  }
}

@keyframes bell-ring {
  0%,
  100% {
    transform-origin: top;
  }

  15% {
    transform: rotateZ(10deg);
  }

  30% {
    transform: rotateZ(-10deg);
  }

  45% {
    transform: rotateZ(5deg);
  }

  60% {
    transform: rotateZ(-5deg);
  }

  75% {
    transform: rotateZ(2deg);
  }
}
</style>
