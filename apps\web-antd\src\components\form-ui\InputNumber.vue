<script setup>
import { ref, watch } from 'vue';

import { InputNumber } from 'ant-design-vue';

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const inputValue = ref(props.modelValue);

const changeValue = () => {
  emits('update:modelValue', inputValue.value);
};
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue;
  },
);
</script>

<template>
  <InputNumber
    v-model:value="inputValue"
    :disabled="props.parameters.field_readonly"
    :placeholder="props.parameters.Field_Help_Text"
    type="text"
    @change="changeValue"
  />
</template>

<style></style>
