<script setup>
import { nextTick, onMounted, ref, toRefs, watch } from 'vue';
import { Bubble } from 'vue-element-plus-x';

import { Icon } from '@vben/icons';

import { Button, Timeline, TimelineItem } from 'ant-design-vue';

import AudioCustom from './AudioCustom.vue';

const props = defineProps({
  currentTelephoneRecord: {
    type: Object,
    default: () => null,
  },
  discernResult: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(['endCall']);

const { currentTelephoneRecord, discernResult } = toRefs(props);

// 下载音频函数
const downloadAudio = async (audioUrl) => {
  if (!audioUrl) return;

  try {
    // 使用 fetch 获取音频文件
    const response = await fetch(audioUrl);
    if (!response.ok) {
      throw new Error(`网络响应失败: ${response.status}`);
    }
    // 获取二进制数据
    const blob = await response.blob();
    // 创建临时 URL
    const url = URL.createObjectURL(blob);

    // 创建一个 a 标签
    const a = document.createElement('a');
    a.href = url;
    // 设置下载后的文件名，可根据实际情况修改
    a.download = `call_record_${Date.now()}.mp3`;
    // 模拟点击 a 标签触发下载
    a.click();

    // 释放临时 URL
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('下载音频失败:', error);
    // 可以添加提示信息，告知用户下载失败
  }
};

const loading = ref(false);
const handleSessionEnded = async (options) => {
  emits('endCall', options);
};
// 回拨号码
const reCall = async () => {
  if (loading.value) return;
  try {
    await window.tccc.Call.startOutboundCall({
      phoneNumber: currentTelephoneRecord.value._Callee,
    });
    // 关键：在呼叫成功后立即绑定事件监听
    window.tccc.on('sessionEnded', handleSessionEnded);
    loading.value = true;
  } catch (error) {
    message.error(`呼出失败：${error.message}`);
  } finally {
    loading.value = false;
  }
};

const summaryText = ref(null);
const agreementText = ref(null);

// 总结加载中
const summaryLoading = ref(false);
const handSummary = () => {
  summaryText.value = 1;
  summaryLoading.value = true;
  setTimeout(() => {
    summaryLoading.value = false;
  }, 2000);
};

// 协议书加载中
const agreementLoading = ref(false);
const handAgreement = () => {
  agreementText.value = 1;
  agreementLoading.value = true;
  setTimeout(() => {
    agreementLoading.value = false;
  }, 2000);
};

const scrollContainer = ref(null);

// 滚动到最底部的函数
const scrollToBottom = () => {
  nextTick(() => {
    if (scrollContainer.value) {
      scrollContainer.value.scrollTo({
        top: scrollContainer.value.scrollHeight,
        behavior: 'smooth', // 可选平滑滚动
      });
    }
  });
};

onMounted(() => {
  scrollToBottom();
});

// 监听 recordedChunks 变化，有变化时滚动到最底部
watch(
  discernResult,
  () => {
    scrollToBottom();
  },
  { deep: true },
);
</script>

<template>
  <div
    v-if="discernResult.length > 0"
    class="flex flex-col gap-2 overflow-y-auto"
  >
    <div class="border-b p-4 text-lg font-bold">当前正在通话</div>
    <div
      ref="scrollContainer"
      class="flex max-h-[900px] flex-col gap-2 overflow-y-auto"
    >
      <Timeline class="p-3">
        <TimelineItem v-for="(item, index) in discernResult" :key="index">
          <template #dot>
            <div class="bg-theme-color-opacity rounded-full p-1.5">
              <Icon class="size-4" icon="fluent:call-connecting-20-filled" />
            </div>
          </template>
          <div class="flex">
            <p class="font-bold opacity-[0.8]">座席</p>
            <p class="ml-1 opacity-[0.6]">2025-05-05 13:12:11</p>
          </div>
          <div
            class="card-box my-2 flex items-center rounded-md p-5 text-[rgba(0,0,0,0.5)]"
          >
            {{ item.text }}
          </div>

          <div v-if="item.result.length > 0" class="mt-3 flex gap-2">
            <div class="flex flex-col">
              <div
                v-for="(_item, _index) in item.result"
                :key="_item.id"
                class="flex flex-col gap-2"
              >
                <div class="mt-1 flex items-center gap-1">
                  <a class="color-theme-color" href="#">
                    {{ `${_index + 1}-${_item.name}` }}
                  </a>
                  <Icon class="color-theme-color size-4" icon="ep:right" />
                </div>
                <div class="card-box bg-theme-color-opacity p-2">
                  {{ _item.content }}
                </div>
              </div>
            </div>
          </div>
        </TimelineItem>
      </Timeline>
    </div>
  </div>
  <div
    v-else-if="currentTelephoneRecord"
    class="container-grid grid h-full w-full"
  >
    <div class="row-span-2 flex items-center justify-between border-b px-4">
      <div class="flex flex-col gap-2">
        <span class="text-lg font-bold">
          {{ currentTelephoneRecord?._Callee.slice(4) }}
        </span>
        <span class="opacity-[0.5]">
          主叫号码：{{ currentTelephoneRecord?._Caller.slice(4) }}&nbsp;
          归属地：
          {{
            currentTelephoneRecord?._CallerLocation
              ? currentTelephoneRecord?._CallerLocation
              : '未知'
          }}&nbsp; 呼叫类型：
          {{ currentTelephoneRecord?._Direction ? '呼出' : '呼入' }}
        </span>
      </div>
      <div class="button-edit flex items-center gap-2">
        <!-- 绑定下载函数，假设音频地址在 currentTelephoneRecord._AudioUrl -->
        <span
          class="color-theme-color cursor-pointer"
          @click="downloadAudio(currentTelephoneRecord._RecordURL)"
        >
          下载音频
        </span>
        <div class="flex cursor-pointer items-center" @click="reCall">
          <Icon
            class="mr-1 size-5 opacity-[0.5]"
            icon="hugeicons:call-missed-04"
          />
          <span>回拨号码</span>
        </div>
      </div>
    </div>
    <div class="row-span-11 max-h-[605px] overflow-y-auto p-4">
      <Timeline class="p-3">
        <TimelineItem>
          <template #dot>
            <div class="bg-theme-color-opacity rounded-full p-1.5">
              <Icon class="size-4" icon="fluent:call-connecting-20-filled" />
            </div>
          </template>
          <div class="flex">
            <p class="font-bold opacity-[0.8]">座席</p>
            <p class="ml-1 opacity-[0.6]">2025-05-05 13:12:11</p>
          </div>
          <div
            class="card-box my-2 flex items-center rounded-md p-5 text-[rgba(0,0,0,0.5)]"
          >
            您好，请问有什么可以帮到您？
          </div>
          <br />
        </TimelineItem>
        <TimelineItem>
          <template #dot>
            <div class="bg-theme-color-opacity rounded-full p-1.5">
              <Icon class="size-4" icon="fluent:call-connecting-20-filled" />
            </div>
          </template>
          <div class="flex">
            <p class="font-bold opacity-[0.8]">用户</p>
            <p class="ml-1 opacity-[0.6]">2025-05-05 13:12:11</p>
          </div>
          <div
            class="bg-theme-color-opacity card-box my-2 flex items-center rounded-md p-5 text-[rgba(0,0,0,0.5)]"
          >
            请问xxxx功能怎么实现
          </div>
        </TimelineItem>
      </Timeline>

      <!-- 生成的总结 -->
      <div v-if="summaryText" class="flex w-full flex-col gap-2">
        <span class="text-xl font-bold">总结</span>
        <Bubble
          :loading="summaryLoading"
          class="mb-3 w-full"
          content="此次通话中，座席礼貌问候并询问需求，用户咨询 xxxx 功能实现方法。双方交流及时，问答清晰。后续可针对该功能需求，为用户提供详细的操作指南或解决方案，以提升用户满意度。"
          styles="width: 100%;"
          typing
        />
      </div>
      <!-- 生成的调解书 -->
      <div v-if="agreementText" class="flex w-full flex-col gap-2">
        <span class="text-xl font-bold">调解协议书</span>
        <Bubble
          :loading="agreementLoading"
          class="bubble-full-width mb-3 w-full"
          content="### 调解协议书

          甲方（座席代表）：[公司名称]
          乙方（用户）：[用户姓名]

          鉴于乙方于 [通话时间] 咨询甲方关于 xxxx 功能的实现方法，经双方友好沟通，达成如下调解协议：

          1. **服务承诺**：甲方承诺在 [承诺时间] 内，为乙方提供详细的 xxxx 功能操作指南，包括但不限于文字说明、截图示例或视频教程。
          2. **后续跟进**：甲方将在提供操作指南后的 [跟进时间] 内，主动联系乙方确认其是否成功实现 xxxx 功能，并解答乙方在操作过程中遇到的问题。
          3. **双方义务**：双方应本着友好合作的原则，积极履行本协议约定的各项义务。

          本协议自双方达成一致意见时生效，一式两份，甲乙双方各执一份，具有同等法律效力。

          甲方（盖章/签字）：[座席签字]
          乙方（签字）：[用户签字]
          日期：[协议签订日期]"
          styles="width: 100%; box-sizing: border-box;"
          typing
        />
      </div>
    </div>
    <div class="relative row-span-2 border-t px-5">
      <div class="absolute right-4 top-[-50px]">
        <div class="flex h-[50px] items-center gap-2">
          <Button
            :disabled="summaryLoading"
            class="bg-theme-color-opacity flex cursor-pointer items-center gap-[2px] rounded-md p-1 font-bold"
            @click="handSummary"
          >
            <Icon icon="mdi:eye-outline" />
            查看总结
          </Button>
          <Button
            :disabled="agreementLoading"
            class="bg-theme-color-opacity flex cursor-pointer items-center gap-[2px] rounded-md p-1 font-bold"
            @click="handAgreement"
          >
            <Icon class="color-theme-color" icon="mingcute:ai-fill" />
            <!-- 应用彩虹文字类 -->
            <span class="rainbow-text">生成调解协议书</span>
          </Button>
        </div>
      </div>
      <AudioCustom :record-url="currentTelephoneRecord?._RecordURL" />
    </div>
  </div>

  <div
    v-else
    class="flex h-full items-center justify-center text-[38px] font-bold text-[rgba(0,0,0,0.5)]"
  >
    当前没有通话详情
  </div>
</template>

<style scoped>
.container-grid {
  grid-template-rows: repeat(15, minmax(0, 1fr));
}

/* 定义彩虹文字样式 */
.rainbow-text {
  color: transparent;
  background: linear-gradient(
    to right,
    hsl(0deg 70% 70%),
    /* 较温和的红色 */ hsl(30deg 70% 70%),
    /* 较温和的黄色 */ hsl(120deg 70% 70%),
    /* 较温和的绿色 */ hsl(240deg 70% 70%),
    /* 较温和的蓝色 */ hsl(270deg 70% 70%),
    /* 较温和的靛色 */ hsl(300deg 70% 70%) /* 较温和的紫色 */
  );
  background-clip: text;
}

/* 确保 Bubble 组件占满宽度 */
.bubble-full-width {
  box-sizing: border-box;
  display: block;
  width: 100%;
}
</style>
