<!-- eslint-disable no-use-before-define -->
<script setup>
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

import { Icon } from '@vben/icons';

import {
  Button,
  Input,
  InputSearch,
  message,
  Spin,
  Tooltip,
} from 'ant-design-vue';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

import {
  createTableDataApi,
  getPhoneRecordApi,
  getTableDataApi,
  getTableRelationValueApi,
} from '#/api';
import { TElephoneStatus } from '#/enums';

const props = defineProps({
  endData: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['getCurrentCallRecord']);

// 引入 relativeTime 插件

dayjs.extend(relativeTime);

const telephoneNumber = ref(null);

const active = ref(0);
const loading = ref(false);

// 获取列表加载
const loadingList = ref(false);

const currentCallResult = ref(null);
const isWaitingForResult = ref(false);

// 秒转换为 00:00 格式的函数
const formatSeconds = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${String(minutes).padStart(2, '0')}:${String(remainingSeconds).padStart(2, '0')}`;
};
// 归属地
const calleeLocation = ref('');
// 加载最新记录
const newLoading = ref(false);

watch(
  () => props.endData,
  (newData) => {
    telephoneNumber.value = newData?.calleePhoneNumber?.slice(4);

    handleSessionEnded(newData);
  },
  { deep: true }, // 深度监听
);
const handleSessionEnded = async (options) => {
  newLoading.value = true;
  // console.log('通话结束事件触发', options);
  // message.success('通话已结束');
  // 记录归属地信息
  if (options?.calleeLocation) {
    calleeLocation.value = options.calleeLocation;
  }
  // 重置等待状态
  isWaitingForResult.value = false;

  // 延迟获取记录（根据实际情况调整延迟时间）
  const delay = options.Duration > 30 ? 5000 : 3000; // 长通话延迟更久
  await new Promise((resolve) => setTimeout(resolve, delay));

  // 获取最新记录（带重试机制）
  await fetchWithRetry();
};

// 带重试机制的获取记录函数
const fetchWithRetry = async (retryCount = 0) => {
  try {
    await fetchLatestCallRecord();
  } catch {
    if (retryCount < 3) {
      // message.warning(`正在重试获取记录 (${retryCount + 1}/3)...`);
      await new Promise((resolve) => setTimeout(resolve, 2000));
      await fetchWithRetry(retryCount + 1);
    } else {
      newLoading.value = false;
      message.error('获取通话记录失败，请稍后手动刷新');
    }
  }
};

// 获取最新的通话记录
const fetchLatestCallRecord = async () => {
  try {
    // 添加时间范围限制（近5分钟）
    const now = Math.floor(Date.now() / 1000);

    const isPhoneNumber = `0086${telephoneNumber.value}`;
    const params = {
      StartTimeStamp: now - 300, // 5分钟前
      EndTimeStamp: now,
      PageSize: 1,
      PageNumber: 0,
      Phones: [isPhoneNumber],
    };
    const { data } = await getPhoneRecordApi(params);
    if (!data._TelCdrList || data._TelCdrList.length === 0) {
      newLoading.value = false;
      throw new Error('记录尚未生成');
    } else {
      const latestRecord = data._TelCdrList[0];
      if (latestRecord) {
        let location;
        if (calleeLocation.value && calleeLocation.value !== '') {
          location = calleeLocation.value;
        } else if (
          latestRecord._CallerLocation &&
          latestRecord._CallerLocation !== ''
        ) {
          location = latestRecord._CallerLocation;
        } else {
          location = '未知';
        }

        const dataParams = ref({
          Callee: latestRecord?._Callee,
          Caller: latestRecord?._Caller,
          CallerLocation: location,
          Direction: latestRecord?._Direction,
          Duration: formatSeconds(latestRecord?._Duration),
          EndStatusString: latestRecord?._EndStatusString,
          EndStatusStringName: TElephoneStatus[latestRecord?._EndStatusString],
          RecordURL: latestRecord?._RecordURL,
        });
        await createTableDataApi('call_record', dataParams.value);
        getCallRecord();
      } else {
        newLoading.value = false;
        message.warning('未找到最新通话记录');
      }
    }
  } catch (error) {
    newLoading.value = false;
    message.error(`获取通话记录失败: ${error.message}`);
  }
};

const handleCallout = async () => {
  const pattern = /^1[3-9]\d{9}$/;
  const isPhoneNumber = pattern.test(telephoneNumber.value);
  if (!isPhoneNumber) return message.error('请输入正确的手机号！');
  if (loading.value) {
    return;
  }
  loading.value = true;
  isWaitingForResult.value = true;
  currentCallResult.value = null; // 重置当前结果
  try {
    await window.tccc.Call.startOutboundCall({
      phoneNumber: telephoneNumber.value,
    });
    // 关键：在呼叫成功后立即绑定事件监听
    window.tccc.on('sessionEnded', handleSessionEnded);
  } catch (error) {
    message.error(`呼出失败：${error.message}`);
    isWaitingForResult.value = false;
  } finally {
    loading.value = false;
  }
};
// =================================================获取通话列表开始============================
const callRecordList = ref([]);
const dataParams = ref({
  page: 1,
  page_size: 10,
  keywords: null,
});
// 新增状态
const hasMore = ref(true); // 是否还有更多数据
const currentPage = ref(1); // 当前页码

// 获取通话记录
// 获取列表数据
const getTable = async (keyword, isAppend = false) => {
  try {
    if (!loadingList.value && !newLoading.value) {
      loadingList.value = true;
    }
    dataParams.value.keywords = keyword;
    if (!isAppend) {
      callRecordList.value = [];
      currentPage.value = 1;
    }
    dataParams.value.page = isAppend ? currentPage.value : 1;
    let res;
    if (keyword) {
      const { data } = await getTableRelationValueApi(
        'call_record',
        dataParams.value,
      );
      res = data;
    } else {
      const { data } = await getTableDataApi('call_record', dataParams.value);
      res = data;
    }
    const processedData = res?.data || [];
    if (processedData.length > 0) {
      const newData = processedData.map((item) => {
        const fullDateStr = `${item.CreatedDate} +08:00`;
        const timestampMs = new Date(fullDateStr).getTime();
        const timestampSec = Math.floor(timestampMs / 1000);
        return {
          ...item,
          lastTimestamp: timestampSec,
          type: item?.Direction?.id,
        };
      });

      if (isAppend) {
        callRecordList.value.push(...newData);
      } else {
        callRecordList.value = newData;
        nextTick(() => {
          chooseItem(callRecordList.value[0], 0);
        });
      }
      // 仅当数据量等于 page_size 时才递增页码
      if (processedData.length === dataParams.value.page_size) {
        currentPage.value += 1;
      }
      hasMore.value = processedData.length === dataParams.value.page_size;
    } else {
      hasMore.value = false;
    }

    loadingList.value = false;
    newLoading.value = false;
  } catch (error) {
    loadingList.value = false;
    newLoading.value = false;
    hasMore.value = false;
    console.error('加载失败:', error);
  }
};
const scrollContainer = ref(null);
const isFetching = ref(false); // 控制加载状态
let scrollTimeout = null;

const handleScroll = async () => {
  try {
    loadingList.value = true;
    // 防抖：200ms 内只触发一次
    if (scrollTimeout) clearTimeout(scrollTimeout);
    scrollTimeout = setTimeout(async () => {
      const container = scrollContainer.value;
      if (!container) return;

      const scrollTop = container.scrollTop;
      const clientHeight = container.clientHeight;
      const scrollHeight = container.scrollHeight;

      if (scrollHeight - scrollTop - clientHeight < 100 && hasMore.value) {
        isFetching.value = true;
        await getTable(dataParams.value.keywords, true);
        isFetching.value = false;
      }
    }, 200);
    loadingList.value = false;
  } catch {
    loadingList.value = false;
  }
};

const onSearch = async (val) => {
  currentPage.value = 1;
  dataParams.value.page = 1;
  callRecordList.value = [];
  hasMore.value = true;
  await getTable(val);
};

const getCallRecord = async () => {
  currentPage.value = 1;
  dataParams.value.page = 1;
  callRecordList.value = [];
  hasMore.value = true;
  await getTable();
};
// =================================================获取通话列表结束============================

// 处理时间戳为指定格式
const formatUnixTimestamp = (timestamp) => {
  const now = dayjs();
  const targetDate = dayjs.unix(timestamp);
  const diffDays = now.diff(targetDate, 'day');

  switch (diffDays) {
    case 0: {
      return targetDate.fromNow(); // 显示相对时间，如 7分钟前
    }
    case 1: {
      return '昨天';
    }
    case 2: {
      return '前天';
    }
    default: {
      return targetDate.format('M-DD'); // 显示 5-05 格式
    }
  }
};

// 选择点击通话记录
const chooseItem = (item, index) => {
  // console.log('item', item);
  active.value = index;
  const data = {
    _Callee: item.Callee,
    _Caller: item.Caller,
    _CallerLocation: item.CallerLocation,
    _Direction: item?.Direction?.id,
    _RecordURL: item.RecordURL,
  };
  emits('getCurrentCallRecord', data);
};

onMounted(async () => {
  getTable();
});
onUnmounted(() => {});
</script>

<template>
  <div class="flex h-full flex-col">
    <div class="dial h-[150px] p-4">
      <!-- 拨号功能 -->
      <div
        class="bg-theme-color-opacity flex h-full flex-col items-center justify-center gap-3 rounded-md px-3"
      >
        <Input
          v-model:value="telephoneNumber"
          placeholder="输入电话号码拨号"
          size="large"
        />
        <Button
          :loading="loading"
          class="w-full"
          size="large"
          type="primary"
          @click="handleCallout"
        >
          拨号
        </Button>
      </div>
    </div>
    <div class="flex items-center justify-between gap-2 p-4 pt-0">
      <InputSearch
        v-model:value="dataParams.keywords"
        allow-clear
        placeholder="输入电话号码搜索"
        style="width: 220px"
        @search="onSearch"
      />

      <span style="cursor: pointer">
        <Tooltip>
          <template #title>刷新</template>
          <Icon
            class="size-5"
            icon="material-symbols-light:refresh-rounded"
            @click="getCallRecord"
          />
        </Tooltip>
      </span>
    </div>
    <div
      ref="scrollContainer"
      class="flex h-[600px] flex-col overflow-y-auto"
      @scroll="handleScroll"
    >
      <div v-if="newLoading" class="flex w-[100%] justify-center">
        <Spin /><span class="ml-1">获取最新通话记录中...</span>
      </div>
      <div
        v-for="(item, index) in callRecordList"
        :key="item"
        :class="[active === index ? 'bg-theme-color-opacity' : '']"
        class="flex min-h-[100px] items-center"
        @click="chooseItem(item, index)"
      >
        <div class="flex h-full w-[25%] items-center justify-center">
          <div
            class="card-box flex items-center justify-center rounded-full p-2"
          >
            <Icon
              v-if="item.type === '1'"
              class="color-theme-color size-8"
              icon="solar:outgoing-call-bold"
            />
            <Icon
              v-else
              class="color-theme-color size-8"
              icon="solar:incoming-call-bold"
            />
          </div>
        </div>
        <div class="flex w-[50%] flex-col text-[17px]">
          <span>{{ item.Callee.slice(4) }}</span>
          <div class="opacity-[0.5]">
            <span>{{ item.EndStatusStringName }}</span>
            <span class="ml-1">{{ item.Duration }}</span>
          </div>
        </div>
        <div class="flex w-[25%] items-center justify-center opacity-[0.5]">
          {{ formatUnixTimestamp(item.lastTimestamp) }}
        </div>
      </div>

      <div
        v-if="!loadingList && callRecordList.length > 0 && !hasMore"
        class="flex w-[100%] justify-center"
      >
        <span class="text-muted-foreground text-xs">——已加载全部——</span>
      </div>
      <div v-if="loadingList" class="flex w-[100%] justify-center">
        <Spin />加载中...
      </div>
      <div
        v-if="callRecordList.length === 0 && !loadingList"
        class="flex h-full items-center justify-center"
      >
        <span>暂无数据</span>
      </div>
      <div></div>
    </div>
  </div>
</template>

<style></style>
