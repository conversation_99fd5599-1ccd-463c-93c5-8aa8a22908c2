<script setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { VbenSpinner } from '@vben/common-ui';

import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  message,
  Popconfirm,
  Select,
  Table,
} from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { fieldObjEditApi, findFieldApi, getFieldMsgApi } from '#/api';
import {
  Checkbox,
  InputNumber,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const props = defineProps({
  fieldId: {
    type: String,
    default: null,
  },
  twoacceptData: {
    type: Object,
    default: () => ({}),
  },
  formDatanew: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['cancelEdit', 'toThree', 'toOne']);

const route = useRoute();

const { isLoading, startLoading, stopLoading } = useLoading();

// 表单数据对象
const formData = ref({});
// 字段id
const fielded = ref('');
// 字段类型
const fieldType = ref('');
// 是否是新建页面
const newIf = ref(false);
// 显示表单
const showForm = ref(false);
// 逻辑表格数据
const dataSource = ref({
  logic: 'and',
  diy_logic: '', // 类型为自定义条件时的 自定义条件内容
  condition_list: [
    {
      number: 0, // 整数序号
      field_id: null, // 选择的关联表的字段id
      operator: null, // 枚举 运算符
      to_content_type: 'value', // 筛选条件 根据字段的值 or 根据固定值
      content: null,
    },
  ],
});
// 显示自定义逻辑输入框
const showCustom = ref(false);
// 下拉选项
const selectOpt = ref([]);

// 获取查找相关字段列表
const getFieldlist = async (id) => {
  const { data } = await findFieldApi(id);
  selectOpt.value = data;
  selectOpt.value = selectOpt.value.filter(
    (item, index, self) =>
      index === self.findIndex((t) => t.name === item.name && t.id === item.id),
  );
  formData.value.Field_Related_To_Display_Field =
    formData.value.Field_Related_To_Display_Field?.id;
  createForm();
};

// 条件逻辑启用状态
const LogicStatus = ref(!!formData.value.Field_Related_Filter_Logic?.status);
const LogicData = reactive({
  type: 'Checkbox',
  field_readonly: false,
  value: LogicStatus.value,
  title: '启用状态',
  field_api: '',
  Field_Help_Text: '启用状态',
});

// 引用对应字段值的下拉选项
const updateOpt = ref([]);

// 获取字段详情信息
async function getFieldDetail() {
  startLoading();
  const { data } = await getFieldMsgApi(fielded.value || '');
  if (!newIf.value) {
    data.Field_Related_Update_values =
      data.Field_Related_Update_values &&
      (data.Field_Related_Update_values !== '' ||
        data.Field_Related_Update_values !== null)
        ? JSON.parse(data.Field_Related_Update_values)
        : [
            {
              Field_Related_To_Find: null,
              Field_Related_To_Obj: null,
            },
          ];
  }
  if (
    data.Field_Related_Filter_Logic !== '' ||
    data.Field_Related_Filter_Logic !== null
  ) {
    data.Field_Related_Filter_Logic = JSON.parse(
      data.Field_Related_Filter_Logic,
    );
    dataSource.value = data.Field_Related_Filter_Logic;

    if (dataSource.value?.logic === 'diy') {
      showCustom.value = true;
    }
  }
  if (data.Field_Related_Filter_Logic) {
    dataSource.value = data.Field_Related_Filter_Logic.condition;
    if (dataSource.value?.logic === 'diy') {
      showCustom.value = true;
    }
    LogicStatus.value = data.Field_Related_Filter_Logic.status;
  } else {
    data.Field_Related_Filter_Logic = {
      condition: {
        logic: 'and',
        diy_logic: '',
        condition_list: [
          {
            number: null,
            field_id: null,
            operator: null,
            to_content_type: 'value',
            content: null,
          },
        ],
      },
      status: false, // 启用状态
    };
    dataSource.value = data.Field_Related_Filter_Logic.condition;
    if (dataSource.value?.logic === 'diy') {
      showCustom.value = true;
    }
    showCustom.value = false;
  }
  formData.value = data;
  getFieldlist(formData.value.Field_Related_To.id);
  stopLoading();
  showForm.value = true;
}

// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
  if (type === 'Checkbox') return Checkbox;
}
// 表单校验
const formRef = ref();
const rules = {
  Field_name: [{ required: true, message: '请输入字段名!', trigger: 'blur' }],
  Field_id: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (!value) {
          throw new Error('请输入字段标签API!');
        }
        const regex = /^[A-Z]\w*$/i;
        if (!regex.test(value)) {
          throw new Error('格式错误!');
        }
      },
      trigger: 'blur',
    },
  ],
  Field_length: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
  Field_orderby: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
  Field_Related_To: [
    { required: true, message: '请选择字段关联表!', trigger: 'blur' },
  ],
  Field_Related_To_Display_Field: [
    { required: true, message: '请选择关联表显示字段!', trigger: 'blur' },
  ],
};

// ==============表单属性==========
const formList = ref([]);
const modelForm = computed(() => {
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});

async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: true,
      Field_Help_Text: '字段类型',
      value: newIf.value ? fieldType.value : formData.value.Field_type.id,
      title: '字段类型',
      field_api: '',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入字段名称',
      value: formData.value.Field_name,
      title: '字段名称',
      field_api: 'Field_name',
      sort: 2,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入以字母和下划线组合的字符',
      value: formData.value.Field_id,
      title: '字段标签API',
      field_api: 'Field_id',
      sort: 3,
    },
    {
      type: 'Lookup_Relationship',
      field_readonly: false,
      Field_Help_Text: '请选择字段关联表',
      value: formData.value.Field_Related_To,
      title: '字段关联表',
      field_api: 'Field_Related_To',
      sort: 4,
      key: 'Field_Related_To',
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择关联表显示字段',
      value: formData.value.Field_Related_To_Display_Field,
      title: '关联表显示字段',
      field_api: 'Field_Related_To_Display_Field',
      sort: 5,
    },
    {
      type: 'Logic',
      field_readonly: false,
      value: formData.value.Field_Related_Filter_Logic,
      title: '关联逻辑',
      field_api: 'Field_Related_Filter_Logic',
      sort: 7,
    },
    {
      type: 'Number',
      field_readonly: false,
      Field_Help_Text: '请输入序号',
      value: formData.value.Field_orderby,
      title: '字段排序',
      field_api: 'Field_orderby',
      sort: 20,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_Active,
      title: '字段启用',
      field_api: 'Field_Active',
      sort: 20,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: formData.value.Field_Description,
      title: '描述',
      Field_Help_Text: '请输入...',
      field_api: 'Field_Description',
      sort: 30,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.Field_Help_Text,
      title: '帮助文本',
      field_api: 'Field_Help_Text',
      sort: 40,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_Required,
      title: '必需',
      field_api: 'Field_Required',
      Field_Help_Text: '将此字段设置为针对外部系统的唯一记录标识符',
      sort: 50,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_readonly,
      title: '只读',
      field_api: 'Field_readonly',
      sort: 60,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_compsite_id,
      title: '外部ID',
      field_api: 'Field_compsite_id',
      sort: 80,
      Field_Help_Text: '此字段中总是需要一个值来保存记录',
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.Field_Default_Value,
      title: '默认值',
      field_api: 'Field_Default_Value',
      sort: 90,
    },
  ];
  if (newIf.value) {
    arr.push({
      type: 'Checkbox',
      field_readonly: false,
      Field_Help_Text: '若选中，当主表删除后，子表关联数据会一起删除',
      value: formData.value.Field_Related_del_all,
      title: '强关联',
      field_api: 'Field_Related_del_all',
      sort: 8,
    });
  }
  if (!newIf.value) {
    arr.push({
      type: 'Update',
      field_readonly: false,
      Field_Help_Text: '',
      value: formData.value.Field_Related_Update_values,
      title: '引用对应字段值',
      field_api: 'Field_Related_Update_values',
      sort: 8,
    });
  }
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
  stopLoading();
}

// 关联逻辑表格
const columns = [
  {
    title: '字段',
    dataIndex: 'field',
    width: '25%',
  },
  {
    title: '运算符',
    dataIndex: 'operator',
    width: '22%',
  },
  {
    title: '字段/值',
    dataIndex: 'value',
    width: '38%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '15%',
  },
];

const onDelete = (i) => {
  formData.value.Field_Related_Filter_Logic.condition.condition_list.splice(
    i,
    1,
  );
};
// 添加条件逻辑
const addLogic = () => {
  const newData = {
    number: null,
    field_id: null,
    operator: null,
    to_content_type: 'value',
    content: null,
  };
  formData.value.Field_Related_Filter_Logic.condition.condition_list.push(
    newData,
  );
};
const FieldData = reactive({
  type: 'dropdownlist',
  field_readonly: false,
  field_api: '',
  placeHolderval: '请选择字段',
});
// 字段或值下拉选项
const valueOpt = [
  { label: '字段', value: 'field' },
  { label: '值', value: 'value' },
];
// 运算符下拉选项
const operatorOpt = [
  { label: '等于', value: 'EQUALS' },
  { label: '不等于', value: 'NOT_EQUALS' },
  { label: '大于', value: 'MORE_THAN' },
  { label: '小于', value: 'LESS_THAN' },
  { label: '介于', value: 'BETWEEN' },
  { label: '大于等于', value: 'MORE_THAN_AND_EQUAL' },
  { label: '小于等于', value: 'LESS_THAN_AND_EQUAL' },
];
// 筛选逻辑下拉选项
const logicOpt = [
  { label: 'AND', value: 'and' },
  { label: '自定义', value: 'diy' },
];
// 显示自定义逻辑输入框
const customVal = ref('');
// 筛选逻辑选择事件
const changeLogic = (e) => {
  if (e === 'diy') {
    showCustom.value = true;
  } else {
    showCustom.value = false;
    customVal.value = '';
  }
};
// 引用对于字段值表格
const columnsUp = [
  {
    title: '查找关系字段',
    dataIndex: 'Field_Related_To_Find',
    width: '42%',
  },
  {
    title: '对象字段',
    dataIndex: 'Field_Related_To_Obj',
    width: '42%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '16%',
  },
];
// 添加引用对于字段值
const addUpdate = () => {
  const newData = {
    Field_Related_To_Find: null,
    Field_Related_To_Obj: null,
  };
  formData.value.Field_Related_Update_values.push(newData);
};
const onDeleteUpdate = (i) => {
  formData.value.Field_Related_Update_values.splice(i, 1);
};
// 监听字段关联表
const isFirstLoad = ref(true);
// 同步更新fromData对象值
watch(
  () => formList.value,
  (newData) => {
    if (isFirstLoad.value === true) {
      isFirstLoad.value = false; // 更新标志变量以表示已初始化
      return; // 初次加载时不执行任何操作
    }
    newData.forEach((item) => {
      formData.value[item.field_api] = item.value;
    });
  },
  { deep: true }, // 深度监听
);

// 当前选择的查找相关数据
const findDate = ref({});
const findChange = async (value) => {
  if (value) {
    findDate.value = {
      id: value.id,
      name: value.name,
    };
    const { data } = await findFieldApi(value.id);

    selectOpt.value = data;
    selectOpt.value = selectOpt.value.filter(
      (item, index, self) =>
        index ===
        self.findIndex((t) => t.name === item.name && t.id === item.id),
    );
  } else {
    selectOpt.value = [];
  }
  formList.value = formList.value.map((item) => {
    if (item.field_api === 'Field_Related_To_Display_Field') {
      return {
        ...item,
        value: null,
      };
    }
    return item;
  });
  formData.value.Field_Related_Filter_Logic.condition = {
    logic: 'and',
    diy_logic: '', // 类型为自定义条件时的 自定义条件内容
    condition_list: [
      {
        number: 0, // 整数序号
        field_id: null, // 选择的关联表的字段id
        operator: null, // 枚举 运算符
        to_content_type: 'value', // 筛选条件 根据字段的值 or 根据固定值
        content: null,
      },
    ],
  };
  dataSource.value = formData.value.Field_Related_Filter_Logic.condition;

  showCustom.value = false;
  if (!newIf.value) {
    formList.value = formList.value.map((item) => {
      if (item.field_api === 'Field_Related_Update_values') {
        return {
          ...item,
          value: [
            {
              Field_Related_To_Find: null,
              Field_Related_To_Obj: null,
            },
          ],
        };
      }
      return item;
    });
    formData.value.Field_Related_Update_values = [
      {
        Field_Related_To_Find: null,
        Field_Related_To_Obj: null,
      },
    ];
  }
};

onMounted(async () => {
  fielded.value = props.fieldId || '';
  const { data: resOpt } = await findFieldApi(route.params.id);
  updateOpt.value = resOpt;
  if (props.twoacceptData && Object.keys(props.twoacceptData).length > 0) {
    startLoading();
    fieldType.value = props.twoacceptData?.type;
    newIf.value = props.twoacceptData?.new;
    formData.value = props.formDatanew;
    formData.value.Field_Related_To = props.formDatanew.Field_Related_To1;
    findDate.value = props.formDatanew.Field_Related_To1;
    formData.value.Field_type = fieldType.value;
    LogicStatus.value = formData.value.Field_Related_Filter_Logic.status;
    dataSource.value = formData.value.Field_Related_Filter_Logic.condition;
    if (dataSource.value?.logic === 'diy') {
      showCustom.value = true;
    }
    const { data } = await findFieldApi(
      typeof formData.value.Field_Related_To === 'object'
        ? formData.value.Field_Related_To.id
        : formData.value.Field_Related_To,
    );
    selectOpt.value = data;
    selectOpt.value = selectOpt.value.filter(
      (item, index, self) =>
        index ===
        self.findIndex((t) => t.name === item.name && t.id === item.id),
    );

    createForm();
    showForm.value = true;
  } else {
    await getFieldDetail();
  }
});
// 保存编辑
function containsChinese(str) {
  const pattern = /[\u4E00-\u9FA5]/; // 中文字符的起始编码到结束编码
  return pattern.test(str);
}
const serveData = async () => {
  const data = cloneDeep(formData.value);
  for (const key in data) {
    if (data[key] !== null && typeof data[key] === 'object') {
      data[key] = containsChinese(data[key].name)
        ? data[key].id
        : data[key].name;
    }
  }
  const filteredList =
    formData.value.Field_Related_Filter_Logic.condition.condition_list.filter(
      (item) => item.field_id !== null && item.field_id !== '',
    );
  filteredList.forEach((item, index) => {
    item.number = index + 1;
  });
  formData.value.Field_Related_Filter_Logic.status = LogicStatus.value;
  formData.value.Field_Related_Filter_Logic.condition.condition_list =
    filteredList;
  data.Field_Related_Filter_Logic = JSON.stringify(
    formData.value.Field_Related_Filter_Logic,
  );
  const filteredUpdate = formData.value.Field_Related_Update_values.filter(
    (item) =>
      item.Field_Related_To_Find !== null &&
      item.Field_Related_To_Find !== '' &&
      item.Field_Related_To_Obj !== null &&
      item.Field_Related_To_Obj !== '',
  );
  data.Field_Related_Update_values = JSON.stringify(filteredUpdate);
  await fieldObjEditApi(data);
  message.success('保存成功');
  emits('cancelEdit');
};

async function saveEdit() {
  formRef.value
    .validateFields() // 触发校验
    .then(() => {
      serveData();
    })
    .catch(() => {});
}

// 取消编辑
async function cancelEdit() {
  emits('cancelEdit');
}
// 上一步
async function toOne() {
  emits('toOne');
}

// 下一步
async function toThree() {
  formData.value.Field_type = fieldType.value;
  const filteredList =
    formData.value.Field_Related_Filter_Logic.condition.condition_list.filter(
      (item) => item.field_id !== null && item.field_id !== '',
    );
  filteredList.forEach((item, index) => {
    item.number = index + 1;
  });
  formData.value.Field_Related_Filter_Logic.condition.condition_list =
    filteredList;
  formData.value.Field_Related_Filter_Logic.status = LogicStatus.value;
  formData.value.Field_Related_To1 = findDate.value;
  formRef.value
    .validateFields() // 触发校验
    .then(() => {
      emits('toThree', formData.value);
    })
    .catch(() => {});
}

const filterOption = (input, option) => {
  return option.name.toLowerCase().includes(input.toLowerCase());
};
</script>

<template>
  <div>
    <Card :title="newIf ? '输入字段详细信息' : '编辑自定义字段'">
      <template #extra>
        <div v-if="!newIf" class="flex gap-6">
          <Button type="primary" @click="saveEdit"> 保存 </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
        <div v-if="newIf" class="flex gap-4">
          <Button type="primary" @click="toOne"> 上一步 </Button>
          <Button type="primary" @click="toThree"> 下一步 </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
      </template>
      <template v-if="showForm">
        <div
          :style="{ height: newIf ? '58vh' : '70vh' }"
          class="overflow-y-auto"
        >
          <Form
            ref="formRef"
            :label-col="{ span: 4 }"
            :model="modelForm"
            :rules="rules"
            :wrapper-col="{ span: 14 }"
            name="nestMessages"
          >
            <template v-for="(item, index) in formList" :key="index">
              <FormItem
                v-if="
                  item.type !== 'Logic' &&
                  item.field_api !== 'Field_Related_To_Display_Field' &&
                  item.type !== 'Update'
                "
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <component
                  :is="getComponent(item.type)"
                  v-model="item.value"
                  :parameters="item"
                  class="col-span-6 !w-[70%]"
                  object-id="Field"
                  object-name="field"
                  @find-change="findChange"
                />
              </FormItem>
              <!-- Field_Related_To_Display_Field 类型的表单项 -->
              <template
                v-if="item.field_api === 'Field_Related_To_Display_Field'"
              >
                <FormItem
                  class="ml-[2%] mt-[2%]"
                  label="关联表显示字段: "
                  name="Field_Related_To_Display_Field"
                >
                  <Select
                    v-model:value="item.value"
                    :field-names="{ label: 'name', value: 'id' }"
                    :filter-option="filterOption"
                    :options="selectOpt"
                    :placeholder="item.Field_Help_Text"
                    class="!w-[68%]"
                    show-search
                  />
                </FormItem>
              </template>
              <!-- Logic 类型的表单项 -->
              <template v-if="item.type === 'Logic'">
                <FormItem
                  class="ml-[2%] mt-[2%]"
                  label="关联逻辑："
                  name="logicSection"
                >
                  <div>
                    <div class="mb-4 flex w-[70%] items-center justify-between">
                      <Button type="primary" @click="addLogic">
                        添加条件逻辑
                      </Button>
                      <component
                        :is="getComponent('Checkbox')"
                        v-model="LogicStatus"
                        :parameters="LogicData"
                      />
                    </div>
                    <Table
                      :columns="columns"
                      :data-source="
                        formData.Field_Related_Filter_Logic.condition
                          .condition_list
                      "
                      :pagination="false"
                      bordered
                    >
                      <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'field'">
                          <FormItem :name="`${record.key}-field`" no-style>
                            <Select
                              v-model:value="record.field_id"
                              :field-names="{ label: 'name', value: 'id' }"
                              :options="selectOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'operator'">
                          <FormItem :name="`${record.key}-operator`" no-style>
                            <Select
                              v-model:value="record.operator"
                              :options="operatorOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'value'">
                          <div class="flex gap-4">
                            <FormItem
                              :name="`${record.key}-to_content_type`"
                              no-style
                            >
                              <Select
                                v-model:value="record.to_content_type"
                                :options="valueOpt"
                                :placeholder="FieldData.placeHolderval"
                              />
                            </FormItem>
                            <FormItem
                              :name="`${record.key}-value-value`"
                              no-style
                            >
                              <Select
                                v-if="
                                  record.to_content_type === 'field' ||
                                  record.to_content_type !== 'value'
                                "
                                v-model:value="record.content"
                                :field-names="{ label: 'name', value: 'id' }"
                                :options="updateOpt"
                                :placeholder="FieldData.placeHolderval"
                              />
                              <Input
                                v-else
                                v-model:value="record.content"
                                placeholder="请输入..."
                                type="text"
                              />
                            </FormItem>
                          </div>
                        </template>
                        <template v-if="column.dataIndex === 'operation'">
                          <Popconfirm
                            v-if="
                              formData.Field_Related_Filter_Logic.condition
                                .condition_list.length > 0
                            "
                            title="确定删除吗?"
                            @confirm="onDelete(index)"
                          >
                            <Button type="primary">删除</Button>
                          </Popconfirm>
                        </template>
                      </template>
                    </Table>
                    <div class="mt-4 flex items-center justify-between">
                      <FormItem
                        class="!mb-0 flex w-[50%] items-center"
                        name="logic"
                      >
                        <div class="flex items-center">
                          筛选逻辑：
                          <Select
                            v-model:value="dataSource.logic"
                            :options="logicOpt"
                            :placeholder="FieldData.placeHolderval"
                            class="flex-1"
                            @change="changeLogic"
                          />
                        </div>
                      </FormItem>
                      <FormItem
                        v-if="showCustom"
                        class="!mb-0 ml-2 flex w-[100%]"
                        name="diy_logic"
                      >
                        <div class="flex items-center">
                          自定义逻辑：
                          <Input
                            v-model:value="dataSource.diy_logic"
                            class="!flex-1"
                            placeholder="请输入自定义逻辑"
                            type="text"
                          />
                        </div>
                      </FormItem>
                    </div>
                  </div>
                </FormItem>
              </template>
              <!-- Update 类型的表单项 -->
              <template v-if="item.type === 'Update'">
                <FormItem
                  class="ml-[2%] mt-[2%]"
                  label="引用对应字段值："
                  name="logicSection"
                >
                  <div>
                    <div class="mb-4 flex w-[70%] items-center justify-between">
                      <Button type="primary" @click="addUpdate"> 添加 </Button>
                    </div>
                    <Table
                      :columns="columnsUp"
                      :data-source="formData.Field_Related_Update_values"
                      :pagination="false"
                      bordered
                    >
                      <template #bodyCell="{ column, record, index }">
                        <template
                          v-if="column.dataIndex === 'Field_Related_To_Find'"
                        >
                          <FormItem :name="`${record.key}-field`" no-style>
                            <Select
                              v-model:value="record.Field_Related_To_Find"
                              :field-names="{ label: 'name', value: 'key' }"
                              :options="selectOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template
                          v-if="column.dataIndex === 'Field_Related_To_Obj'"
                        >
                          <FormItem :name="`${record.key}-field`" no-style>
                            <Select
                              v-model:value="record.Field_Related_To_Obj"
                              :field-names="{ label: 'name', value: 'key' }"
                              :options="updateOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'operation'">
                          <Popconfirm
                            v-if="
                              formData.Field_Related_Update_values.length > 0
                            "
                            title="确定删除吗?"
                            @confirm="onDeleteUpdate(index)"
                          >
                            <Button type="primary">删除</Button>
                          </Popconfirm>
                        </template>
                      </template>
                    </Table>
                  </div>
                </FormItem>
              </template>
            </template>
          </Form>
        </div>
      </template>
    </Card>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
