import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';
import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'ant-design:home-outlined',
      order: -1,
      title: $t('page.dashboard.title'),
    },
    name: 'Dashboard',
    path: '/',
    children: [
      {
        name: 'Workspace',
        path: '/workspace',
        component: () => import('#/views/dashboard/workspace/index.vue'),
        meta: {
          affixTab: true,
          icon: 'carbon:workspace',
          title: $t('page.dashboard.workspace'),
        },
      },
      {
        name: 'Telephonesystem',
        path: '/telephonesystem',
        component: () => import('#/views/dashboard/telephonesystem/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: $t('page.dashboard.telephonesystem'),
        },
      },
    ],
  },
];

export default routes;
