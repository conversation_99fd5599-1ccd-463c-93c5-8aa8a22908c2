import { requestClient } from '#/api/request';

/**
 * @returns 获取个人所有数据、未读消息、已读消息
 */
export async function getUnreadMessagesApi(data: any) {
  return requestClient.post(`/notice_hand_message/get_unread_messages`, data);
}

/**
 
 * @returns 批量处理消息记录/删除单条记录
 */
export function batchMessageApi(data: any) {
  return requestClient.post('/notice_hand_message/batch_mark_message', data);
}

/**
 * @returns 单条信息已读处理
 */
export function markMessageApi(data: any) {
  return requestClient.post('/approval_message/update', data);
}

/**
 * @returns 验证是否可以websocket连接
 */

export function websocketStatusApi() {
  return requestClient.post('/backlog_hand/authenticate');
}
