import type { FilterColumn } from '@vben/types';

import dayjs from 'dayjs';

// 处理下拉条件选项
export function disposeSelectList(item: FilterColumn) {
  if (
    ['color_picker', 'dropdownlist', 'picklist_multi_select'].includes(
      item.type,
    )
  ) {
    const filterColumnsList = ['包含', '不包含', '为空', '不为空'].map(
      (item) => {
        return {
          label: item,
          value: item,
        };
      },
    );
    return filterColumnsList;
  }

  if (['Checkbox'].includes(item.type)) {
    const filterColumnsList = ['是', '否'].map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    return filterColumnsList;
  }

  if (
    [
      'Formula',
      'Lookup_Relationship',
      'Password',
      'Text',
      'Text_Area',
      'Text_Area_HTML',
      'Text_Area_Rich',
      'URL',
    ].includes(item.type)
  ) {
    const filterColumnsList = [
      '等于',
      '不等于',
      '包含',
      '不包含',
      '为空',
      '不为空',
    ].map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    return filterColumnsList;
  }

  if (
    [
      'BLOB',
      'group',
      'integer',
      'Number',
      'Password',
      'Percent',
      'Time',
      'Year',
    ].includes(item.type)
  ) {
    const filterColumnsList = [
      '等于',
      '不等于',
      '介于',
      '小于',
      '大于',
      '小于或等于',
      '大于或等于',
      '为空',
      '不为空',
    ].map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    return filterColumnsList;
  }

  if (['Date', 'Date_Time'].includes(item.type)) {
    const filterColumnsList = [
      '等于',
      '不等于',
      '大于',
      '小于',
      '介于',
      '小于等于',
      '大于等于',
      '为空',
      '不为空',
      '当日',
      '当月',
      '当年',
      '昨日',
      '上月',
      '上年',
    ].map((item) => {
      return {
        label: item,
        value: item,
      };
    });
    return filterColumnsList;
  }
  return [];
}

// 处理选择条件对应的值
type SelectType =
  | '上年'
  | '上月'
  | '不为空'
  | '不包含'
  | '不等于'
  | '为空'
  | '介于'
  | '包含'
  | '否'
  | '大于'
  | '大于等于'
  | '小于'
  | '小于等于'
  | '当年'
  | '当日'
  | '当月'
  | '昨日'
  | '是'
  | '等于';
export function disposeSelectValue(selectType: SelectType) {
  const enumType = {
    等于: 'EQUALS',
    不等于: 'NOT_EQUALS',
    包含: 'CONTAINS',
    不包含: 'NOT_CONTAINS',
    为空: 'IS_NULL',
    不为空: 'IS_NOT_NULL',
    大于: 'MORE_THAN',
    小于: 'LESS_THAN',
    介于: 'BETWEEN',
    大于等于: 'MORE_THAN_AND_EQUAL',
    小于等于: 'LESS_THAN_AND_EQUAL',
    当日: 'BETWEEN',
    当月: 'BETWEEN',
    当年: 'BETWEEN',
    昨日: 'BETWEEN',
    上月: 'BETWEEN',
    上年: 'BETWEEN',
    是: 'EQUALS',
    否: 'EQUALS',
  };
  return enumType[selectType] ?? '';
}

// 处理类型和输入方式
export function disposeTypeAndInputType(type: string) {
  let class_name, format;
  if (
    [
      'Formula',
      'Password',
      'Text',
      'Text_Area',
      'Text_Area_HTML',
      'Text_Area_Rich',
      'URL',
    ].includes(type)
  ) {
    class_name = 'string';
    format = 'input';
  }

  if (['Checkbox'].includes(type)) {
    class_name = 'bool';
    format = 'input';
  }
  if (['Lookup_Relationship'].includes(type)) {
    class_name = 'object';
    format = 'input';
  }
  if (['integer'].includes(type)) {
    class_name = 'int';
    format = 'input';
  }
  if (['Number'].includes(type)) {
    class_name = 'float';
    format = 'input';
  }
  if (['Date'].includes(type)) {
    class_name = 'date';
    format = 'input';
  }
  if (['Time'].includes(type)) {
    class_name = 'time';
    format = 'input';
  }
  if (['Date_Time'].includes(type)) {
    class_name = 'dateTime';
    format = 'input';
  }
  if (
    ['color_picker', 'dropdownlist', 'picklist_multi_select'].includes(type)
  ) {
    class_name = 'string';
    format = 'list';
  }

  return { class_name, format };
}

// 处理查询输入值
export function handleQueryValue(item: any) {
  if (['不为空', '为空'].includes(item.selectConditionValue)) {
    return [];
  }

  if (
    [
      'Formula',
      'integer',
      'Lookup_Relationship',
      'Number',
      'Password',
      'Text',
      'Text_Area',
      'Text_Area_HTML',
      'Text_Area_Rich',
      'URL',
    ].includes(item.type)
  ) {
    return [item.value];
  }
  if (['Checkbox'].includes(item.type)) {
    const value = item.selectConditionValue === '是' ? 1 : 0;
    return [value];
  }

  if (
    ['color_picker', 'dropdownlist', 'picklist_multi_select'].includes(
      item.type,
    )
  ) {
    return [item.selectValue];
  }

  if (['Date', 'Date_Time', 'Time'].includes(item.type)) {
    if (
      ['不等于', '大于', '大于等于', '小于', '小于等于', '等于'].includes(
        item.selectConditionValue,
      )
    ) {
      if (item.type === 'Date') return [dayjs(item.value).format('YYYY-MM-DD')];
      if (item.type === 'Date_Time')
        return [dayjs(item.value).format('YYYY-MM-DD HH:mm:ss')];
      if (item.type === 'Time') return [dayjs(item.value).format('HH:mm:ss')];
    }
    if (Array.isArray(item.value)) {
      if (item.type === 'Date')
        return [
          dayjs(item.value[0]).format('YYYY-MM-DD'),
          dayjs(item.value[1]).format('YYYY-MM-DD'),
        ];
      if (item.type === 'Date_Time')
        return [
          dayjs(item.value[0]).format('YYYY-MM-DD HH:mm:ss'),
          dayjs(item.value[1]).format('YYYY-MM-DD HH:mm:ss'),
        ];
      if (item.type === 'Time')
        return [
          dayjs(item.value[0]).format('HH:mm:ss'),
          dayjs(item.value[1]).format('HH:mm:ss'),
        ];
    }
  }

  return [item.value];
}

export function handleQueryLabel(item: any) {
  if (['不为空', '为空'].includes(item.selectConditionValue)) {
    return [];
  }

  if (
    [
      'Formula',
      'integer',
      'Lookup_Relationship',
      'Number',
      'Password',
      'Text',
      'Text_Area',
      'Text_Area_HTML',
      'Text_Area_Rich',
      'URL',
    ].includes(item.type)
  ) {
    return [item.value];
  }
  if (['Checkbox'].includes(item.type)) {
    return [];
  }

  if (['color_picker', 'picklist_multi_select'].includes(item.type)) {
    return [item.selectValue];
  }

  if (item.type === 'dropdownlist') {
    const find = item.selectOption.find(
      (_item: any) => item.selectValue === _item.value,
    );

    return [find.label];
  }

  if (['Date', 'Date_Time', 'Time'].includes(item.type)) {
    if (
      ['不等于', '大于', '大于等于', '小于', '小于等于', '等于'].includes(
        item.selectConditionValue,
      )
    ) {
      if (item.type === 'Date') return [dayjs(item.value).format('YYYY-MM-DD')];
      if (item.type === 'Date_Time')
        return [dayjs(item.value).format('YYYY-MM-DD HH:mm:ss')];
      if (item.type === 'Time') return [dayjs(item.value).format('HH:mm:ss')];
    }
    if (Array.isArray(item.value)) {
      if (item.type === 'Date')
        return [
          dayjs(item.value[0]).format('YYYY-MM-DD'),
          dayjs(item.value[1]).format('YYYY-MM-DD'),
        ];
      if (item.type === 'Date_Time')
        return [
          dayjs(item.value[0]).format('YYYY-MM-DD HH:mm:ss'),
          dayjs(item.value[1]).format('YYYY-MM-DD HH:mm:ss'),
        ];
      if (item.type === 'Time')
        return [
          dayjs(item.value[0]).format('HH:mm:ss'),
          dayjs(item.value[1]).format('HH:mm:ss'),
        ];
    }
  }

  return [item.value];
}
