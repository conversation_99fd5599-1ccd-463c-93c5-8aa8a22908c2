<script setup></script>

<template>
  <div class="h-full p-2">
    <div class="h-full w-full">
      <div class="card-box mb-[10px] flex h-[10%] p-2">
        <div class="ml-[15px] text-[23px] font-bold leading-[55px]">
          报表系统人员整理数据
        </div>
        <div class="ml-[30px] text-[18px] font-bold leading-[55px]">附件：</div>
        <a class="leading-[55px]" href="https://baidu.com">123123</a>
      </div>
      <div class="flex h-full justify-between">
        <div
          class="card-box mr-[10px] h-[84%] max-h-[700px] w-[70%] overflow-y-auto p-5"
        >
          <span class="text-[17px]"></span>
        </div>
        <div class="card-box h-[84%] w-[30%] p-5">
          <div class="h-[75%]">
            <div class="mb-[10px] text-[20px] font-bold">AI摘要</div>
            <div class="h-[450px] overflow-y-auto text-[15px]"></div>
          </div>
          <div>
            <div class="mb-[10px] mt-[10px] text-[20px] font-bold">
              AI关键词
            </div>
            <div class="h-[120px] overflow-y-auto text-[15px]"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped></style>
