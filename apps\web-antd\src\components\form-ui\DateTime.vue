<script setup>
import { ref, watch } from 'vue';

import { DatePicker } from 'ant-design-vue';
import dayjs from 'dayjs';

const props = defineProps({
  modelValue: {
    type: Object,
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const dateValue = ref(null);

dateValue.value = props.modelValue
  ? dayjs(props.modelValue, 'YYYY-MM-DD HH:mm:ss')
  : null;
watch(dateValue, (newValue) => {
  if (newValue) {
    emits('update:modelValue', newValue);
  } else {
    emits('update:modelValue', null);
  }
});
</script>

<template>
  <DatePicker
    v-model:value="dateValue"
    :disabled="props.parameters.field_readonly"
    show-time
  />
</template>

<style></style>
