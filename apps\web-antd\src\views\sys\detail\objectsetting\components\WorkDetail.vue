<script setup>
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { Button, Card, Pagination, Table } from 'ant-design-vue';

import { getObjWorkListApi, getObjWorkUseListApi } from '#/api';

const router = useRouter();

const route = useRoute();

const fieldLoading = ref(false);
const fieldLoading2 = ref(false);
const dataSource = ref([]);
const dataSource2 = ref([]);
const btnTotal = ref(0);
const btnTotal2 = ref(0);
const dataColumns = [
  {
    title: '作业名称',
    dataIndex: 'ProcessAutomationName',
    key: 'ProcessAutomationName',
    align: 'center',
  },
  {
    title: '请求方法',
    dataIndex: 'ProcessAutomationMethod',
    key: 'ProcessAutomationMethod',
    align: 'center',
  },
  {
    title: '作业路径',
    dataIndex: 'ProcessAutomationUrl',
    key: 'ProcessAutomationUrl',
    align: 'center',
  },
  {
    title: '调用形式',
    dataIndex: 'ProcessAutomationTriggerMode',
    key: 'ProcessAutomationTriggerMode',
    align: 'center',
  },
  {
    title: '作业描述',
    dataIndex: 'ProcessAutomationDescription',
    key: 'ProcessAutomationDescription',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
  },
];

const dataParams = ref({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
});
const dataParams2 = ref({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
});
// 获取作业列表
async function getWorkList() {
  fieldLoading.value = true;
  const { data } = await getObjWorkListApi(dataParams.value);
  dataSource.value = data.data;
  btnTotal.value = data.total;
  fieldLoading.value = false;
}

// 获取对象被使用作业列表
async function getWorkUseList() {
  fieldLoading2.value = true;
  const { data } = await getObjWorkUseListApi(dataParams2.value);
  dataSource2.value = data.data;
  btnTotal2.value = data.total;
  fieldLoading2.value = false;
}

// 查看按钮
async function toWork(id) {
  router.push({
    name: 'taskdetail',
    params: {
      id,
    },
  });
}

// 改变页数
async function pageChange(page, pageSize) {
  dataParams.value.page = page;
  dataParams.value.page_size = pageSize;
  getWorkList(); // 重新调用
}
// 改变页数
async function pageChange2(page, pageSize) {
  dataParams2.value.page = page;
  dataParams2.value.page_size = pageSize;
  getWorkUseList(); // 重新调用
}

onMounted(() => {
  getWorkList();
  getWorkUseList();
});
</script>

<template>
  <div>
    <Card title="对象作业">
      <Table
        :columns="dataColumns"
        :data-source="dataSource"
        :loading="fieldLoading"
        :pagination="false"
        :scroll="{ y: 220 }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'ProcessAutomationMethod'">
            {{ record[column.key].name }}
          </template>
          <template v-if="column.key === 'ProcessAutomationTriggerMode'">
            {{ record[column.key].name }}
          </template>
          <template v-if="column.key === 'operation'">
            <div class="flex justify-center gap-6">
              <Button type="primary" @click="toWork(record.id)">查看</Button>
            </div>
          </template>
        </template>
        <template #footer>
          <div class="text-right">
            <Pagination
              v-model:current="dataParams.page"
              v-model:page-size="dataParams.page_size"
              :show-size-changer="true"
              :show-total="(btnTotal) => `共 ${btnTotal} 条`"
              :total="btnTotal"
              @change="pageChange"
            />
          </div>
        </template>
      </Table>
    </Card>
    <Card class="mt-6" title="对象被作业使用清单">
      <Table
        :columns="dataColumns"
        :data-source="dataSource2"
        :loading="fieldLoading2"
        :pagination="false"
        :scroll="{ y: 220 }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'ProcessAutomationMethod'">
            {{ record[column.key].name }}
          </template>
          <template v-if="column.key === 'ProcessAutomationTriggerMode'">
            {{ record[column.key].name }}
          </template>
          <template v-if="column.key === 'operation'">
            <div class="flex justify-center gap-6">
              <Button type="primary" @click="toWork(record.id)">查看</Button>
            </div>
          </template>
        </template>
        <template #footer>
          <div class="text-right">
            <Pagination
              v-model:current="dataParams2.page"
              v-model:page-size="dataParams2.page_size"
              :show-size-changer="true"
              :show-total="(btnTotal2) => `共 ${btnTotal2} 条`"
              :total="btnTotal2"
              @change="pageChange2"
            />
          </div>
        </template>
      </Table>
    </Card>
  </div>
</template>

<style></style>
