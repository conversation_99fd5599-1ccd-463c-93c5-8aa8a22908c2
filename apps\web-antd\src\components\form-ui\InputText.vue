<script setup>
import { onUnmounted, ref, watch } from 'vue';

import { VbenIcon } from '@vben/common-ui';

import { Button, Input, message } from 'ant-design-vue';

import { WebSocketASR } from '../../utils/websocket-asr.js';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const inputValue = ref(props.modelValue);

const changeValue = () => {
  emits('update:modelValue', inputValue.value);
};

watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue;
  },
);

// 语音输入相关状态
const isRecording = ref(false);
const isWaitingResult = ref(false); // 新增：等待结果状态
const recordingTime = ref(0);
const recordingInterval = ref(null);

// WebSocket语音识别相关状态
const wsASR = ref(null);

// 语音输出相关状态
const isPlaying = ref(false);
const audioElement = ref(null);

// 注意：已移除传统的语音识别API，只使用WebSocket ASR

// 停止语音输入
const stopVoiceInput = async () => {
  if (!isRecording.value) return;

  isRecording.value = false;
  clearInterval(recordingInterval.value);

  // 只使用WebSocket ASR方式
  if (wsASR.value) {
    try {
      wsASR.value.stopRecording();
      // 停止录音后会自动进入等待结果状态，由回调函数处理
    } catch (error) {
      console.error('WebSocket ASR停止失败:', error);
      message.error('WebSocket语音识别停止失败');
      isWaitingResult.value = false;
    }
  } else {
    message.error('语音识别服务未初始化');
  }
};

// WebSocket语音识别功能
const startWebSocketASR = async () => {
  try {
    // 每次录音都创建新的WebSocket ASR实例，确保连接状态正确
    if (wsASR.value) {
      try {
        wsASR.value.destroy();
      } catch (error) {
        console.warn('清理旧的WebSocket ASR实例失败:', error);
      }
    }

    wsASR.value = new WebSocketASR();

    // 设置回调函数
    wsASR.value.setCallbacks({
      onResult: (text) => {
        // 将识别结果赋值到输入框
        inputValue.value = text;
        changeValue();
        message.success('语音识别完成');
        // 停止录音状态和等待状态
        isRecording.value = false;
        isWaitingResult.value = false;
        clearInterval(recordingInterval.value);
      },
      onError: (error) => {
        console.error('WebSocket ASR错误:', error);
        isRecording.value = false;
        isWaitingResult.value = false;
        clearInterval(recordingInterval.value);

        // 根据错误类型显示不同的提示
        if (
          error.includes('权限被拒绝') ||
          error.includes('Permission denied')
        ) {
          message.error('麦克风权限被拒绝，请检查浏览器权限设置');
        } else if (
          error.includes('未找到麦克风') ||
          error.includes('NotFound')
        ) {
          message.error('未检测到麦克风设备，请检查设备连接');
        } else if (error.includes('被占用') || error.includes('NotReadable')) {
          message.error('麦克风被其他应用占用，请关闭其他录音应用');
        } else {
          message.error(`语音识别失败: ${error}`);
        }
      },
      onStatusChange: (status) => {
        // 显示状态变化（可选）
        if (import.meta.env.DEV) {
          // eslint-disable-next-line no-console
          console.log('WebSocket ASR状态:', status);
        }
      },
      onLoadingChange: (isLoading) => {
        // 处理loading状态变化
        isWaitingResult.value = isLoading;
      },
    });

    await wsASR.value.startRecording();
    isRecording.value = true;
    recordingTime.value = 0;

    // 计时
    recordingInterval.value = setInterval(() => {
      recordingTime.value++;
    }, 1000);

    message.info('开始语音识别，请说话...');
  } catch (error) {
    console.error('WebSocket ASR启动失败:', error);
    isRecording.value = false;
    clearInterval(recordingInterval.value);
  }
};

// 语音输入功能（统一入口）
const startVoiceInput = async () => {
  if (isRecording.value) {
    stopVoiceInput();
    return;
  }

  // 直接调用WebSocket ASR
  await startWebSocketASR();
};

// 使用浏览器原生TTS
const useBrowserTTS = () => {
  try {
    // 停止当前所有语音
    speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(inputValue.value);

    // 设置语音参数 - 可以根据需要调整这些值
    utterance.lang = 'zh-CN'; // 语言设置：'zh-CN'中文, 'en-US'英文, 'ja-JP'日文等

    // 语速控制 (rate)
    // 范围：0.1 - 10，默认值：1
    // 0.5 = 慢速，1 = 正常速度，1.5 = 快速，2 = 很快
    utterance.rate = 0.7; // 降低语速，让语音更清晰自然

    // 音调控制 (pitch)
    // 范围：0 - 2，默认值：1
    // 0.5 = 低音调，1 = 正常音调，1.5 = 高音调，2 = 很高音调
    utterance.pitch = 0.8; // 降低音调，避免声音尖细

    // 音量控制 (volume)
    // 范围：0 - 1，默认值：1
    // 0 = 静音，0.5 = 中等音量，1 = 最大音量
    utterance.volume = 0.9; // 稍高音量确保清晰度

    // 可选：选择特定的语音（如果系统支持多种语音）
    // 获取可用的语音列表
    const voices = speechSynthesis.getVoices();

    // 方法1：按语言筛选语音
    const chineseVoices = voices.filter((voice) => voice.lang.includes('zh'));
    if (chineseVoices.length > 0) {
      utterance.voice = chineseVoices[0]; // 使用第一个中文语音
    }

    // 方法2：按名称选择特定语音（需要根据系统实际语音名称调整）
    // const preferredVoice = voices.find(voice =>
    //   voice.name.includes('Microsoft') || // Windows系统语音
    //   voice.name.includes('Google') ||    // Chrome浏览器语音
    //   voice.name.includes('Ting-Ting')    // macOS中文语音
    // );
    // if (preferredVoice) {
    //   utterance.voice = preferredVoice;
    // }

    // 调试：打印所有可用语音（开发时可以取消注释查看）
    // console.log('可用语音列表:', voices.map(v => ({ name: v.name, lang: v.lang })));

    // 监听事件
    utterance.addEventListener('start', () => {
      isPlaying.value = true;
      message.success('开始播放语音（浏览器TTS）');
    });

    utterance.addEventListener('end', () => {
      isPlaying.value = false;
    });

    utterance.addEventListener('error', () => {
      isPlaying.value = false;
      message.error('语音播放失败');
    });

    // 开始播放
    speechSynthesis.speak(utterance);
  } catch {
    isPlaying.value = false;
    message.error('浏览器TTS播放失败');
  }
};

// 停止语音输出
const stopVoiceOutput = () => {
  // 停止音频播放
  if (audioElement.value) {
    audioElement.value.pause();
    audioElement.value.currentTime = 0;
  }
  // 停止浏览器TTS
  if ('speechSynthesis' in window) {
    speechSynthesis.cancel();
  }
  isPlaying.value = false;
};

// 语音输出功能
const startVoiceOutput = async () => {
  if (!inputValue.value.trim()) {
    message.warning('请先输入文字内容');
    return;
  }

  if (isPlaying.value) {
    stopVoiceOutput();
    return;
  }

  // 检查浏览器是否支持语音合成
  if (!('speechSynthesis' in window)) {
    message.error('您的浏览器不支持语音合成功能');
    return;
  }

  try {
    isPlaying.value = true;

    // 清理之前的音频
    if (audioElement.value) {
      audioElement.value.pause();
      audioElement.value.src = '';
      audioElement.value = null;
    }

    // 方案一：尝试调用后端接口（使用fetch直接请求）
    try {
      message.loading('正在生成语音...', 0);

      // 使用 fetch 直接调用您的接口（通过代理）
      // 从localStorage获取accessToken
      const tokenData = localStorage.getItem(
        'vben-web-antd-5.4.0-dev-core-access',
      );
      const accessToken = tokenData ? JSON.parse(tokenData).accessToken : null;

      const headers = {
        'Content-Type': 'application/json',
      };

      // 如果有accessToken，添加到请求头
      if (accessToken) {
        headers.Authorization = `Bearer ${accessToken}`;
      }

      const response = await fetch('/basic-api/ai/play_voice_tts', {
        method: 'POST',
        headers,
        body: JSON.stringify({
          content: inputValue.value,
        }),
      });

      message.destroy();

      if (response.ok) {
        const contentType = response.headers.get('content-type');

        // 检查是否返回音频流 (media_type="audio/wav")
        if (contentType && contentType.startsWith('audio/')) {
          try {
            // 分块读取流数据
            const readStreamInChunks = async (readableStream) => {
              const reader = readableStream.getReader();
              const chunks = [];
              let totalLength = 0;
              let receivedLength = 0;

              try {
                // 获取内容长度（如果有的话）
                const contentLength = response.headers.get('content-length');
                const expectedLength = contentLength
                  ? Number.parseInt(contentLength, 10)
                  : null;

                while (true) {
                  const { done, value } = await reader.read();

                  if (done) {
                    break;
                  }

                  if (value) {
                    chunks.push(value);
                    receivedLength += value.length;
                    totalLength += value.length;

                    // 显示进度（如果知道总长度）
                    if (expectedLength) {
                      const progress = Math.round(
                        (receivedLength / expectedLength) * 100,
                      );
                      message.loading(`正在接收音频数据... ${progress}%`, 0);
                    }
                  }
                }

                // 将所有块合并为一个完整的 Uint8Array
                message.loading('正在处理音频数据...', 0);
                const completeArray = new Uint8Array(totalLength);
                let offset = 0;

                for (const chunk of chunks) {
                  completeArray.set(chunk, offset);
                  offset += chunk.length;
                }

                return completeArray;
              } catch (readError) {
                throw new Error(`流读取失败: ${readError.message}`);
              } finally {
                reader.releaseLock();
              }
            };

            // 读取完整的音频数据
            message.loading('正在接收音频数据...', 0);
            const audioData = await readStreamInChunks(response.body);
            message.destroy();

            // 检查音频数据是否有效
            if (audioData.length === 0) {
              throw new Error('音频数据为空');
            }

            // 验证音频数据格式（简单检查）
            const isValidAudio = audioData.length > 44; // WAV文件至少需要44字节的头部
            if (!isValidAudio) {
              throw new Error('音频数据格式无效');
            }

            // 检查音频数据是否可能是原始PCM数据
            const isPossiblePCM = (data) => {
              // 检查是否没有标准音频文件头
              const header = data.slice(0, 12);

              // 检查常见音频格式的魔数
              const hasStandardHeader =
                // WAV格式: RIFF...WAVE
                (header[0] === 82 &&
                  header[1] === 73 &&
                  header[2] === 70 &&
                  header[3] === 70 &&
                  header[8] === 87 &&
                  header[9] === 65 &&
                  header[10] === 86 &&
                  header[11] === 69) ||
                // MP3格式: 前两字节
                (header[0] === 255 && (header[1] & 224) === 224) ||
                // OGG格式: OggS
                (header[0] === 79 &&
                  header[1] === 103 &&
                  header[2] === 103 &&
                  header[3] === 83) ||
                // FLAC格式: fLaC
                (header[0] === 102 &&
                  header[1] === 76 &&
                  header[2] === 97 &&
                  header[3] === 67);

              const isPCM = !hasStandardHeader && data.length > 1000;

              // 可选：调试信息
              // console.log('音频格式检测:', { hasStandardHeader, isPCM, dataLength: data.length });

              return isPCM;
            };

            // 如果是原始PCM数据，尝试转换为WAV格式
            if (isPossiblePCM(audioData)) {
              try {
                // 智能检测音频参数
                const detectAudioParams = (dataLength) => {
                  // 开始检测音频参数

                  // 常见的TTS输出配置
                  const commonConfigs = [
                    // 最常见的TTS配置
                    { sampleRate: 22_050, channels: 1, bitsPerSample: 16 },
                    { sampleRate: 16_000, channels: 1, bitsPerSample: 16 },
                    { sampleRate: 24_000, channels: 1, bitsPerSample: 16 },
                    { sampleRate: 8000, channels: 1, bitsPerSample: 16 },
                    { sampleRate: 44_100, channels: 1, bitsPerSample: 16 },
                    // 双声道配置
                    { sampleRate: 22_050, channels: 2, bitsPerSample: 16 },
                    { sampleRate: 16_000, channels: 2, bitsPerSample: 16 },
                    // 8位配置
                    { sampleRate: 22_050, channels: 1, bitsPerSample: 8 },
                    { sampleRate: 16_000, channels: 1, bitsPerSample: 8 },
                  ];

                  // 基于文本长度估算音频时长（更精确的算法）
                  // 中文语音合成通常每分钟200-300字
                  const charsPerSecond = 4; // 每秒约4个字符
                  const estimatedDuration = Math.max(
                    0.5, // 最少0.5秒
                    inputValue.value.length / charsPerSecond,
                  );

                  // 估算音频时长并匹配最佳参数

                  let bestMatch = null;
                  let bestScore = Infinity;

                  for (const config of commonConfigs) {
                    const bytesPerSample = config.bitsPerSample / 8;
                    const expectedSize =
                      config.sampleRate *
                      config.channels *
                      bytesPerSample *
                      estimatedDuration;
                    const sizeDiff = Math.abs(dataLength - expectedSize);
                    const score = sizeDiff / expectedSize;

                    if (score < bestScore) {
                      bestScore = score;
                      bestMatch = config;
                    }
                  }

                  // 如果最佳匹配的误差仍然很大，使用保守的默认值
                  if (bestScore > 2) {
                    bestMatch = {
                      sampleRate: 22_050,
                      channels: 1,
                      bitsPerSample: 16,
                    };
                  }

                  return bestMatch;
                };

                // 检测最佳音频参数
                const audioParams = detectAudioParams(audioData.length);

                // 创建WAV文件头 - 使用检测到的最佳参数
                const createWavHeader = (
                  dataLength,
                  sampleRate = audioParams.sampleRate,
                  channels = audioParams.channels,
                  bitsPerSample = audioParams.bitsPerSample,
                ) => {
                  const header = new ArrayBuffer(44);
                  const view = new DataView(header);

                  // RIFF chunk descriptor
                  view.setUint32(0, 1_179_011_410, true); // "RIFF"
                  view.setUint32(4, 36 + dataLength, true); // file size - 8
                  view.setUint32(8, 1_163_280_727, true); // "WAVE"

                  // fmt sub-chunk
                  view.setUint32(12, 544_501_094, true); // "fmt "
                  view.setUint32(16, 16, true); // sub-chunk size
                  view.setUint16(20, 1, true); // audio format (PCM)
                  view.setUint16(22, channels, true); // number of channels
                  view.setUint32(24, sampleRate, true); // sample rate
                  view.setUint32(
                    28,
                    (sampleRate * channels * bitsPerSample) / 8,
                    true,
                  ); // byte rate
                  view.setUint16(32, (channels * bitsPerSample) / 8, true); // block align
                  view.setUint16(34, bitsPerSample, true); // bits per sample

                  // data sub-chunk
                  view.setUint32(36, 1_635_017_060, true); // "data"
                  view.setUint32(40, dataLength, true); // data size

                  return new Uint8Array(header);
                };

                // 创建完整的WAV文件
                const wavHeader = createWavHeader(audioData.length);
                const wavData = new Uint8Array(
                  wavHeader.length + audioData.length,
                );
                wavData.set(wavHeader, 0);
                wavData.set(audioData, wavHeader.length);

                // 尝试播放转换后的WAV文件
                const wavBlob = new Blob([wavData], { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(wavBlob);
                const audio = new Audio(audioUrl);

                // 音频播放优化设置
                // 重要：不调整播放速度，保持原始音频的正确播放速度
                audio.playbackRate = 1; // 使用标准播放速度，避免音频变形

                // 音量设置
                audio.volume = 0.9; // 稍高音量确保清晰度

                // WAV音频播放参数已配置

                // 音频质量优化设置
                audio.preload = 'auto'; // 预加载音频
                audio.crossOrigin = 'anonymous'; // 跨域设置

                // 音频上下文优化（提升播放质量）
                try {
                  if (window.AudioContext) {
                    const audioContext = new window.AudioContext();
                    // 确保音频上下文处于运行状态
                    if (audioContext.state === 'suspended') {
                      audioContext.resume();
                    }
                  }
                } catch (contextError) {
                  // 音频上下文创建失败，继续使用普通播放
                  console.warn(
                    '音频上下文优化失败，使用标准播放:',
                    contextError,
                  );
                }

                await audio.play();
                message.success('开始播放语音');

                // 保存音频元素引用
                audioElement.value = audio;

                // 设置播放结束事件监听器
                audio.addEventListener('ended', () => {
                  isPlaying.value = false;
                });

                return;
              } catch (wavError) {
                console.error('WAV转换播放失败:', wavError);
              }
            }

            // 如果WAV转换也失败，降级到浏览器TTS
            console.error('所有音频格式都无法播放');
            isPlaying.value = false;
            message.error('音频数据无法播放，使用浏览器TTS');
            useBrowserTTS();
            return;
          } catch (streamError) {
            message.destroy();
            console.error('音频流处理失败:', streamError);
            throw new Error(`音频流处理失败: ${streamError.message}`);
          }
        }

        // 如果返回的是 JSON 格式（包含音频URL或Base64）
        if (contentType && contentType.includes('application/json')) {
          const data = await response.json();

          if (data && (data.audioUrl || data.audioBase64)) {
            const audioSrc = data.audioUrl || data.audioBase64;

            try {
              audioElement.value = new Audio(audioSrc);

              // 音频播放优化设置
              audioElement.value.playbackRate = 1; // 使用标准播放速度，保持音频原始质量
              audioElement.value.volume = 0.9; // 稍高音量确保清晰度

              // JSON音频播放参数已配置
              audioElement.value.preload = 'auto'; // 预加载音频
              audioElement.value.crossOrigin = 'anonymous'; // 跨域设置

              // 音频上下文优化
              try {
                if (window.AudioContext) {
                  const audioContext = new window.AudioContext();
                  if (audioContext.state === 'suspended') {
                    audioContext.resume();
                  }
                }
              } catch (contextError) {
                console.warn('音频上下文优化失败:', contextError);
              }

              // 设置播放结束事件监听器
              audioElement.value.addEventListener('ended', () => {
                isPlaying.value = false;
              });

              await audioElement.value.play();
              message.success('开始播放语音');
              return;
            } catch {
              message.error('音频播放失败，使用浏览器TTS');
              useBrowserTTS();
              return;
            }
          }
        }

        // 如果都不匹配，降级到浏览器TTS
        throw new Error('未识别的响应格式');
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error) {
      message.destroy();
      console.error('后端TTS接口调用失败:', error);
      message.error(`TTS接口调用失败，使用浏览器TTS: ${error.message}`);
    }

    // 降级到浏览器原生TTS
    useBrowserTTS();
  } catch (error) {
    console.error('语音输出总体错误:', error);
    isPlaying.value = false;
    message.error(`语音播放失败: ${error.message}`);
  }
};

// 格式化录音时间
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

// 清理资源
onUnmounted(() => {
  // 清理录音相关资源
  if (recordingInterval.value) {
    clearInterval(recordingInterval.value);
  }

  // 清理WebSocket ASR资源
  if (wsASR.value) {
    try {
      wsASR.value.destroy();
    } catch (error) {
      console.warn('清理WebSocket ASR失败:', error);
    }
  }

  // 清理播放相关资源
  if (audioElement.value) {
    audioElement.value.pause();
  }
  stopVoiceOutput();
});
</script>

<template>
  <div class="flex items-center gap-2">
    <!-- 文本输入框 -->
    <Input
      v-model:value="inputValue"
      :disabled="props.parameters.field_readonly"
      :placeholder="props.parameters.Field_Help_Text"
      class="flex-1"
      type="text"
      @change="changeValue"
    />

    <!-- 语音按钮组 -->
    <div v-if="!props.parameters.field_readonly" class="flex gap-1">
      <!-- 语音输入按钮 -->
      <Button
        :loading="isWaitingResult"
        :type="isRecording || isWaitingResult ? 'primary' : 'default'"
        class="flex min-w-[60px] items-center justify-center"
        size="small"
        @click="startVoiceInput"
      >
        <div
          v-if="!isWaitingResult"
          class="flex items-center justify-center gap-1"
        >
          <VbenIcon
            :class="isRecording ? 'text-red-500' : ''"
            icon="ant-design:audio-outlined"
          />
          <span v-if="isRecording" class="text-xs">{{
            formatTime(recordingTime)
          }}</span>
        </div>
      </Button>

      <!-- 语音输出按钮 -->
      <Button
        :type="isPlaying ? 'primary' : 'default'"
        class="flex min-w-[40px] items-center justify-center"
        size="small"
        @click="startVoiceOutput"
      >
        <VbenIcon
          :class="isPlaying ? 'text-blue-500' : ''"
          icon="ant-design:sound-outlined"
        />
      </Button>
    </div>
  </div>
</template>

<style></style>
