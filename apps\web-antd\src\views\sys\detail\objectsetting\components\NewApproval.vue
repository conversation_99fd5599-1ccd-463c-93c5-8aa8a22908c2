<script setup>
import { onMounted, ref } from 'vue';

import { Card, Steps } from 'ant-design-vue';

import StepFive from '../newapprove/StepFive.vue';
import StepFour from '../newapprove/StepFour.vue';
import StepOne from '../newapprove/StepOne.vue';
import StepThree from '../newapprove/StepThree.vue';
import StepTwo from '../newapprove/StepTwo.vue';

// 父组件传递参数
const props = defineProps({
  dataIds: {
    type: String,
    default: '',
  },
  stepVal: {
    type: Number,
    default: 0,
  },
  unenableDataLength: {
    type: Number,
    default: 0,
  },
  newIf: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['cancelEdit', 'onSave']);

const stepOneFormRef = ref(null); // 用于引用步骤一子组件的表单实例
const stepTwoFormRef = ref(null); // 用于引用步骤二子组件的表单实例
const stepThreeFormRef = ref(null); // 用于引用步骤三子组件的表单实例
const stepFourFormRef = ref(null); // 用于引用步骤四子组件的表单实例
const stepFiveFormRef = ref(null); // 用于引用步骤五子组件的表单实例

const current = ref(0);
// 是否是新审批流
const newIfs = ref(false);

// 步骤条
const steps = [
  {
    key: 1,
    title: '一、输入名称和说明',
    content: '步骤一',
  },
  {
    key: 2,
    title: '二、指定条目条件',
    content: '步骤二',
  },
  {
    key: 3,
    title: '三、选择批准权限',
    content: '步骤三',
  },
  {
    key: 4,
    title: '四、流程设置',
    content: '步骤四',
  },
  {
    key: 5,
    title: '五、审批结束操作',
    content: '步骤五',
  },
];
const showStep = ref(false);
// 新建数据后的id
const dataId = ref('');
const handleStepChange = async (newCurrent) => {
  if (newCurrent > current.value || newCurrent < current.value) {
    // 如果是前进步骤，先验证
    try {
      if (current.value === 0) {
        await stepOneFormRef.value?.validate();
        // 调用子组件的 onNext 方法，并接收返回值
        const result = await stepOneFormRef.value?.onNext2();
        if (result) {
          // 更新状态
          dataId.value = result.id; // 保存新建数据的 ID
          newIfs.value = false;
        }
      }
      if (current.value === 1) {
        await stepTwoFormRef.value?.onNext2();
      }
      if (current.value === 2) {
        await stepThreeFormRef.value?.onNext2();
      }
      if (current.value === 3) {
        await stepFourFormRef.value?.onNext2();
      }
      if (current.value === 4) {
        await stepFiveFormRef.value?.onNext2();
      }
      current.value = newCurrent;
    } catch {}
  } else {
    // 后退步骤直接切换
    current.value = newCurrent;
  }
};
// 保存事件
const toSave = () => {
  emits('onSave');
};

// 步骤一到下一页

const toNext = (id) => {
  if (id) {
    dataId.value = id;
  }
  current.value = 1;
  newIfs.value = false;
};
const toLast = () => {
  current.value = 0;
};

onMounted(() => {
  if (props.newIf) {
    showStep.value = false;
    newIfs.value = props.newIf;
  }
  if (props.dataIds) {
    dataId.value = props.dataIds;
  }
  if (props.stepVal) {
    current.value = props.stepVal - 1;
  }

  showStep.value = true;
});
</script>
<template>
  <div>
    <Card title="新建审批流">
      <Steps
        :current="current"
        :items="steps"
        class="mb-6"
        @change="handleStepChange($event)"
      />
      <StepOne
        v-if="current === 0 && showStep"
        ref="stepOneFormRef"
        :data-id="dataId"
        :new-ifs="newIfs"
        :unenable-data-length="props.unenableDataLength"
        @cancel-edit="() => emits('cancelEdit')"
        @to-next="toNext"
        @to-save="toSave"
      />
      <StepTwo
        v-if="current === 1"
        ref="stepTwoFormRef"
        :data-id="dataId"
        @cancel-edit="() => emits('cancelEdit')"
        @to-last="toLast"
        @to-next="() => (current = 2)"
        @to-save="toSave"
      />
      <StepThree
        v-if="current === 2"
        ref="stepThreeFormRef"
        :data-id="dataId"
        @cancel-edit="() => emits('cancelEdit')"
        @to-last="
          () => {
            current = 1;
          }
        "
        @to-next="() => (current = 3)"
        @to-save="toSave"
      />
      <StepFour
        v-if="current === 3"
        ref="stepFourFormRef"
        :data-id="dataId"
        @cancel-edit="() => emits('cancelEdit')"
        @to-last="
          () => {
            current = 2;
          }
        "
        @to-next="() => (current = 4)"
        @to-save="toSave"
      />
      <StepFive
        v-if="current === 4"
        ref="stepFiveFormRef"
        :data-id="dataId"
        @cancel-edit="() => emits('cancelEdit')"
        @to-last="
          () => {
            current = 3;
          }
        "
        @to-save="toSave"
      />
    </Card>
  </div>
</template>

<style lang="scss" scoped></style>
