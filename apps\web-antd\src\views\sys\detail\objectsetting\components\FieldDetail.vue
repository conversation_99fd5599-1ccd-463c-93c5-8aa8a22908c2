<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import {
  <PERSON>ton,
  Card,
  InputSearch,
  message,
  Pagination,
  Popconfirm,
  Table,
} from 'ant-design-vue';
import { debounce } from 'lodash-es';

import { delFiledApi, getFiledListApi } from '#/api';

import FieldEdit from './FieldEdit.vue';
import NewFiled from './NewField.vue';

const route = useRoute();

const fieldLoading = ref(false);
const dataSource = ref([]);
const fieldTotal = ref(0);
const dataColumns = [
  {
    title: '字段名',
    dataIndex: 'Field_name',
    key: 'Field_name',
    align: 'center',
  },
  {
    title: '字段标签',
    dataIndex: 'Field_id',
    key: 'Field_id',
    align: 'center',
  },
  {
    title: '数据类型',
    dataIndex: 'Field_type',
    key: 'Field_type',
    align: 'center',
  },
  {
    title: '字段排序',
    dataIndex: 'Field_orderby',
    key: 'Field_orderby',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
  },
];

const dataParams = reactive({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
  order_by: 'Field_orderby',
  sort_by: 'ASC',
  keywords: null,
});
// 获取字段列表
async function getFiledList(data) {
  fieldLoading.value = true;
  const { data: resData } = await getFiledListApi(data);
  dataSource.value = resData.data.map((item, i) => {
    item.key = i;
    return item;
  });
  fieldTotal.value = resData.total;
  fieldLoading.value = false;
}

const showNewfieldone = ref(false);
// 新建字段
const addFiled = () => {
  showNewfieldone.value = true;
};

// 搜索防抖
const searchChange = debounce(async () => {
  getFiledList(dataParams);
}, 800);

const showFieldEdit = ref(false);
const fieldEditval = ref({});

// 编辑字段
async function editField(data) {
  fieldEditval.value = data;
  showFieldEdit.value = true;
}

// 删除字段
async function deletedFiled(id) {
  await delFiledApi(id);
  message.success('删除成功');
  getFiledList(dataParams);
}

// 改变页数
async function pageChange(page, pageSize) {
  dataParams.page = page;
  dataParams.page_size = pageSize;
  getFiledList(dataParams); // 重新调用
}

// 步骤三新建后重新刷新列表
const saveField = () => {
  showNewfieldone.value = false;
  getFiledList(dataParams);
};

// 字段保存返回刷新列表
const cancelField = () => {
  showFieldEdit.value = false;
  getFiledList(dataParams);
};

onMounted(() => {
  getFiledList(dataParams);
});
</script>

<template>
  <div>
    <Card v-if="!showFieldEdit && !showNewfieldone" title="字段">
      <template #extra>
        <div class="flex gap-2">
          <Button type="primary" @click="addFiled"> 新建 </Button>
        </div>
      </template>
      <InputSearch
        v-model:value="dataParams.keywords"
        class="mb-5 w-1/4"
        enter-button
        placeholder="请输入关键词！"
        @change="searchChange"
      />
      <Table
        :columns="dataColumns"
        :data-source="dataSource"
        :loading="fieldLoading"
        :pagination="false"
        :scroll="{ y: 500 }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'Field_type'">
            {{ record.Field_type.name }}
          </template>
          <template v-if="column.key === 'operation'">
            <div class="flex justify-center gap-6">
              <Button type="primary" @click="editField(record)">编辑</Button>
              <Popconfirm
                cancel-text="取消"
                ok-text="确定"
                title="您确定要删除该字段吗？"
                @confirm="deletedFiled(record.id)"
              >
                <Button danger type="primary"> 删除 </Button>
              </Popconfirm>
            </div>
          </template>
        </template>
        <template #footer>
          <div class="text-right">
            <Pagination
              v-model:current="dataParams.page"
              v-model:page-size="dataParams.page_size"
              :show-size-changer="true"
              :show-total="(fieldTotal) => `共 ${fieldTotal} 条`"
              :total="fieldTotal"
              @change="pageChange"
            />
          </div>
        </template>
      </Table>
    </Card>
    <!-- 字段编辑 -->
    <FieldEdit
      v-if="showFieldEdit"
      :field-editval="fieldEditval"
      @cancel-field="cancelField"
    />
    <!-- 新建字段 -->
    <NewFiled
      v-if="showNewfieldone"
      @cancel-field="showNewfieldone = false"
      @save-field="saveField"
    />
  </div>
</template>

<style></style>
