/* eslint-disable unicorn/prefer-add-event-listener */
/* eslint-disable no-use-before-define */
// src/hooks/web/useWebSocket.ts
import { onUnmounted, ref } from 'vue';

interface WebSocketOptions {
  url: string; // WebSocket 连接地址
  onMessage?: (data: any) => void; // 消息接收回调
  onOpen?: (event: Event) => void; // 连接成功回调
  onClose?: (event: CloseEvent) => void; // 连接关闭回调
  onError?: (event: Event) => void; // 错误回调
  reconnectLimit?: number; // 最大重连次数，默认10次
  reconnectInterval?: number; // 重连间隔时间，默认10秒
  heartBeatInterval?: number; // 心跳间隔时间，默认35秒
  heartBeatMessage?: any; // 心跳消息内容，默认为 { type: 'heartbeat' }
}

export function useWebSocket(options: WebSocketOptions) {
  const {
    url,
    onMessage,
    onOpen,
    onClose,
    onError,
    reconnectLimit = 10,
    reconnectInterval = 10_000, // 10秒
    heartBeatInterval = 35_000, // 35秒
    heartBeatMessage = { type: 'heartbeat' },
  } = options;

  const socketRef = ref<null | WebSocket>(null);
  const status = ref<'closed' | 'connecting' | 'error' | 'open'>('connecting');
  const reconnectCount = ref(0);
  let reconnectTimer: NodeJS.Timeout | null = null;
  let heartBeatTimer: NodeJS.Timeout | null = null;

  // 发送心跳
  const sendHeartBeat = () => {
    if (socketRef.value && status.value === 'open') {
      heartBeatMessage.current_timestamp = Date.now();
      const message = JSON.stringify(heartBeatMessage);
      send(message);
    }
  };

  // 启动心跳
  const startHeartBeat = () => {
    stopHeartBeat(); // 先停止已有心跳
    sendHeartBeat(); // 立即发送一次心跳
    heartBeatTimer = setInterval(sendHeartBeat, heartBeatInterval);
  };

  // 停止心跳
  const stopHeartBeat = () => {
    if (heartBeatTimer) {
      clearInterval(heartBeatTimer);
      heartBeatTimer = null;
    }
  };

  // 关闭连接
  const close = (code?: number, reason?: string) => {
    if (socketRef.value) {
      stopHeartBeat();
      socketRef.value.close(code, reason);
    }
    clearReconnectTimer();
  };

  // 清理重连定时器
  const clearReconnectTimer = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer);
      reconnectTimer = null;
    }
  };

  // 创建 WebSocket 连接
  const createWebSocket = () => {
    status.value = 'connecting';
    try {
      socketRef.value = new WebSocket(url);

      socketRef.value.addEventListener('open', (event) => {
        status.value = 'open';
        reconnectCount.value = 0; // 重置重连次数
        clearReconnectTimer();
        startHeartBeat(); // 启动心跳
        onOpen?.(event);
      });

      socketRef.value.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          // 忽略心跳响应消息（如果需要）
          if (data.type !== 'heartbeat-response') {
            onMessage?.(data);
          }
        } catch {
          // 如果不是 JSON 数据，直接传递原始数据
          onMessage?.(event.data);
        }
      };

      socketRef.value.addEventListener('close', (event) => {
        status.value = 'closed';
        stopHeartBeat();
        onClose?.(event);
        // 非正常关闭时尝试重连
        if (event.code !== 1000) {
          handleReconnect();
        }
      });

      socketRef.value.onerror = (event) => {
        status.value = 'error';
        stopHeartBeat();
        onError?.(event);
        handleReconnect();
      };
    } catch (error) {
      console.error('WebSocket 创建错误:', error);
      handleReconnect();
    }
  };

  // 处理重连逻辑
  const handleReconnect = () => {
    clearReconnectTimer();

    if (reconnectCount.value < reconnectLimit) {
      reconnectCount.value += 1;
      reconnectTimer = setTimeout(() => {
        console.warn(`尝试第 ${reconnectCount.value} 次重连...`);
        createWebSocket();
      }, reconnectInterval);
    } else {
      console.error(
        `WebSocket 重连失败，已达最大重连次数 ${reconnectLimit} 次`,
      );
    }
  };

  // 发送消息
  const send = (data: any) => {
    if (socketRef.value && status.value === 'open') {
      try {
        const payload = typeof data === 'string' ? data : JSON.stringify(data);
        socketRef.value.send(payload);
      } catch (error) {
        console.error('消息发送失败:', error);
      }
    } else {
      console.error('WebSocket 连接未就绪，消息发送失败');
    }
  };

  // 手动重连
  const reconnect = () => {
    reconnectCount.value = 0; // 重置重连计数
    close();
    createWebSocket();
  };

  // 初始化连接
  createWebSocket();

  // 组件卸载时关闭连接
  onUnmounted(() => {
    close(1000, '组件卸载');
  });

  return {
    socket: socketRef,
    status,
    reconnectCount,
    send,
    close,
    reconnect,
  };
}
