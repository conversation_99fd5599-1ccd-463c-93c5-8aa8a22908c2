// 定义订单状态枚举
/**
 * 电话呼入&呼出 1 ok 正常通话

电话呼入&呼出 0 error 异常结束

电话呼入 102 ivrGiveUp IVR 期间用户放弃

电话呼入 103 waitingGiveUp 排队时用户放弃

电话呼入 104 ringingGiveUp 振铃时用户放弃

电话呼入 105 noSeatOnline 无座席在线

电话呼入 106 notWorkTime 非工作时间

电话呼入 107 ivrEnd IVR 后直接结束

电话呼入 100 blackList 黑名单

电话呼出 2 unconnected 未接通

电话呼出 108 restrictedCallee 被叫因高风险受限

电话呼出 109 tooManyRequest 外呼超频限制

电话呼出 110 restrictedArea 外呼区域限制

电话呼出 111 restrictedTime 外呼时间限制

电话呼出 201 unknown 未知状态

电话呼出 202 notAnswer 被叫未接听

电话呼出 203 userReject 被叫拒接挂断

电话呼出 204 powerOff 被叫关机

电话呼出 205 numberNotExist 被叫空号

电话呼出 206 busy 被叫忙

电话呼出 207 outOfCredit 被叫欠费

电话呼出 208 operatorError 运营商线路异常

电话呼出 209 callerCancel 主叫取消

电话呼出 210 notInService 被叫不在服务区

电话呼入&呼出 211 clientError 客户端错误
电话呼出 212 carrierBlocked 运营商拦截
 */
export enum TElephoneStatus {
  blackList = '黑名单',
  busy = '被叫忙',
  callerCancel = '主叫取消',
  carrierBlocked = '运营商拦截',
  clientError = '客户端错误',
  error = '异常结束',
  ivrEnd = 'IVR 后直接结束',
  ivrGiveUp = 'IVR 期间用户放弃',
  noSeatOnline = '无座席在线',
  notAnswer = '被叫未接听',
  notInService = '被叫不在服务区',
  notWorkTime = '非工作时间',
  numberNotExist = '被叫空号',
  ok = '正常通话',
  operatorError = '运营商线路异常',
  outOfCredit = '被叫欠费',
  powerOff = '被叫关机',
  restrictedArea = '外呼区域限制',
  restrictedCallee = '被叫因高风险受限',
  restrictedTime = '外呼时间限制',
  ringingGiveUp = '振铃时用户放弃',
  tooManyRequest = '外呼超频限制',
  unconnected = '未接通',
  unknown = '未知状态',
  userReject = '被叫拒接挂断',
  waitingGiveUp = '排队时用户放弃',
}
