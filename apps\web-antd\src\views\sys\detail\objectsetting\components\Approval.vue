<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { VbenIcon } from '@vben/common-ui';

import {
  Button,
  Card,
  Dropdown,
  InputNumber,
  Menu,
  MenuItem,
  message,
  Popconfirm,
  Table,
} from 'ant-design-vue';

import {
  approvalL<PERSON>pi,
  changeOrderapi,
  changeStatusapi,
  delApproveapi,
} from '#/api';

import NewApproval from './NewApproval.vue';

const route = useRoute();

const btnLoading = ref(false);
// 编辑下拉选项
const menuList = [
  { key: 1, name: '输入名称和说明' },
  { key: 2, name: '指定条目条件' },
  { key: 3, name: '选择批准权限' },
  { key: 4, name: '流程设置' },
  { key: 5, name: '审批结束操作' },
];
// 已启用数据
const enableData = reactive({
  data: [],
});
const arr = reactive({
  data: [1, 2, 3],
});
// 已启用表头
const dataColumns = [
  {
    title: '审批流名称',
    dataIndex: 'name',
    key: 'name',
    align: 'center',
    width: '30%',
  },
  {
    title: '优先级',
    key: 'order_num',
    align: 'center',
    dataIndex: 'order_num',
    customRender: (text, record, index) => {
      return arr[index];
    },
    width: '20%',
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
    width: '20%',
  },
];
// 未启用数据
const unenableData = reactive({
  data: [],
});
// 未启用表头
const unColumns = [
  {
    title: '审批流程名称',
    dataIndex: 'name',
    align: 'center',
  },
  {
    title: '描述',
    dataIndex: 'description',
    with: '35%',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '28%',
    align: 'center',
  },
];
// 重新排序
const changeOrder = async (data) => {
  try {
    await changeOrderapi(data);
    message.success('排序成功');
  } catch {
    message.error('排序失败');
  }
};

let sourceObj = reactive({});
let isDragging = false; // 新增拖拽状态标志

const customRow = (record) => {
  return {
    style: {
      cursor: 'move',
    },
    onMouseenter: (event) => {
      const ev = event || window.event;
      ev.target.draggable = true;
    },
    onDragstart: (event) => {
      const ev = event || window.event;
      ev.stopPropagation();
      sourceObj = record;
      isDragging = true; // 开始拖拽
    },
    onDragover: (event) => {
      const ev = event || window.event;
      ev.preventDefault();
    },
    onDrop: async (event) => {
      const ev = event || window.event;
      ev.stopPropagation();
      if (!isDragging) return; // 如果没有在拖拽状态，直接返回
      const targetObj = record;
      const tempData = [...enableData.data]; // 创建副本避免直接修改
      const indexA = tempData.indexOf(sourceObj);
      const indexB = tempData.indexOf(targetObj);
      if (indexA !== -1 && indexB !== -1 && indexA !== indexB) {
        // 执行数组元素移动
        const [removed] = tempData.splice(indexA, 1);
        tempData.splice(indexB, 0, removed);

        // 更新排序序号
        const updatedData = tempData.map((item, index) => ({
          ...item,
          order_num: index + 1,
        }));
        // 更新响应式数据
        enableData.data = updatedData;
        // 调用排序接口
        await changeOrder(updatedData);
      }

      isDragging = false; // 重置拖拽状态
    },
  };
};
// 重新排序
const sortorder = async (data) => {
  enableData.data.sort((a, b) => {
    return a.order_num - b.order_num;
  });
  enableData.data.map((item, index) => {
    item.order_num = index + 1;
    return index + 1;
  });
  changeOrder(data);
};

// 修改启用未用状态
const changeStatus = async (data) => {
  try {
    await changeStatusapi(data);
  } catch {}
};

// 目前未启用数据数量
const unenableDataLength = ref(0);
// 获取列表数据
const getList = async () => {
  btnLoading.value = true;
  const { data } = await approvalListapi(route.params.id);
  const data1 = data.filter((item) => item.status === 1);
  const data2 = data.filter((item) => item.status === 2);
  enableData.data = data1 || [];
  unenableData.data = data2 || [];

  // 计算未启用数据最后排序
  const numerable = unenableData.data.length - 1;
  const lastnum = ref(0);
  lastnum.value =
    unenableData.data[numerable] && unenableData.data[numerable].order_num
      ? (unenableData.data[numerable].order_num ?? 0)
      : 0;

  unenableDataLength.value = lastnum.value;
  btnLoading.value = false;
};

// 禁用按钮
const removeData = async (abledata) => {
  const changedata = {};
  changedata.id = abledata.id;
  changedata.object_id = abledata.object_id;
  changedata.status = 2;
  await changeStatus(changedata);
  message.success('禁用成功');
  await getList();
};

// 启用按钮
const startData = async (undata) => {
  const changedata = {};
  changedata.id = undata.id;
  changedata.object_id = undata.object_id;
  changedata.status = 1;
  await changeStatus(changedata);
  message.success('启用成功');
  await getList();
};

// 删除按钮
const delData = async (id) => {
  await delApproveapi(id);
  message.success('删除成功');
  await getList();
};

// 选中的数据id
const dataIds = ref('');
// 选择的步骤
const stepVal = ref(0);

// 新建审批流页面
const showNew = ref(false);
// 是否是新建
const newIf = ref(false);
const toNew = () => {
  newIf.value = true;
  dataIds.value = '';
  showNew.value = true;
};
// 保存按钮
const onSave = () => {
  showNew.value = false;
  getList();
};
// 取消按钮
const cancelEdit = () => {
  stepVal.value = 0;
  showNew.value = false;
  getList();
};

// 跳转到步骤页
const choseStep = (data, index) => {
  newIf.value = false;
  showNew.value = false;
  dataIds.value = data.id;
  stepVal.value = index;
  showNew.value = true;
};

onMounted(() => {
  getList();
});
</script>
<template>
  <div>
    <Card v-if="!showNew" title="审批流">
      <template #extra>
        <div class="flex gap-2">
          <Button type="primary" @click="toNew"> 创建新的审批流程 </Button>
        </div>
      </template>
      <Table
        :columns="dataColumns"
        :custom-row="customRow"
        :data-source="enableData.data"
        :loading="btnLoading"
        :pagination="false"
        bordered
      >
        <template #title>
          <div class="flex justify-between">
            <span class="text-base font-bold">已启用的审批流程</span>
            <Button type="primary" @click="sortorder(enableData.data)">
              重新排序
            </Button>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <div @dragenter="dragenter()">
            <!-- 名称 -->
            <template v-if="column.dataIndex === 'name'">
              {{ record.name }}
            </template>
            <!-- 顺序 -->
            <template v-if="column.dataIndex === 'order_num'">
              <InputNumber
                v-model:value="record.order_num"
                :step="1"
                class="w-[90%]"
              />
            </template>
            <!-- 描述 -->
            <template v-if="column.dataIndex === 'description'">
              {{ record.description }}
            </template>
            <template v-if="column.dataIndex === 'operation'">
              <div class="inline-flex items-center">
                <Dropdown>
                  <template #overlay>
                    <Menu>
                      <MenuItem
                        v-for="item in menuList"
                        :key="item.key"
                        @click="choseStep(record, item.key)"
                      >
                        {{ item.name }}
                      </MenuItem>
                    </Menu>
                  </template>
                  <Button
                    class="mr-2 flex items-center gap-1"
                    @click="choseStep(record, 0)"
                  >
                    编辑
                    <VbenIcon class="size-4" icon="formkit:down" />
                  </Button>
                </Dropdown>
                <Button class="ml-2" type="primary" @click="removeData(record)">
                  禁用
                </Button>
              </div>
            </template>
          </div>
        </template>
      </Table>
      <Table
        :columns="unColumns"
        :data-source="unenableData.data"
        :loading="btnLoading"
        :pagination="false"
        bordered
        class="mt-6"
      >
        <template #title>
          <span class="text-base font-bold">未启用的审批流程</span>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'operation'">
            <div class="inline-flex items-center">
              <Dropdown>
                <template #overlay>
                  <Menu>
                    <MenuItem
                      v-for="item in menuList"
                      :key="item.key"
                      @click="choseStep(record, item.key)"
                    >
                      {{ item.name }}
                    </MenuItem>
                  </Menu>
                </template>
                <Button
                  class="mr-2 flex items-center gap-1"
                  @click="choseStep(record, 0)"
                >
                  编辑
                  <VbenIcon class="size-4" icon="formkit:down" />
                </Button>
              </Dropdown>
              <Button class="mr-2" type="primary" @click="startData(record)">
                启用
              </Button>
              <Popconfirm
                cancel-text="取消"
                class="ml-2"
                ok-text="确定"
                title="您确定要删除该条审批流吗？"
                @confirm="delData(record.id)"
              >
                <Button danger type="primary"> 删除 </Button>
              </Popconfirm>
            </div>
          </template>
        </template>
      </Table>
    </Card>
    <NewApproval
      v-if="showNew"
      :data-ids="dataIds"
      :new-if="newIf"
      :step-val="stepVal"
      :unenable-data-length="unenableDataLength"
      @cancel-edit="cancelEdit"
      @on-save="onSave"
    />
  </div>
</template>

<style lang="scss" scoped></style>
