<!-- eslint-disable no-use-before-define -->
<script setup>
import { computed, inject, onUnmounted, ref, watch } from 'vue';

import { VbenIcon } from '@vben/common-ui';

import { Button, message, Upload } from 'ant-design-vue';
import Recorder from 'js-audio-recorder';

import { UploadImgApi } from '#/api/common';
import { getAudioSummaryApi } from '#/api/core/telephone';

const props = defineProps({
  modelValue: {
    type: [String, Object],
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
  // 添加这些props以支持AddModel的使用方式
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:modelValue']);

// 获取父组件的components数组，用于更新其他字段
const parentComponents = inject('parentComponents', null);

// 判断是否为编辑模式
const isEditMode = computed(() => {
  // 可以通过多种方式判断是否为编辑模式
  // 方法1：通过 parameters 中的标识
  if (props.isEdit) {
    return true;
  }
  return false;
});

// 状态管理
const isRecording = ref(false);
const recordingTime = ref(0);
const recorder = ref(null);
const audioUrl = ref(props.modelValue ?? '');
const recordingInterval = ref(null);
const audioElement = ref(null);

// 上传相关状态
const fileList = ref([]);
const uploadedAudioUrl = ref('');
// 音频来源类型：'recording' | 'upload'
const audioSource = ref(isEditMode.value ? 'upload' : '');

// 开始录音
const startRecording = async () => {
  if (isRecording.value) return;

  try {
    recorder.value = new Recorder();
    await recorder.value.start();
    isRecording.value = true;
    recordingTime.value = 0;

    // 计时
    recordingInterval.value = setInterval(() => {
      recordingTime.value++;
    }, 1000);
  } catch (error) {
    console.error('录音失败:', error);
    message.error('请允许麦克风权限以使用录音功能');
  }
};

// 停止录音
const stopRecording = () => {
  if (!isRecording.value || !recorder.value) return;

  recorder.value.stop();
  isRecording.value = false;
  clearInterval(recordingInterval.value);

  // 获取录音文件
  const blob = recorder.value.getWAVBlob();
  audioUrl.value = URL.createObjectURL(blob);
  audioSource.value = 'recording';

  // 更新表单值
  emits('update:modelValue', {
    blob,
    url: audioUrl.value,
    duration: recordingTime.value,
  });
};

// 切换录音状态
const toggleRecording = () => {
  if (isRecording.value) {
    stopRecording();
  } else {
    startRecording();
  }
};

// 格式化时间
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

const removeAudio = () => {
  audioUrl.value = null;
  uploadedAudioUrl.value = '';
  fileList.value = [];
  audioSource.value = '';
  emits('update:modelValue', null);
};

// 处理音频播放结束
const handleAudioEnded = () => {
  // 音频播放结束时的处理逻辑
  // 可以在这里添加播放结束后的逻辑
};

// 更新父组件中的字段值
const updateParentField = (fieldKey, value) => {
  let updated = false;

  // 方法1：通过注入的parentComponents更新
  if (parentComponents && parentComponents.value) {
    const targetField = parentComponents.value.find(
      (item) => item.key === fieldKey,
    );
    if (
      targetField && // 检查值是否真的发生了变化
      targetField.value !== value
    ) {
      const oldValue = targetField.value;
      targetField.value = value;
      updated = true;

      // 在开发环境下显示更新信息
      if (import.meta.env.DEV) {
        // eslint-disable-next-line no-console
        console.log(`字段 ${fieldKey} 已更新:`, {
          from: oldValue,
          to: value,
          fieldName: targetField.name || fieldKey,
        });
      }

      // 触发Vue的响应式更新
      // 由于我们直接修改了响应式对象，Vue会自动处理更新
      // 这里可以添加额外的通知机制，比如触发自定义事件
    }
  }

  return updated;
};

// 获取音频摘要
const getAudioSummary = async (audioUrl) => {
  try {
    message.loading('正在生成音频摘要...', 0);

    const { data } = await getAudioSummaryApi({
      voice_file_path: audioUrl,
    });

    message.destroy();

    // 更新表单中的字段
    if (data) {
      let updateCount = 0;

      // 字段映射：API返回字段 -> 表单字段名
      const fieldMappings = [
        { apiField: 'all_text', formFields: ['all_text', 'audio_text'] },
        {
          apiField: 'summary_text',
          formFields: ['summary_text', 'audio_summary'],
        },
      ];

      // 遍历字段映射进行更新
      fieldMappings.forEach(({ apiField, formFields }) => {
        if (data[apiField]) {
          formFields.forEach((fieldKey) => {
            if (updateParentField(fieldKey, data[apiField])) {
              updateCount++;
            }
          });
        }
      });

      if (updateCount > 0) {
        message.success(`音频摘要生成成功，已更新 ${updateCount} 个字段`);
      } else {
        message.success('音频摘要生成成功');
      }
    } else {
      message.warning('音频摘要生成成功，但未返回数据');
    }

    return data;
  } catch (error) {
    message.destroy();
    message.error('音频摘要生成失败');
    console.error('音频摘要生成失败:', error);
    throw error;
  }
};

// 上传录音文件
const uploadRecording = async () => {
  if (!audioUrl.value || !recorder.value) return;

  try {
    const blob = recorder.value.getWAVBlob();
    const fileParams = new FormData();
    fileParams.append('file', blob, 'recording.wav');
    fileParams.append('file_rel_id', 'id_test');
    fileParams.append('file_rel_Object', 'object_test');
    fileParams.append('file_max_num', 1);

    const { data } = await UploadImgApi(fileParams);

    // 更新音频URL和来源类型
    audioUrl.value = data.url;
    audioSource.value = 'upload';

    message.success('录音上传成功');

    // 更新表单值为上传后的URL
    emits('update:modelValue', {
      url: data.url,
      type: 'upload',
      duration: recordingTime.value,
    });

    // 上传成功后自动获取音频摘要
    await getAudioSummary(data.url);
  } catch (error) {
    message.error('录音上传失败');
    console.error('录音上传失败:', error);
  }
};

// 上传音频文件
async function customRequest(file) {
  const fileParams = new FormData();
  fileParams.append('file', file.file);
  fileParams.append('file_rel_id', 'id_test');
  fileParams.append('file_rel_Object', 'object_test');
  fileParams.append('file_max_num', 1);

  try {
    const { data } = await UploadImgApi(fileParams);
    fileList.value = [{ url: data.url, name: data.url, status: 'done' }];
    uploadedAudioUrl.value = data.url;
    audioUrl.value = data.url; // 统一使用audioUrl来显示音频
    audioSource.value = 'upload';
    message.success('上传成功');

    // 更新表单值
    emits('update:modelValue', {
      url: data.url,
      type: 'upload',
    });

    // 上传成功后自动获取音频摘要
    await getAudioSummary(data.url);
  } catch (error) {
    fileList.value = [];
    message.error('上传失败');
    console.error('上传失败:', error);
  }
}

// 监听文件列表变化
watch(fileList, (newValue) => {
  if (newValue.length === 0 && uploadedAudioUrl.value) {
    uploadedAudioUrl.value = '';
    if (audioUrl.value === uploadedAudioUrl.value) {
      audioUrl.value = '';
    }
    emits('update:modelValue', null);
  }
});

// 组件卸载时清理资源
onUnmounted(() => {
  if (isRecording.value) {
    stopRecording();
  }

  if (recordingInterval.value) {
    clearInterval(recordingInterval.value);
  }

  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value);
  }
});
</script>

<template>
  <div>
    <!-- 录音和上传按钮区域 -->
    <div v-if="!audioUrl" class="flex items-center gap-2">
      <!-- 录音按钮 -->
      <div class="flex cursor-pointer" @click="toggleRecording">
        <div class="card-box flex items-center gap-1 rounded-full p-2">
          <VbenIcon
            v-if="isRecording"
            class="size-5 text-[red]"
            icon="material-symbols-light:stop"
          />
          <VbenIcon v-else class="size-5" icon="ant-design:audio-twotone" />

          <span>{{
            isRecording ? `停止录音 ${formatTime(recordingTime)}` : '开始录音'
          }}</span>
        </div>
      </div>

      <!-- 上传按钮 -->
      <Upload
        v-model:file-list="fileList"
        :custom-request="customRequest"
        accept="audio/*"
        action
        name="file"
      >
        <Button v-if="fileList.length === 0">+上传音频</Button>
      </Upload>
    </div>

    <!-- 音频播放区域 -->
    <div v-if="audioUrl" class="flex items-center gap-2">
      <audio
        ref="audioElement"
        :src="audioUrl"
        controls
        @ended="handleAudioEnded"
      ></audio>
      <div class="flex gap-2">
        <!-- 如果是录音，显示上传按钮（添加模式）或更新按钮（编辑模式） -->
        <Button
          v-if="audioSource === 'recording'"
          size="small"
          type="primary"
          @click="uploadRecording"
        >
          {{ isEditMode ? '更新' : '上传' }}
        </Button>
        <!-- 如果是已上传的音频且在编辑模式，显示更新按钮 -->
        <Button
          v-if="audioSource === 'upload' && isEditMode"
          size="small"
          type="primary"
          @click="() => getAudioSummary(audioUrl)"
        >
          更新
        </Button>
        <Button danger size="small" type="default" @click="removeAudio">
          清除
        </Button>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
