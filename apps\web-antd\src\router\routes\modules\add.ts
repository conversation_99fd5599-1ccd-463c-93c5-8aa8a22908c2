import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layout-dashboard',
      order: 100,
      title: '新增',
      hideInMenu: true,
    },
    name: 'Add',
    path: '/add',
    children: [
      {
        name: 'report',
        path: '/add/report',
        component: () => import('#/views/sys/add/ReportAdd.vue'),
        meta: {
          icon: 'proicons:add',
          keepAlive: true,
          title: '报表新增',
        },
      },
      // ...其他新增模块
    ],
  },
];

export default routes;
