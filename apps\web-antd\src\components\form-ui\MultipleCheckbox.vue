<script setup>
import { ref, watch } from 'vue';

import { Checkbox, CheckboxGroup } from 'ant-design-vue';

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

// const emits = defineEmits(['update:modelValue']);

const checkValue = ref(props.modelValue);

// const changeValue = () => {
//   emits('update:modelValue', inputValue.value);
// };
watch(
  () => props.modelValue,
  (newValue) => {
    inputValue.value = newValue;
  },
);
</script>

<template>
  <CheckboxGroup v-model:value="checkValue" style="width: 100%">
    <Checkbox value="B">B</Checkbox>
    <Checkbox value="A">A</Checkbox>
    <Checkbox value="C">C</Checkbox>
    <Checkbox value="D">D</Checkbox>
    <Checkbox value="F">F</Checkbox>
  </CheckboxGroup>
</template>

<style></style>
