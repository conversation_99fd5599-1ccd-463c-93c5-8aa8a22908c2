<script setup>
import { ref, watch } from 'vue';

import { Checkbox } from 'ant-design-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const checkValue = ref(null);

checkValue.value = props.modelValue || null;

watch(checkValue, (newValue) => {
  emits('update:modelValue', newValue);
});
</script>

<template>
  <Checkbox
    v-model:checked="checkValue"
    :disabled="props.parameters.field_readonly"
  >
    {{ props.parameters.Field_Help_Text }}
  </Checkbox>
</template>

<style></style>
