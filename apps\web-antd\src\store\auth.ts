import type { LoginAndRegisterParams } from '@vben/common-ui';
import type { UserInfo, UserInfoData } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { DEFAULT_HOME_PATH, LOGIN_PATH } from '@vben/constants';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { notification } from 'ant-design-vue';
import { jwtDecode } from 'jwt-decode';
import { defineStore } from 'pinia';

import { loginApi } from '#/api';
import { $t } from '#/locales';

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();

  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLogin(
    params: LoginAndRegisterParams,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const { data } = await loginApi(params);

      const accessToken = data.access_token;
      // 如果成功获取到 accessToken
      if (accessToken) {
        accessStore.setAccessToken(accessToken);

        const userInfoData: UserInfoData = jwtDecode(accessToken);
        userInfo = {
          userId: userInfoData.userId,
          realName: userInfoData.username,
          roles: ['super'],
          username: userInfoData.username,
          token: accessToken,
          roleId: userInfoData.role,
        };
        const accessCodes = [
          'AC_100100',
          'AC_100110',
          'AC_100120',
          'AC_100010',
        ];

        userStore.setUserInfo(userInfo);
        accessStore.setAccessCodes(accessCodes);

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(userInfo.homePath || DEFAULT_HOME_PATH);
        }

        if (userInfo?.realName) {
          notification.success({
            description: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            duration: 3,
            message: $t('authentication.loginSuccess'),
          });
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登陆页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }

  async function fetchUserInfo() {
    let userInfo: null | UserInfo = null;
    // userInfo = await getUserInfoApi();
    const accessToken = accessStore.accessToken || '';
    const userInfoData: UserInfoData = jwtDecode(accessToken);
    userInfo = {
      userId: userInfoData.userId || '0',
      realName: userInfoData.username || 'admin',
      roles: ['super'],
      username: userInfoData.username || 'admin',
      token: accessToken,
      roleId: userInfoData.role || '',
    };
    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
  };
});
