<script setup>
import { computed, onMounted, ref, watch } from 'vue';

import { VbenSpinner } from '@vben/common-ui';

import {
  Button,
  Card,
  Form,
  FormItem,
  message,
  Radio,
  RadioGroup,
  Select,
} from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { checkPythonApi, fieldObjEditApi, getFieldMsgApi } from '#/api';
import {
  Checkbox,
  InputInteger,
  InputText,
  InputTextArea,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const props = defineProps({
  fieldId: {
    type: String,
    default: null,
  },
  twoacceptData: {
    type: Object,
    default: () => ({}),
  },
  formDatanew: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['cancelEdit', 'toThree', 'toOne']);

const { isLoading, startLoading, stopLoading } = useLoading();

// 表单数据对象
const formData = ref({});
// 字段id
const fielded = ref('');
// 字段类型
const fieldType = ref('');
// 是否是新建页面
const newIf = ref(false);
// 显示表单
const showForm = ref(false);
onMounted(async () => {
  fielded.value = props.fieldId || '';
  if (props.twoacceptData && Object.keys(props.twoacceptData).length > 0) {
    fieldType.value = props.twoacceptData?.type;
    newIf.value = props.twoacceptData?.new;
    formData.value = props.formDatanew;
    formData.value.Field_type = fieldType.value;
    stopLoading();
    createForm();
    showForm.value = true;
  } else {
    await getFieldDetail();
  }
});

// 获取字段详情信息
async function getFieldDetail() {
  startLoading();
  const { data } = await getFieldMsgApi(fielded.value || '');
  data.Field_error_condition = Number(data.Field_error_condition);
  formData.value = data;
  createForm();
  stopLoading();
  showForm.value = true;
}

// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Checkbox') return Checkbox;
  if (type === 'integer') return InputInteger;
}
// 表单校验
const formRef = ref();
const rules = {
  Field_name: [{ required: true, message: '请输入字段名!', trigger: 'blur' }],
  Field_id: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (!value) {
          throw new Error('请输入字段标签API!');
        }
        const regex = /^[A-Z]\w*$/i;
        if (!regex.test(value)) {
          throw new Error('格式错误!');
        }
      },
      trigger: 'blur',
    },
  ],
  Field_length: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
  Field_orderby: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
};

// ==============表单属性==========
const formList = ref([]);
const modelForm = computed(() => {
  // 处理modalForm值
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
// 同步更新fromData对象值
watch(
  () => formList.value,
  (newData) => {
    newData.forEach((item) => {
      if (formData.value[item.field_api] !== undefined) {
        formData.value[item.field_api] = item.value;
      }
    });
  },
  { deep: true }, // 深度监听
);

async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: true,
      Field_Help_Text: '字段类型',
      value: newIf.value ? fieldType.value : formData.value.Field_type.id,
      title: '字段类型',
      field_api: '',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入字段名称',
      value: formData.value.Field_name,
      title: '字段名称',
      field_api: 'Field_name',
      sort: 2,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入以字母和下划线组合的字符',
      value: formData.value.Field_id,
      title: '字段标签API',
      field_api: 'Field_id',
      sort: 3,
    },
    {
      type: 'integer',
      field_readonly: false,
      Field_Help_Text: '请输入序号',
      value: formData.value.Field_orderby,
      title: '字段排序',
      field_api: 'Field_orderby',
      sort: 20,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.Field_Active,
      title: '字段启用',
      field_api: 'Field_Active',
      sort: 20,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: formData.value.Field_Description,
      title: '描述',
      Field_Help_Text: '请输入...',
      field_api: 'Field_Description',
      sort: 60,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.Field_Help_Text,
      title: '帮助文本',
      field_api: 'Field_Help_Text',
      sort: 70,
    },
    {
      type: 'Formula',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.Field_Formula_Content,
      title: '公式计算',
      field_api: 'Field_Formula_Content',
      sort: 50,
    },
    {
      type: 'Error',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.Field_error_condition,
      title: '错误条件公式',
      field_api: 'Field_error_condition',
      sort: 80,
    },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
}

// 选择运算符值
const operatorVal = ref();
const operatorHolder = ref('选择运算符');
const operatorOpts = ref([
  {
    label: '等于',
    value: '=',
  },
  {
    label: '不等于',
    value: '!=',
  },
  {
    label: '大于',
    value: '>',
  },
  {
    label: '大于等于',
    value: '>=',
  },
  {
    label: '小于',
    value: '<',
  },
  {
    label: '小于等于',
    value: '<=',
  },
]);

// 选择插入函数值
const functionVal = ref();
const functionHolder = ref('插入函数');
const functionOpts = ref([
  {
    label: 'IF',
    value: 'if :\n  \nelse:\n',
  },
  {
    label: 'ACOS',
    value: 'ACOS',
  },
  {
    label: 'ADDMONTHS',
    value: 'ADDMONTHS',
  },
  {
    label: 'AND',
    value: 'AND',
  },
  {
    label: 'ASCLL',
    value: 'ASCLL',
  },
  {
    label: 'ASIN',
    value: 'ASIN',
  },
]);

const changeOperator = (value) => {
  const formulaItem = formList.value.find((item) => item.title === '公式计算');

  if (formulaItem) {
    formulaItem.value = formulaItem.value ?? '';
    formulaItem.value += value || '';
    formData.value.Field_Formula_Content = formulaItem.value;
  }
};
const changeFunction = (value) => {
  const formulaItem = formList.value.find((item) => item.title === '公式计算');

  if (formulaItem) {
    formulaItem.value = formulaItem.value ?? '';
    formulaItem.value += value || '';
    formData.value.Field_Formula_Content = formulaItem.value;
  }
};

// 保存编辑
function containsChinese(str) {
  const pattern = /[\u4E00-\u9FA5]/; // 中文字符的起始编码到结束编码
  return pattern.test(str);
}
const serveData = async () => {
  const data = cloneDeep(formData.value);
  for (const key in data) {
    if (data[key] !== null && typeof data[key] === 'object') {
      data[key] = containsChinese(data[key].name)
        ? data[key].id
        : data[key].name;
    }
  }
  await fieldObjEditApi(data);
  message.success('保存成功');
  emits('cancelEdit');
};
// 检查语法
const grammar_active = ref(false);
const checkPython = async () => {
  const { data } = await checkPythonApi({
    python_str: formData.value.Field_Formula_Content,
  });
  if (data === true) {
    message.success('语法正确！');
    grammar_active.value = false;
  } else {
    message.error('语法错误！');
    grammar_active.value = true;
  }
};

async function saveEdit() {
  formRef.value
    .validateFields() // 触发校验
    .then(async () => {
      await checkPython();
      if (grammar_active.value) return;
      serveData();
    })
    .catch(() => {});
}

// 取消编辑
async function cancelEdit() {
  emits('cancelEdit');
}

// 上一步
async function toOne() {
  emits('toOne');
}

// 下一步
async function toThree() {
  formData.value.Field_type = fieldType.value;
  formRef.value
    .validateFields() // 触发校验
    .then(async () => {
      await checkPython();
      if (grammar_active.value) return;
      emits('toThree', formData.value);
    })
    .catch(() => {});
}
</script>

<template>
  <div>
    <Card :title="newIf ? '输入字段详细信息' : '编辑自定义字段'">
      <template #extra>
        <div v-if="!newIf" class="flex gap-6">
          <Button type="primary" @click="saveEdit"> 保存 </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
        <div v-if="newIf" class="flex gap-4">
          <Button type="primary" @click="toOne"> 上一步 </Button>
          <Button type="primary" @click="toThree"> 下一步 </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
      </template>
      <template v-if="showForm">
        <div
          :style="{ height: newIf ? '58vh' : '70vh' }"
          class="overflow-y-auto"
        >
          <Form
            ref="formRef"
            :label-col="{ span: 4 }"
            :model="modelForm"
            :rules="rules"
            :wrapper-col="{
              span: 14,
            }"
            name="nestMessages"
          >
            <template v-for="(item, index) in formList" :key="index">
              <FormItem
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <div
                  v-if="item.type !== 'Formula' && item.type !== 'Error'"
                  class="col-span-6 !w-[70%]"
                >
                  <component
                    :is="getComponent(item.type)"
                    v-model="item.value"
                    :clear="true"
                    :parameters="item"
                    class="!w-[100%]"
                  />
                </div>
                <template v-if="item.type === 'Formula'">
                  <div class="col-span-6">
                    <div>示例：result = 1</div>
                    <div class="flex">
                      <div>计算示例：</div>
                      <div class="flex-1">
                        from decimal import Decimal<br />result =
                        Decimal(str([@字段值1])) * Decimal(str([@字段值2]))
                      </div>
                    </div>
                    <div class="my-6 flex !w-[70%] gap-6">
                      <Button type="primary">插入字段</Button>
                      <FormItem :name="`${operatorVal}`" no-style>
                        <Select
                          v-model:value="operatorVal"
                          :options="operatorOpts"
                          :placeholder="operatorHolder"
                          @select="changeOperator"
                        />
                      </FormItem>
                      <FormItem :name="`${functionVal}`" no-style>
                        <Select
                          v-model:value="functionVal"
                          :options="functionOpts"
                          :placeholder="functionHolder"
                          @select="changeFunction"
                        />
                      </FormItem>
                    </div>
                    <div class="my-6 !w-[70%]">
                      <component
                        :is="getComponent('TextArea')"
                        v-model="item.value"
                        :clear="true"
                        :parameters="item"
                        :rows="5"
                        class="col-span-6"
                      />
                    </div>
                    <div class="mt-6 flex !w-[70%] justify-end">
                      <Button type="primary" @click="checkPython">
                        检查语法
                      </Button>
                    </div>
                  </div>
                </template>
                <template v-if="item.type === 'Error'">
                  <div class="col-span-6">
                    <div class="flex">
                      <div class="mt-1.5">
                        如果您的公式引用了任何数字、货币和百分比字段，请指定当这些字段值为空时如何处理公式输出。
                      </div>
                    </div>
                    <div class="mt-3 !w-[70%]">
                      <RadioGroup v-model:value="item.value">
                        <Radio :value="1" class="flex h-8 leading-8">
                          将空白字段视为零
                        </Radio>
                        <Radio :value="2" class="flex h-8 leading-8">
                          将空白字段视为空白
                        </Radio>
                      </RadioGroup>
                    </div>
                  </div>
                </template>
              </FormItem>
            </template>
          </Form>
        </div>
      </template>
    </Card>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
