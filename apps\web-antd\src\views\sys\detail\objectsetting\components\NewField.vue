<script setup>
import { onMounted, reactive, ref } from 'vue';

import { Card, Steps } from 'ant-design-vue';

import StepOne from '../newsteps/StepOne.vue';
import StepThree from '../newsteps/StepThree.vue';
import StepTwo from '../newsteps/StepTwo.vue';

// 定义 props 接收父组件传递的页数
// const props = defineProps({
//   pageData: {
//     type: Object,
//     default: () => {},
//   },
// });

// 字段编辑
const emits = defineEmits(['cancelField', 'saveField']);

// 步骤条
const steps = [
  {
    key: 1,
    title: '步骤一：选择字段类型',
    content: '步骤一. 选择字段类型',
  },
  {
    key: 2,
    title: '步骤二：输入详细信息',
    content: '步骤二：输入详细信息',
  },
  {
    key: 3,
    title: '步骤三：建立字段安全性',
    content: '步骤三 建立字段安全性',
  },
];
const current = ref(0);

// 取消按钮
const cancelEdit = () => {
  emits('cancelField');
};

// 新建完成
const saveFinish = () => {
  emits('saveField');
};

// 第二步接收数据
let twoacceptData = reactive({});
let saveTwodata = reactive({});

// 字段类型
const fieldType = ref('');

// 到第二步
const toTwo = (data, twodata) => {
  twoacceptData = {
    type: data?.type,
    new: true,
  };
  fieldType.value = data?.type;
  saveTwodata = twodata;
  current.value = 1;
};
// 到第一步
const toOne = () => {
  current.value = 0;
};
// 到第三步
let formDatanews = reactive({});
const toThree = (data) => {
  formDatanews = data;
  current.value = 2;
};

onMounted(() => {});
</script>

<template>
  <div>
    <Card title="新建自定义字段">
      <Steps :current="current" :items="steps" class="mb-6" disabled="true" />
      <!-- <Divider class="h-px bg-stone-100" /> -->
      <StepOne
        v-if="current === 0"
        :field-type="fieldType"
        @cancel-edit="cancelEdit"
        @to-two="toTwo"
      />
      <StepTwo
        v-if="current === 1"
        :save-twodata="saveTwodata"
        :twoaccept-data="twoacceptData"
        @cancel-edit="cancelEdit"
        @to-one="toOne"
        @to-three="toThree"
      />
      <StepThree
        v-if="current === 2"
        :form-datanews="formDatanews"
        :twoaccept-data="twoacceptData"
        @cancel-edit="cancelEdit"
        @save-finish="saveFinish"
        @to-two="toTwo"
      />
    </Card>
  </div>
</template>

<style scoped>
/* 修改步骤条字号 */
::v-deep(.ant-steps-item-title) {
  font-size: 15px;
}
</style>
