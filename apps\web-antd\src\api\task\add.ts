import { requestClient } from '#/api/request';

/**
 *
 * @returns 获取应用包表格头部
 */
export async function findHeader<PERSON><PERSON>(object_id: string, field: string) {
  return requestClient.get(`/relationship/table_header`, {
    params: {
      object_id,
      field,
    },
  });
}

/**
 *
 * @returns 获取应用包表格内容
 */
export async function findCorrelationApi(
  object: string,
  field: string,
  data: object,
) {
  return requestClient.post(`/${object}/relationship/${field}/`, data);
}

/**
 *
 * @returns 新建作业提交接口
 */
export async function postAddWorkApi(data: object) {
  return requestClient.post('processautomation/add', data);
}

/**
 *
 * @returns 获取对象列表
 */
export async function getObjectListApi() {
  return requestClient.get('uniapp/jsonstr');
}

/**
 * @returns 发布作业
 */
export async function publishWorkApi(data: object) {
  return requestClient.get('rtc/exe_single_work', { params: data });
}
