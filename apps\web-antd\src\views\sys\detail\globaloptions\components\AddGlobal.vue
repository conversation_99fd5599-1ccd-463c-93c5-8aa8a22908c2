<script setup>
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import { Form, FormItem, message } from 'ant-design-vue';
import { ColorPicker } from 'vue3-colorpicker';

import { addGlobalapi } from '#/api';
import {
  Checkbox,
  InputInteger,
  InputNumber,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
} from '#/components/form-ui';

import 'vue3-colorpicker/style.css';

const emits = defineEmits(['newOpt']);

const router = useRoute();
// 表数据
const objForm = ref({
  global_selectoption_Field_id: router.params.id,
  selectoption_Values: null,
  selectoption_id: null,
  selectoption_orderby: null,
  selectoption_color: 'transparent',
  selectoption_default: false,
  selectoption_Active: false,
});
const formRef = ref();
// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'integer') return InputInteger;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
  if (type === 'Checkbox') return Checkbox;
}

const formList = ref([]);
async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入值',
      value: objForm.value.selectoption_Values,
      title: '值',
      field_api: 'selectoption_Values',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入API名称',
      value: objForm.value.selectoption_id,
      title: 'API名称',
      field_api: 'selectoption_id',
      sort: 2,
    },
    {
      type: 'integer',
      field_readonly: false,
      Field_Help_Text: '请输入序号',
      value: objForm.value.selectoption_orderby,
      title: '排序',
      field_api: 'selectoption_orderby',
      sort: 20,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: objForm.value.selectoption_Active,
      title: '是否启用',
      field_api: 'selectoption_Active',
      sort: 22,
    },
    {
      type: 'Color',
      field_readonly: false,
      value: objForm.value.selectoption_color,
      title: '背景颜色',
      field_api: 'selectoption_color',
      sort: 25,
    },
    // {
    //   type: 'Checkbox',
    //   field_readonly: false,
    //   value: objForm.value.selectoption_default,
    //   title: '默认选中',
    //   field_api: 'selectoption_default',
    //   sort: 30,
    // },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
}

const rules = {
  selectoption_Values: [
    { required: true, message: '请输入值!', trigger: 'blur' },
  ],
  selectoption_id: [
    { required: true, message: '请输入API名称!', trigger: 'blur' },
  ],
  selectoption_orderby: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (typeof value !== 'number' || !value) {
          throw new TypeError('请输入数字!');
        }
      },
      trigger: 'blur',
    },
  ],
  Owning_application_package: [
    { required: true, message: '请选择应用包!', trigger: 'blur' },
  ],
  selectoption_Active: [
    { required: true, message: '请选择是否启用!', trigger: 'blur' },
  ],
  selectoption_color: [
    { required: true, message: '请选择背景颜色!', trigger: 'blur' },
  ],
};
const modelForm = computed(() => {
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
const isFirstLoad = ref(true);
watch(
  () => formList.value,
  (newData) => {
    if (isFirstLoad.value === true) {
      isFirstLoad.value = false; // 更新标志变量以表示已初始化
      return; // 初次加载时不执行任何操作
    }
    newData.forEach((item) => {
      objForm.value[item.field_api] = item.value;
    });
  },
  { deep: true }, // 深度监听
);

const [mainModal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[680px]',
  contentClass: 'p-4',
  appendToMain: true,
  confirmText: '确定',
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    formRef.value
      .validateFields() // 触发校验
      .then(async () => {
        await addGlobalapi(objForm.value);
        message.success('添加成功');
        modalApi.close();
        emits('newOpt');
      })
      .catch(() => {});
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      objForm.value = {
        global_selectoption_Field_id: router.params.id,
        selectoption_Values: null,
        selectoption_id: null,
        selectoption_orderby: null,
        selectoption_color: 'transparent',
        selectoption_default: false,
        selectoption_Active: false,
      };
      formList.value = [];
      isFirstLoad.value = true;
      createForm();
    }
  },
});
</script>

<template>
  <div>
    <mainModal title="添加选项值">
      <div class="flex flex-col gap-3 p-2">
        <Form
          ref="formRef"
          :label-col="{ span: 5 }"
          :model="modelForm"
          :rules="rules"
          :wrapper-col="{ span: 15 }"
          autocomplete="off"
          name="basic"
        >
          <div class="grid">
            <template v-for="(item, index) in formList" :key="index">
              <FormItem
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <component
                  :is="getComponent(item.type)"
                  v-model="item.value"
                  :parameters="item"
                  class="col-span-6 w-[100%]"
                  object-id="Object"
                  object-name="object"
                />
                <ColorPicker
                  v-if="item.type === 'Color'"
                  v-model:pure-color="item.value"
                  format="hex6"
                  picker-type="chrome"
                  style="width: 100px"
                />
              </FormItem>
            </template>
          </div>
        </Form>
      </div>
    </mainModal>
  </div>
</template>

<style lang="scss" scoped></style>
