<!-- eslint-disable no-use-before-define -->
<script setup>
import { h, ref, toRaw } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';

import { SendOutlined } from '@ant-design/icons-vue';
import {
  Button,
  Collapse,
  CollapsePanel,
  Select,
  Skeleton,
  Step,
  Steps,
  Table,
  Tooltip,
} from 'ant-design-vue';
import { Sender, theme } from 'ant-design-x-vue';
import * as echarts from 'echarts';

import { autoEchartsTableApi, getTableColumnApi, getTableDataApi } from '#/api';
import { reportHandSaveApi } from '#/api/report';

const props = defineProps({
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
  // 详情页添加使用的参数
  addDefult: {
    type: Object,
    default: () => null,
    required: false,
  },
  // 详情页添加使用的参数
  columnList: {
    type: Array,
    default: () => [],
    required: false,
  },
});

const router = useRouter();

const { token } = theme.useToken();
const [Modal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[60vw]',
  contentClass: 'p-8',
  closeOnClickModal: false,
  footer: false,
  onOpenChange(isOpen) {
    if (isOpen) {
      current.value = 0;
      inputValue.value = '';
      getFelidsMessage();
      getModelList();
    }
  },
});
const route = useRoute();
const felidList = ref([]);
const current = ref(0);
// const formRef = ref(null);
const components = ref([]);
const skeletonLoading = ref(true);
const modelList = ref([]);
const chooseModel = ref(null);
const chooseList = ref([]);
const inputValue = ref('');
const activeKey = ref([]);
const explain = ref('');
const fieldsColumns = ref([
  {
    title: '字段',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '字段类型',
    dataIndex: 'typeName',
    key: 'typeName',
  },
]);
const option = ref(null);

const selectedRowKeys = ref([]);
// 表格配置项
const rowSelection = {
  selectedRowKeys,
  onChange: (selectedRowKeys, selectedRows) => {
    onSelectChange(selectedRowKeys, selectedRows);
  },
};

// 获取字段模版信息
async function getFelidsMessage() {
  const { data } = await getTableColumnApi(props.objectName);
  felidList.value = data.slice(0, -6) || []; // slice操作去除后面几项，例如：修改人，操作时间等等
  const fieldTypeMapping = {
    Text: '文本类型',
    Number: '数字类型',
    Date: '日期类型',
    Date_Time: '日期和时间类型',
    Checkbox: '单选框类型',
    dropdownlist: '下拉选择框类型',
    Text_Area: '多行文本类型',
    Text_Area_Rich: '富文本类型',
    integer: '整数类型',
    group: '分组类型',
    Lookup_Relationship: '查找相关类型',
    Time: '时间类型',
  };
  selectedRowKeys.value = felidList.value.map((item) => item.key);
  felidList.value.forEach((item) => {
    item.typeName = fieldTypeMapping[item.type] || item.type;
  });
  chooseList.value = felidList.value.map((item) => {
    return {
      id: item.id,
      name: item.name,
      type: item.type,
      key: item.key,
      field_length: item.field_length,
      field_visible: item.field_visible,
      field_required: item.field_required,
      Field_Help_Text: item.Field_Help_Text,
    };
  });
}

// 获取大模型类型
async function getModelList() {
  const { data } = await getTableDataApi('ai_engine');
  modelList.value = data.data.map((item) => {
    return item.engine_name === 'deepseek-r1'
      ? {
          label: `${item.engine_name}(深度思考)`,
          value: item.engine_key_name.id,
        }
      : {
          label: item.engine_name,
          value: item.engine_key_name.id,
        };
  });
  chooseModel.value = modelList.value[0].value;
}

// 提交
async function submitVal() {
  current.value = 1;
  components.value = [];
  skeletonLoading.value = true;
  chooseList.value = chooseList.value.map((item) => {
    return toRaw(item);
  });

  felidList.value = felidList.value.map((item) => {
    return {
      id: item.id,
      name: item.name,
      type: item.type,
      key: item.key,
      field_length: item.field_length,
      field_visible: item.field_visible,
      field_required: item.field_required,
      Field_Help_Text: item.Field_Help_Text,
    };
  });

  const AIParams = {
    ai_mode: chooseModel.value,
    object_name: route.meta.title,
    object_uid: props.objectId,
    object_id: props.objectName,
    chart_type: ['柱状图'],
    input_content: inputValue.value,
    fields_content: toRaw(felidList.value),
  };

  const { data } = await autoEchartsTableApi(JSON.stringify(AIParams));

  option.value = JSON.parse(data[0].esCode);
  option.value.grid = {
    left: '0px',
    right: '0px',
    bottom: '0px',
    top: '0px',
  };
  const chartDom = document.querySelector('#mychart');
  const myChart = echarts.init(chartDom);
  myChart.setOption(option.value);
  explain.value = data[0].explain;
  skeletonLoading.value = false;
}

// 完成
async function finish() {
  modalApi.close();
  const data = {
    report_name: option.value.title.text,
    report_Object: props.objectName,
    report_chart_type: option.value.series[0].type,
    report_ai_content: JSON.stringify(option.value),
  };
  await reportHandSaveApi(data);
}

// 表格选择
function onSelectChange(newSelectedRowKeys, e) {
  selectedRowKeys.value = newSelectedRowKeys;
  chooseList.value = e;
}

// 上一步
const lastStep = async () => {
  current.value = 0;
};

const toEdit = () => {
  modalApi.close();
  localStorage.setItem('options', JSON.stringify(option.value));
  localStorage.setItem('aiObjectName', props.objectName);
  router.push({
    name: 'aireport',
  });
};
</script>

<template>
  <Modal>
    <template #title>
      <div class="flex gap-2">
        <span>AI智能报表</span>
      </div>
    </template>
    <Steps :current="current" class="mb-[10px]" type="navigation">
      <Step title="输入内容" @click="() => (currentStep = 0)" />
      <Step disabled title="生成报表" />
    </Steps>
    <div v-if="current === 0">
      <Collapse v-model:active-key="activeKey" ghost>
        <CollapsePanel key="1" header="选择字段">
          <Table
            :columns="fieldsColumns"
            :data-source="felidList"
            :pagination="{ pageSize: 5 }"
            :row-selection="rowSelection"
            size="small"
          />
        </CollapsePanel>
      </Collapse>
      <Sender
        v-model:value="inputValue"
        class="h-[200px]"
        placeholder="例：帮我生成一个报表，类型是柱状图"
        @cancel="() => {}"
        @submit="
          (msg) => {
            submitVal();
            value = '';
          }
        "
      >
        <template #footer>
          <Select
            v-model:value="chooseModel"
            :options="modelList"
            class="absolute bottom-[10px] w-[200px]"
            @change="
              (val) => {
                console.log(val);
              }
            "
          />
        </template>

        <template
          #actions="{
            info: {
              components: { SendButton },
            },
          }"
        >
          <Tooltip title="确认生成">
            <component
              :is="SendButton"
              :icon="h(SendOutlined)"
              :style="{ color: token.colorPrimary, fontSize: '15px' }"
              shape="default"
              type="text"
            />
          </Tooltip>
        </template>
      </Sender>
    </div>
    <div v-if="current === 1">
      <Skeleton :loading="skeletonLoading" active class="mb-[20px]" />
      <div v-show="!skeletonLoading">
        <div id="mychart" class="mb-[20px] h-[300px] w-[800px]"></div>
        <div class="card-box mb-[10px] p-[10px]">图表说明：{{ explain }}</div>
      </div>
      <div class="flex justify-between gap-2">
        <Button type="primary" @click="lastStep"> 上一步 </Button>
        <div v-show="!skeletonLoading">
          <Button class="mr-[20px]" type="primary" @click="toEdit">
            报表编辑
          </Button>
          <Button type="primary" @click="finish"> 完成 </Button>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style lang="scss" scoped></style>
