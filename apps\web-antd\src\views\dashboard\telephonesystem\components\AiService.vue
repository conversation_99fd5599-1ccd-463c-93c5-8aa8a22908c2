<!-- eslint-disable no-use-before-define -->
<script setup>
import { onMounted, ref } from 'vue';

import { Icon } from '@vben/icons';

import { Avatar, Spin } from 'ant-design-vue';

const emits = defineEmits(['getDiscernResult']);

// 语音识别是否加载中
const discernLoading = ref(true);

// 识别语音文字
const discernText = ref(null);

// 查询知识库状态
const ThinkingStatus = ref(true);

// 识别提取的知识库结果
const discernResult = ref([]);

// 识别语音-1
const discernVoice = () => {
  discernLoading.value = true;
  setTimeout(() => {
    discernText.value = '报表系统人员整理数据';
    discernLoading.value = false;
    queryKnowledgeBase();
  }, 1000);
};

// 查询知识库
const queryKnowledgeBase = () => {
  ThinkingStatus.value = true;
  setTimeout(() => {
    discernResult.value = [
      {
        id: 1,
        name: '报表系统人员整理数据',
        content:
          '模拟摘要信息：用户咨询了 xxxx 功能的实现方法，可能涉及报表系统数据整理、新人注册操作手册以及作业配置对象设置步骤等方面。语音助手建议用户参考相关文档和操作指南来解决问题。',
      },
    ];
    ThinkingStatus.value = false;
    sendResult({
      text: discernText.value,
      result: discernResult.value,
    });
  }, 1500);
};

setTimeout(() => {
  discernVoice2();
}, 5000);

// 识别语音-1
const discernVoice2 = () => {
  discernLoading.value = true;
  setTimeout(() => {
    discernText.value = '您好，请问你是想查询xxx吗？';
    discernLoading.value = false;
    queryKnowledgeBase2();
  }, 3000);
};

// 查询知识库
const queryKnowledgeBase2 = () => {
  ThinkingStatus.value = true;
  setTimeout(() => {
    discernResult.value = [
      {
        id: 1,
        name: '报表系统人员整理数据',
        content:
          '模拟摘要信息：用户咨询了 xxxx 功能的实现方法，可能涉及报表系统数据整理、新人注册操作手册以及作业配置对象设置步骤等方面。语音助手建议用户参考相关文档和操作指南来解决问题。',
      },
      {
        id: 2,
        name: '报表系统人员整理数据',
        content:
          '模拟摘要信息：用户咨询了 xxxx 功能的实现方法，可能涉及报表系统数据整理、新人注册操作手册以及作业配置对象设置步骤等方面。语音助手建议用户参考相关文档和操作指南来解决问题。',
      },
      {
        id: 3,
        name: '报表系统人员整理数据',
        content:
          '模拟摘要信息：用户咨询了 xxxx 功能的实现方法，可能涉及报表系统数据整理、新人注册操作手册以及作业配置对象设置步骤等方面。语音助手建议用户参考相关文档和操作指南来解决问题。',
      },
    ];
    ThinkingStatus.value = false;
    sendResult({
      text: discernText.value,
      result: discernResult.value,
    });
    discernVoice3();
  }, 4500);
};

// 识别语音-1
const discernVoice3 = () => {
  discernLoading.value = true;
  setTimeout(() => {
    discernText.value = '您好，请问你是想查询xxx吗？';
    discernLoading.value = false;
    queryKnowledgeBase3();
  }, 6000);
};

// 查询知识库
const queryKnowledgeBase3 = () => {
  ThinkingStatus.value = true;
  setTimeout(() => {
    discernResult.value = [
      {
        id: 1,
        name: '报表系统人员整理数据',
        content:
          '模拟摘要信息：用户咨询了 xxxx 功能的实现方法，可能涉及报表系统数据整理、新人注册操作手册以及作业配置对象设置步骤等方面。语音助手建议用户参考相关文档和操作指南来解决问题。',
      },
      {
        id: 2,
        name: '报表系统人员整理数据',
        content:
          '模拟摘要信息：用户咨询了 xxxx 功能的实现方法，可能涉及报表系统数据整理、新人注册操作手册以及作业配置对象设置步骤等方面。语音助手建议用户参考相关文档和操作指南来解决问题。',
      },
      {
        id: 3,
        name: '报表系统人员整理数据',
        content:
          '模拟摘要信息：用户咨询了 xxxx 功能的实现方法，可能涉及报表系统数据整理、新人注册操作手册以及作业配置对象设置步骤等方面。语音助手建议用户参考相关文档和操作指南来解决问题。',
      },
    ];
    ThinkingStatus.value = false;
    sendResult({
      text: discernText.value,
      result: discernResult.value,
    });
  }, 7000);
};

// 往中间区域发送结果
const sendResult = (data) => {
  emits('getDiscernResult', data);
};

onMounted(() => {
  discernVoice();
  // 监听电话事件
});
</script>

<template>
  <div class="max-h-[810px] overflow-y-auto p-5">
    <div
      class="bg-theme-color-opacity border-theme-color mb-3 flex rounded-md border p-3"
    >
      <div class="flex w-[30%] items-center justify-center">
        <Avatar
          class="h-[60px] w-[60px]"
          src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
        />
      </div>
      <div class="flex w-[70%] flex-col gap-2">
        <span class="text-[18px] font-bold">您好，我是您的语音助手</span>
        <span>我将实时为您提供通话知识库建议~</span>
      </div>
    </div>

    <!-- 语音识别中 -->
    <div class="flex items-center gap-2">
      <Avatar
        class="h-[40px] w-[40px]"
        src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
      />
      <div
        v-if="discernLoading"
        class="inline-flex gap-2 rounded-md border p-2"
      >
        <Spin />
        <span>语音识别中...</span>
      </div>
      <div v-else class="inline-flex gap-2 rounded-md border p-2">
        <Icon class="size-5 text-[green]" icon="ep:success-filled" />
        <div>
          语音结果：<i class="color-theme-color">{{ discernText }}</i>
        </div>
      </div>
    </div>

    <!-- 知识库查询 -->
    <div v-if="discernText" class="mt-3 flex items-center gap-2">
      <Avatar
        class="h-[40px] w-[40px]"
        src="https://mdn.alipayobjects.com/huamei_iwk9zp/afts/img/A*s5sNRo5LjfQAAAAAAAAAAAAADgCCAQ/fmt.webp"
      />
      <div
        v-if="ThinkingStatus"
        class="inline-flex gap-2 rounded-md border p-2"
      >
        <Spin />
        <span>知识库查询中...</span>
      </div>
      <div v-else class="inline-flex gap-2 rounded-md border p-2">
        <Icon class="size-5 text-[green]" icon="ep:success-filled" />
        <span>查询成功</span>
      </div>
    </div>

    <!-- 知识库查询内容 -->
    <div v-if="discernResult.length > 0" class="mt-3 flex gap-2">
      <div class="h-[40px] w-[40px] flex-shrink-0"></div>
      <div class="flex flex-col">
        <div
          v-for="(item, index) in discernResult"
          :key="item.id"
          class="flex flex-col gap-2"
        >
          <div class="mt-1 flex items-center gap-1">
            <a class="color-theme-color" href="#">
              {{ `${index + 1}-${item.name}` }}
            </a>
            <Icon class="color-theme-color size-4" icon="ep:right" />
          </div>
          <div class="card-box bg-theme-color-opacity p-2">
            {{ item.content }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.markdown-body) {
  background-color: transparent;
}
</style>
