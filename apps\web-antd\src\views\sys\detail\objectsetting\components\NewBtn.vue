<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { VbenIcon, VbenSpinner } from '@vben/common-ui';

import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  message,
  Radio,
  RadioGroup,
} from 'ant-design-vue';

import {
  addObjBtnApi,
  getBtnObjWorkApi,
  getObjBtnDetailApi,
  getObjSelectApi,
  updateObjBtnApi,
} from '#/api';
import {
  Checkbox,
  InputInteger,
  InputText,
  LookupItem,
  SelectItem,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const props = defineProps({
  btnData: {
    type: Object,
    default: () => {},
  },
});

const emits = defineEmits(['cancelEditBt', 'saveNewt']);

const router = useRouter();

const route = useRoute();

const { isLoading, startLoading, stopLoading } = useLoading();

// 显示表单
const showForm = ref(false);
// 是否是编辑页
const editIf = ref(false);

// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
  if (type === 'Checkbox') return Checkbox;
  if (type === 'integer') return InputInteger;
}
// 表单校验
const formRef = ref();

// ==============表单属性==========
const formList = ref([]);
const modelForm = computed(() => {
  // 处理modalForm值
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});

const formState = ref({
  button_name: null,
  button_ico: null,
  button_orderby: null,
  button_type: 'button_work',
  button_work: null,
  button_action: 'new_window',
  button_link: null,
  button_object: null, // 所属对象id
  Button_position: null, // 按钮位置
  Button_active: false, // 是否启用
  Button_notes: false, // 按钮备注
});
// 根据button_type动态生成校验规则
const dynamicRules = computed(() => {
  const rules = ref({});
  if (
    formState.value.button_type === 'button_work' ||
    formState.value.button_type === 'button_link'
  ) {
    rules.value.button_name = [
      { required: true, message: '请输入按钮名称!', trigger: 'blur' },
    ];
  } else {
    rules.value = {};
  }
  return rules.value;
});
// 同步更新formState对象值
watch(
  () => formList.value,
  (newData) => {
    newData.forEach((item) => {
      if (formState.value[item.field_api] !== undefined) {
        formState.value[item.field_api] = item.value;
      }
    });
  },
  { deep: true }, // 深度监听
);

// 按钮图标列表
const iconList = ref([]);
// 按钮位置列表
const positionList = ref([]);
// 按钮行为类型
const actionList = ref([]);
// 对象作业列表
const workList = ref([]);

async function createForm() {
  const arr = [
    {
      type: 'Radio',
      field_readonly: true,
      Field_Help_Text: '按钮类型',
      value: formState.value.button_type,
      title: '按钮类型',
      field_api: 'button_type',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入按钮名称',
      value: formState.value.button_name,
      title: '按钮名称',
      field_api: 'button_name',
      sort: 2,
      show: 'work',
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      value: formState.value.button_ico,
      title: '按钮图标',
      field_api: 'button_ico',
      opts: iconList.value,
      alone: true,
      sort: 12,
      show: 'work',
    },
    {
      type: 'integer',
      field_readonly: false,
      Field_Help_Text: '请输入序号',
      value: formState.value.button_orderby,
      title: '按钮排序',
      field_api: 'button_orderby',
      sort: 14,
      show: 'work',
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      value: formState.value.Button_position,
      title: '按钮位置',
      field_api: 'Button_position',
      opts: positionList.value,
      alone: true,
      sort: 16,
      show: 'work',
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formState.value.Button_active,
      title: '是否启用',
      field_api: 'Button_active',
      sort: 20,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formState.value.Button_notes,
      title: '是否注释',
      field_api: 'Button_notes',
      sort: 22,
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      Field_Help_Text: '请选择按钮作业',
      value: formState.value.button_work,
      title: '按钮作业',
      field_api: 'button_work',
      opts: workList.value,
      alone: true,
      sort: 25,
      show: 'nolink',
    },
    {
      type: 'dropdownlist',
      field_readonly: false,
      value: formState.value.button_action,
      title: '按钮行为',
      field_api: 'button_action',
      opts: actionList.value,
      alone: true,
      sort: 27,
      show: 'link',
    },
    {
      type: 'button_link',
      field_readonly: false,
      value: formState.value.button_link,
      title: '按钮连接',
      field_api: 'button_link',
      sort: 28,
      show: 'link',
    },
  ];
  if (formState.value.button_type === 'button_work') {
    const newArr = arr.filter((item) => item.show !== 'link');
    formList.value = [];
    formList.value.push(...newArr);
    formList.value.sort((a, b) => a.sort - b.sort);
  } else if (formState.value.button_type === 'button_link') {
    const newArr = arr.filter((item) => item.show !== 'nolink');
    formList.value = [];
    formList.value.push(...newArr);
    formList.value.sort((a, b) => a.sort - b.sort);
  } else {
    const newArr = arr.filter(
      (item) => item.show !== 'work' && item.show !== 'link',
    );
    formList.value = [];
    formList.value.push(...newArr);
    formList.value.sort((a, b) => a.sort - b.sort);
  }
  stopLoading();
}

// 按钮类型
const btnType = ref([]);

// 取消编辑
async function cancelEdit() {
  emits('cancelEditBt');
}

// 保存编辑
async function saveEdit() {
  startLoading();
  if (formState.value.button_type === 'button_work') {
    formState.value.button_link = null;
    formState.value.button_action = null;
  } else if (formState.value.button_type === 'button_link') {
    formState.value.button_work = null;
  } else {
    formState.value.Button_position = null;
    formState.value.button_action = null;
    formState.value.button_ico = null;
    formState.value.button_link = null;
    formState.value.button_name = null;
    formState.value.button_orderby = null;
  }
  formRef.value
    .validateFields() // 触发校验
    .then(async () => {
      stopLoading();
      await updateObjBtnApi(formState.value);
      message.success('保存成功');
      emits('saveNewt');
    })
    .catch(() => {
      stopLoading();
    });
}

// 获取按钮类型
const getBtnType = async () => {
  const { data } = await getObjSelectApi(
    'gl91lbk5po0curmribp9qm7htioq42as',
    'button_type',
  );
  btnType.value = data;
};
// 获取按钮图标
const getBtnIcon = async () => {
  const { data } = await getObjSelectApi(
    'gl91lbk5po0curmribp9qm7htioq42as',
    'button_ico',
  );
  const arr = data.map((item) => {
    return {
      ...item,
      label: item.name,
      value: item.id,
    };
  });
  iconList.value = arr;
};
// 获取按钮位置
const getBtnPosition = async () => {
  const { data } = await getObjSelectApi(
    'gl91lbk5po0curmribp9qm7htioq42as',
    'Button_position',
  );
  const arr = data.map((item) => {
    return {
      ...item,
      label: item.name,
      value: item.id,
    };
  });
  positionList.value = arr;
};
// 获取按钮行为
const getBtnAction = async () => {
  const { data } = await getObjSelectApi(
    'gl91lbk5po0curmribp9qm7htioq42as',
    'button_action',
  );
  const arr = data.map((item) => {
    return {
      ...item,
      label: item.name,
      value: item.id,
    };
  });
  actionList.value = arr;
};

// 获取对象作业列表
const getBtnWork = async () => {
  const { data } = await getBtnObjWorkApi(route.params.id);
  const arr = data.map((item) => {
    return {
      ...item,
      label: item.name,
      value: item.id,
    };
  });
  workList.value = arr;
};

// 切换按钮类型
const choseType = (val) => {
  formState.value.button_type = val;
  formRef.value.clearValidate();
  createForm();
};

// 跳转作业
const toWork = (work) => {
  if (work) {
    router.push({
      name: 'taskdetail',
      params: {
        id: work,
      },
    });
  } else {
    message.error('请选择按钮作业');
  }
};

// 添加按钮
const sureAdd = async () => {
  startLoading();
  formState.value.button_object = route.params.id;
  if (formState.value.button_type === 'button_work') {
    formState.value.button_link = null;
    formState.value.button_action = null;
  } else if (formState.value.button_type === 'button_link') {
    formState.value.button_work = null;
  } else {
    formState.value.Button_position = null;
    formState.value.button_action = null;
    formState.value.button_ico = null;
    formState.value.button_link = null;
    formState.value.button_name = null;
    formState.value.button_orderby = null;
  }
  formRef.value
    .validateFields() // 触发校验
    .then(async () => {
      stopLoading();
      await addObjBtnApi(formState.value);
      message.success('新建成功');
      emits('saveNewt');
    })
    .catch(() => {
      stopLoading();
    });
};

onMounted(async () => {
  startLoading();
  await getBtnType();
  await getBtnIcon();
  await getBtnPosition();
  await getBtnAction();
  await getBtnWork();
  if (props.btnData && Object.keys(props.btnData).length > 0) {
    const { data } = await getObjBtnDetailApi(props.btnData.id);
    formState.value = data;
    editIf.value = true;
    await createForm();
  } else {
    await createForm();
  }
  showForm.value = true;
});
</script>

<template>
  <div>
    <Card :title="editIf ? '编辑按钮' : '新建按钮'">
      <template #extra>
        <div class="flex gap-6">
          <Button v-if="editIf" type="primary" @click="saveEdit"> 保存 </Button>
          <Button v-if="!editIf" type="primary" @click="sureAdd"> 确定 </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
      </template>
      <template v-if="showForm">
        <div class="h-70vh overflow-y-auto">
          <Form
            ref="formRef"
            :label-col="{ span: 4 }"
            :model="modelForm"
            :rules="dynamicRules"
            :wrapper-col="{
              span: 14,
            }"
            name="nestMessages"
          >
            <template v-for="(item, index) in formList" :key="index">
              <FormItem
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <template v-if="item.type === 'Radio'">
                  <RadioGroup v-model:value="formState.button_type">
                    <Radio
                      v-for="items in btnType"
                      :key="items.id"
                      :value="items.id"
                      @change="choseType(items.id)"
                    >
                      {{ items.name }}
                    </Radio>
                  </RadioGroup>
                </template>
                <template v-if="item.field_api === 'button_link'">
                  <Input
                    v-model:value="formState.button_link"
                    class="col-span-6 !w-[70%]"
                  >
                    <template #addonBefore>
                      <div>http://</div>
                      <!-- <Select v-model:value="button_link" style="width: 90px">
                        <SelectOption value="http://">http://</SelectOption>
                      </Select> -->
                    </template>
                  </Input>
                </template>
                <div class="flex !w-[100%] items-center">
                  <component
                    :is="getComponent(item.type)"
                    :key="item.field_api"
                    v-model="item.value"
                    :parameters="item"
                    :select-alone="item.alone"
                    :select-opts="item.opts"
                    class="col-span-6 !w-[70%]"
                    place-holder="请选择"
                  />
                  <Button
                    v-if="item.field_api === 'button_work'"
                    class="ml-3"
                    @click="toWork(item.value)"
                  >
                    <VbenIcon class="size-4" icon="ep:right" />
                  </Button>
                </div>
              </FormItem>
            </template>
          </Form>
        </div>
      </template>
    </Card>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
