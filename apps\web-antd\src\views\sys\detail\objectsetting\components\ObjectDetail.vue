<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import { useVbenModal, VbenSpinner } from '@vben/common-ui';
import { Icon } from '@vben/icons';

import { Button, Card, Input, message } from 'ant-design-vue';

import { getObjectDetailApi, getRecalculateApi, issueObjapi } from '#/api';
import { useLoading } from '#/hook';

import FormulaModel from '../../../setup/components/modals/FormulaModel.vue';
import CopyObjectModel from './CopyObjectModel.vue';
import EditObject from './EditObject.vue';

const props = defineProps({
  powerIf: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['toPower', 'getObjectType']);

// 重新计算公式弹窗
const [ForMulaModal, forMulaModalApi] = useVbenModal({
  connectedComponent: FormulaModel,
});

const { isLoading, startLoading, stopLoading } = useLoading();

const route = useRoute();

// 批量作业执行状态弹窗
const [copyObjectModal, copyObjectModalApi] = useVbenModal({
  connectedComponent: CopyObjectModel,
});

const objectData = reactive({
  Object_id: {
    name: '对象API ID',
    value: '',
  },
  Object_name: {
    name: '对象名称',
    value: '',
  },
  Object_orderby: {
    name: '对象排序',
    value: '',
  },
  Object_type: {
    name: '对象类别',
    value: '',
  },
  Object_app_id: {
    name: '应用包',
    value: '',
  },
  Object_structure_type: {
    name: '对象结构类型',
    value: '',
  },
  Object_Original_Language: {
    name: '初始语言',
    value: '',
  },
  Object_ICO: {
    name: '图标',
    value: '',
  },
  Object_column_id_data_type: {
    name: '主键类型',
    value: '',
  },
  Object_column_id_Display_Format: {
    name: '主键显示格式',
    value: '',
  },
  Object_delivery_class: {
    name: '交付类型',
    value: '',
  },
  Object_Deployment_Status: {
    name: '部署状态',
    value: '',
  },
});

// 对象详情数据
const objDetail = ref({});

async function getObjectDetail() {
  const { data } = await getObjectDetailApi(route.params.id || '');
  objDetail.value = data;
  emits('getObjectType', data?.Object_type?.name);
  for (const key in objectData) {
    objectData[key].value =
      typeof data[key] === 'object' ? data[key]?.name : data[key];
  }
}

async function issueObject() {
  try {
    startLoading();
    const res = await issueObjapi(route.params.id);
    if (res === 200) {
      stopLoading();
      message.success('发布成功');
    } else {
      stopLoading();
      message.success('发布失败');
    }
  } catch {
    stopLoading();
  }
}
async function reComputedFormula() {
  forMulaModalApi.open();
  await getRecalculateApi(objDetail.value.Object_id.toLowerCase());
  message.success('计算成功');
}

const editObjectShow = ref(false);
async function editObject() {
  editObjectShow.value = true;
}
async function copyObject() {
  copyObjectModalApi.open();
}

async function cancelEdit() {
  editObjectShow.value = false;
  getObjectDetail();
}
// 是否是新建页进入
const fromState = ref('');
async function newSave(toData) {
  editObjectShow.value = false;
  fromState.value = '';
  getObjectDetail();
  emits('toPower', toData);
}

onMounted(async () => {
  // 如果从新建页跳转需要直接打开编辑页
  if (props?.powerIf === false && route?.params?.state) {
    fromState.value = route.params.state;
    if (fromState.value === 'addobj') {
      editObjectShow.value = true;
    }
  }
  await getObjectDetail();
  stopLoading();
});
</script>

<template>
  <div>
    <Card v-if="!editObjectShow" title="对象详细">
      <template #extra>
        <div class="flex gap-2">
          <Button type="primary" @click="issueObject"> 发布对象 </Button>
          <Button @click="reComputedFormula"> 重新计算公式 </Button>
          <Button @click="editObject"> 编辑 </Button>
          <Button @click="copyObject"> 复制 </Button>
        </div>
      </template>
      <div class="grid grid-cols-10 gap-2">
        <div
          v-for="item in objectData"
          :key="item.name"
          class="col-span-5 flex items-center gap-2"
        >
          <span class="w-[20%] text-right">{{ item.name }}</span>
          <Input v-model:value="item.value" class="w-[80%] flex-1" disabled />
          <Icon
            v-if="item.name === '图标' && item.value"
            :icon="item.value"
            class="size-6"
          />
        </div>
      </div>
    </Card>
    <!-- 对象编辑 -->
    <EditObject
      v-if="editObjectShow"
      :from-state="fromState"
      @cancel-edit="cancelEdit"
      @new-save="newSave"
    />
    <copyObjectModal :obj-detail="objDetail" />
    <ForMulaModal />
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
