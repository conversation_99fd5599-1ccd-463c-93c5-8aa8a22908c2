<!-- eslint-disable no-use-before-define -->
<script setup>
import { computed, ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Form, FormItem, message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { getFiledList<PERSON>pi, getObjectListApi, newGlobalapi } from '#/api';
import {
  Checkbox,
  InputInteger,
  InputNumber,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
} from '#/components/form-ui';

const emits = defineEmits(['clearFilter']);
// 表数据
const objForm = ref({
  global_selectoption_name: null,
  global_selectoption_id: null,
  Owning_application_package: null,
  global_selectoption_description: null,
  option_value: null,
  default: false,
  associated_field: null, // 关联字段
  associated_object: null, // 关联对象
});
const formRef = ref();
// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'integer') return InputInteger;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
  if (type === 'Checkbox') return Checkbox;
}

const formList = ref([]);
async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入名称',
      value: objForm.value.global_selectoption_name,
      title: '名称',
      field_api: 'global_selectoption_name',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入由字母为开头数字下划线组成的字符',
      value: objForm.value.global_selectoption_id,
      title: 'API',
      field_api: 'global_selectoption_id',
      sort: 2,
    },
    {
      type: 'Lookup_Relationship',
      field_readonly: false,
      Field_Help_Text: '请选择应用包',
      value: objForm.value.Owning_application_package,
      title: '所属应用包',
      field_api: 'Owning_application_package',
      sort: 3,
      key: 'Object_app_id',
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: objForm.value.global_selectoption_description,
      title: '对象描述',
      Field_Help_Text: '请输入...',
      field_api: 'global_selectoption_description',
      sort: 4,
    },
    {
      type: 'custom',
      value: objForm.value.associated_object,
      title: '选项值关联对象',
      field_api: 'associated_object',
      sort: 5,
    },
    {
      type: 'custom',
      value: objForm.value.associated_field,
      title: '选项值关联字段',
      field_api: 'associated_field',
      sort: 6,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: objForm.value.option_value,
      title: '值',
      Field_Help_Text: '请输入...',
      field_api: 'option_value',
      sort: 7,
    },
    // {
    //   type: 'Checkbox',
    //   field_readonly: false,
    //   value: objForm.value.default,
    //   title: '默认值',
    //   field_api: 'default',
    //   Field_Help_Text: '将第一个值作为默认值',
    //   sort: 20,
    // },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
}

const rules = {
  global_selectoption_name: [
    { required: true, message: '请输入名称!', trigger: 'blur' },
  ],
  global_selectoption_id: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (!value) {
          throw new Error('请输入API!');
        }
        const regex = /^[a-z]\w*$/;
        if (!regex.test(value)) {
          throw new Error('格式错误!');
        }
      },
      trigger: 'blur',
    },
  ],
  Owning_application_package: [
    { required: true, message: '请选择应用包!', trigger: 'blur' },
  ],
  option_value: [{ required: true, message: '请输入选项值!', trigger: 'blur' }],
};
const modelForm = computed(() => {
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
const isFirstLoad = ref(true);
watch(
  () => formList.value,
  (newData) => {
    if (isFirstLoad.value === true) {
      isFirstLoad.value = false; // 更新标志变量以表示已初始化
      return; // 初次加载时不执行任何操作
    }
    newData.forEach((item) => {
      objForm.value[item.field_api] = item.value;
    });
  },
  { deep: true }, // 深度监听
);

const [mainModal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[680px]',
  contentClass: 'p-4',
  appendToMain: true,
  confirmText: '新建',
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    formRef.value
      .validateFields() // 触发校验
      .then(async () => {
        objForm.value.select_options = objForm.value.option_value
          .split('\n')
          .map((item, i) => {
            return item === ''
              ? null
              : {
                  selectoption_id: item,
                  selectoption_Values: item,
                  selectoption_orderby: i,
                  selectoption_Active: 1, // 是否启用
                  selectoption_default: 0, // 是否默认选中
                };
          })
          .filter((item) => item !== null);
        if (objForm.value.default && objForm.value.select_options.length > 0) {
          objForm.value.select_options[0].selectoption_default = 1;
        }
        const toData = cloneDeep(objForm.value);
        delete toData.option_value;
        delete toData.default;
        await newGlobalapi(toData);
        message.success('新建成功');
        emits('clearFilter');
        modalApi.close();
      })
      .catch(() => {});
  },
  onOpenChange: (isOpen) => {
    if (isOpen) {
      objForm.value = {
        global_selectoption_name: null,
        global_selectoption_id: null,
        Owning_application_package: null,
        global_selectoption_description: null,
        option_value: null,
        default: false,
      };
      formList.value = [];
      isFirstLoad.value = true;
      createForm();
      getObjectList();
    }
  },
});

const objListOption = ref([]);
const felidListOption = ref([]);

// 获取对象列表可选项目
const getObjectList = async () => {
  try {
    const { data } = await getObjectListApi();
    data.items.shift();
    objListOption.value = data.items.map((input) => ({
      label: input.name,
      options: input.objects.map((item) => ({
        value: item.id,
        label: item.name,
      })),
    }));
  } catch {
    objListOption.value = [];
  }
};

function filterOption(inputValue, option) {
  return option.label.includes(inputValue);
}

// 选择对象后获取字段
const getFelidsMessage = async (value) => {
  const dataParams = {
    page: 1,
    page_size: 999,
    object_id: value,
    order_by: 'Field_orderby',
    sort_by: 'ASC',
    keywords: null,
  };
  try {
    felidListOption.value = [];
    if (!value) return;
    const { data } = await getFiledListApi(dataParams);
    felidListOption.value = data.data.map((item) => {
      return {
        label: item.Field_name,
        value: item.id,
      };
    });
  } catch {
    felidListOption.value = [];
  }
};
</script>

<template>
  <div>
    <mainModal title="新增全局选项列表集">
      <div class="flex flex-col gap-3 p-2">
        <Form
          ref="formRef"
          :label-col="{ span: 5 }"
          :model="modelForm"
          :rules="rules"
          :wrapper-col="{ span: 15 }"
          autocomplete="off"
          name="basic"
        >
          <div class="grid">
            <template v-for="(item, index) in formList" :key="index">
              <FormItem
                v-if="item.type !== 'custom'"
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <component
                  :is="getComponent(item.type)"
                  v-model="item.value"
                  :object-id="item.objectId ? item.objectId : 'Object'"
                  :object-name="item.objectName ? item.objectName : 'object'"
                  :parameters="item"
                  class="col-span-6 w-[100%]"
                />
                <p
                  v-if="item.field_api === 'option_value'"
                  class="mt-2 text-gray-400"
                >
                  文本域一行为一个选项值
                </p>
              </FormItem>
              <FormItem
                v-if="item.field_api === 'associated_object'"
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <Select
                  v-model:value="item.value"
                  :filter-option="filterOption"
                  :options="objListOption"
                  allow-clear
                  placeholder="搜索对象"
                  show-search
                  @change="getFelidsMessage"
                />
              </FormItem>
              <FormItem
                v-if="item.field_api === 'associated_field'"
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <Select
                  v-model:value="item.value"
                  :options="felidListOption"
                  allow-clear
                  placeholder="选择关联字段"
                  show-search
                />
              </FormItem>
            </template>
          </div>
        </Form>
      </div>
    </mainModal>
  </div>
</template>

<style lang="scss" scoped></style>
