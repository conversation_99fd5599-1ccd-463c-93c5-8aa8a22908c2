<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { VbenSpinner } from '@vben/common-ui';

import { Button, Card, Form, FormItem, message } from 'ant-design-vue';

import { getGlobalDetailApi, updateGlobalMainapi } from '#/api';
import {
  Checkbox,
  InputInteger,
  InputNumber,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const { isLoading, startLoading, stopLoading } = useLoading();

const router = useRoute();

// 表数据
const objForm = ref({
  id: router.params.id,
  global_selectoption_name: null,
  global_selectoption_id: null,
  Owning_application_package: null,
  global_selectoption_description: null,
});
const formRef = ref();
// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'integer') return InputInteger;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
  if (type === 'Checkbox') return Checkbox;
}
// 显示数据
const showData = ref(false);
// 是否点击编辑按钮
const editIf = ref(false);
const formList = ref([]);
async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: !editIf.value,
      Field_Help_Text: '请输入名称',
      value: objForm.value.global_selectoption_name,
      title: '名称',
      field_api: 'global_selectoption_name',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: !editIf.value,
      Field_Help_Text: '请输入由字母为开头数字下划线组成的字符',
      value: objForm.value.global_selectoption_id,
      title: 'API',
      field_api: 'global_selectoption_id',
      sort: 2,
    },
    {
      type: 'Lookup_Relationship',
      field_readonly: !editIf.value,
      Field_Help_Text: '请选择应用包',
      value: objForm.value.Owning_application_package,
      title: '应用包',
      field_api: 'Owning_application_package',
      sort: 3,
      key: 'Object_app_id',
    },
    {
      type: 'TextArea',
      field_readonly: !editIf.value,
      value: objForm.value.global_selectoption_description,
      title: '描述',
      Field_Help_Text: '请输入...',
      field_api: 'global_selectoption_description',
      sort: 4,
    },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
  showData.value = true;
  stopLoading();
}

const rules = {
  global_selectoption_name: [
    { required: true, message: '请输入名称!', trigger: 'blur' },
  ],
  global_selectoption_id: [
    {
      required: true,
      validator: async (_rule, value) => {
        if (!value) {
          throw new Error('请输入API!');
        }
        const regex = /^[a-z]\w*$/;
        if (!regex.test(value)) {
          throw new Error('格式错误!');
        }
      },
      trigger: 'blur',
    },
  ],
  Owning_application_package: [
    { required: true, message: '请选择应用包!', trigger: 'blur' },
  ],
};
const modelForm = computed(() => {
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
const isFirstLoad = ref(true);
watch(
  () => formList.value,
  (newData) => {
    if (isFirstLoad.value === true) {
      isFirstLoad.value = false; // 更新标志变量以表示已初始化
      return; // 初次加载时不执行任何操作
    }
    newData.forEach((item) => {
      objForm.value[item.field_api] = item.value;
    });
  },
  { deep: true }, // 深度监听
);

// 数据赋值
const dataAssign = async () => {
  startLoading();
  showData.value = false;
  const { data } = await getGlobalDetailApi(router.params.id);
  objForm.value = {
    id: data.id,
    global_selectoption_name: data.global_selectoption_name,
    Owning_application_package: data.Owning_application_package,
    global_selectoption_id: data.global_selectoption_id,
    global_selectoption_description: data.global_selectoption_description,
  };
  formList.value = [];
  isFirstLoad.value = true;
  editIf.value = false;
  await createForm();
};

onMounted(async () => {
  showData.value = false;
  dataAssign();
});

// 编辑信息
const editInfo = async () => {
  formList.value = [];
  editIf.value = true;
  await createForm();
};
// 取消编辑
const cancelEdit = async () => {
  dataAssign();
  formRef.value.clearValidate();
  editIf.value = false;
};
// 保存数据
const saveInfo = async () => {
  formRef.value
    .validateFields() // 触发校验
    .then(async () => {
      editIf.value = false;
      await updateGlobalMainapi(objForm.value);
      message.success('保存成功');
      dataAssign();
    })
    .catch(() => {});
};
</script>
<template>
  <div>
    <Card title="全局值集详细信息">
      <template #extra>
        <div class="flex gap-6">
          <Button v-if="!editIf" type="primary" @click="editInfo">
            编辑
          </Button>
          <Button v-if="editIf" type="primary" @click="saveInfo"> 保存 </Button>
          <Button v-if="editIf" @click="cancelEdit"> 取消 </Button>
        </div>
      </template>
      <div>
        <Form
          ref="formRef"
          :label-col="{ span: 5 }"
          :model="modelForm"
          :rules="rules"
          :wrapper-col="{ span: 15 }"
          autocomplete="off"
          name="basic"
        >
          <div class="grid">
            <template v-for="(item, index) in formList" :key="index">
              <FormItem :label="item.title" :name="item.field_api" class="m-2">
                <component
                  :is="getComponent(item.type)"
                  v-if="showData"
                  :key="item.field_api"
                  v-model="item.value"
                  :parameters="item"
                  class="col-span-6 w-[100%]"
                  object-id="Object"
                  object-name="object"
                />
              </FormItem>
            </template>
          </div>
        </Form>
      </div>
      <VbenSpinner :spinning="isLoading" />
    </Card>
  </div>
</template>
