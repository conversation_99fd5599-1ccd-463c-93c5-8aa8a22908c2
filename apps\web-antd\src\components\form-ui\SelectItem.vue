<script setup>
import { onMounted, ref, watch } from 'vue';

import { Icon } from '@vben/icons';

import { Select, SelectOption } from 'ant-design-vue';

import { getFilterProSelectApi } from '#/api';

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
  placeHolder: {
    type: String,
    default: '',
  },
  selectOpts: {
    type: Array,
    default: () => [],
  },
  selectAlone: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['update:modelValue']);

const selectValue = ref(props.modelValue);
const selectOption = ref([]);

// 提示信息
const placeHolderval = ref(null);
// 是否支持删除
const allowClear = ref(props.parameters?.clear ?? false);

// 获取下拉选项列表
async function getSelectOptions() {
  if (props.placeHolder) {
    placeHolderval.value = props.placeHolder;
  }
  if (Array.isArray(props.selectOpts) && props.selectAlone) {
    selectOption.value = props.selectOpts;
  } else {
    const object_id = props.objectId || '';
    const field = props.parameters.key;
    const { data } = await getFilterProSelectApi(object_id, field);

    selectOption.value = data.map((item) => {
      return {
        label: item.name,
        value: item.id,
      };
    });
    selectOption.value = selectOption.value.filter(
      (item, index, self) =>
        index ===
        self.findIndex((t) => t.value === item.value && t.label === item.label),
    );
  }

  const find = selectOption.value.find(
    (item) =>
      item.label === props.modelValue || item.value === props.modelValue,
  );

  selectValue.value = find ? find.value : null;
  emits('update:modelValue', selectValue.value);
}

watch(selectValue, (newValue) => {
  selectValue.value = newValue;
  emits('update:modelValue', selectValue.value);
});

watch(
  () => props.selectOpts,
  () => {
    if (Array.isArray(props.selectOpts) && props.selectAlone) {
      selectOption.value = props.selectOpts;
    }
  },
);

watch(
  () => props.modelValue,
  () => {
    selectValue.value = props.modelValue;
  },
);

onMounted(() => {
  getSelectOptions();
});
</script>

<template>
  <Select
    v-if="props.parameters.field_api === 'Object_ICO'"
    v-model:value="selectValue"
    :allow-clear="allowClear"
    :disabled="props.parameters.field_readonly"
    :placeholder="placeHolderval"
    show-search
  >
    <SelectOption
      v-for="item in selectOption"
      :key="item.value"
      :value="item.value"
    >
      <div class="flex items-center">
        <Icon :icon="item.value" class="size-5" />
        {{ item.label }}
      </div>
    </SelectOption>
  </Select>
  <Select
    v-else
    v-model:value="selectValue"
    :allow-clear="allowClear"
    :disabled="props.parameters.field_readonly"
    :options="selectOption"
    :placeholder="placeHolderval"
    show-search
  />
</template>

<style></style>
