import { ref } from 'vue';

import { defineStore } from 'pinia';

interface filterSetList {
  /**
   * 对象名称
   */
  name: string;
  /**
   * 筛选值缓存列表
   */
  value: Array<any>;
}

export const useTableStore = defineStore('table', () => {
  const filterProject = ref<filterSetList[]>([]);
  const isDelete = ref<string>('False');
  const viewShow = ref<boolean>(false); // 视图保存功能是否显示
  const viewColumns = ref<any[]>([]);
  const viewFilter = ref<any>(null);

  function addFilterProject(value: filterSetList) {
    if (
      filterProject.value.filter((item) => item.name === value.name).length ===
      0
    ) {
      filterProject.value.push(value);
    }
  }

  function getFilterProject(name: string) {
    return filterProject.value.find((item) => item.name === name)?.value;
  }

  function clearFilterProject(objectName: string) {
    filterProject.value.forEach((item) => {
      if (item.name === objectName) {
        item.value.forEach((el) => {
          el.value = null;
          el.selectConditionValue =
            el.type === 'Checkbox' ? null : el.selectConditionOption[0].value;
          el.selectValue = null;
        });
      }
    });
  }

  function clearFilterOne(field_identifier: string, objectName: string) {
    const findFilterMessage = filterProject.value.find(
      (filterItem) => filterItem.name === objectName,
    );
    findFilterMessage?.value.forEach((el) => {
      if (el.key === field_identifier) {
        el.value = null;
        el.selectConditionValue =
          el.type === 'Checkbox' ? null : el.selectConditionOption[0].value;
        el.selectValue = null;
      }
    });
    filterProject.value.forEach((item) => {
      if (item.name === field_identifier) {
        item.value = findFilterMessage?.value || [];
      }
    });
  }
  function $reset() {
    filterProject.value = [];
    isDelete.value = 'False';
  }

  return {
    filterProject,
    isDelete,
    viewShow,
    viewColumns,
    viewFilter,
    addFilterProject,
    getFilterProject,
    clearFilterProject,
    clearFilterOne,
    $reset,
  };
});
