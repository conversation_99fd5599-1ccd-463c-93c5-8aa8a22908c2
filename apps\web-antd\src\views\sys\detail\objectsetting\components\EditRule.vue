<script setup>
import { computed, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import { VbenSpinner } from '@vben/common-ui';

import {
  Button,
  Card,
  Checkbox,
  Form,
  FormItem,
  message,
  Select,
} from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { addRuleApi, checkPythonApi, ruleDetailApi } from '#/api';
import {
  Checkbox as diyCheck,
  InputInteger,
  InputText,
  InputTextArea,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const props = defineProps({
  choseData: {
    type: Object,
    default: () => {},
  },
});

const emits = defineEmits(['cancelEditRule', 'changeRule']);

const { isLoading, startLoading, stopLoading } = useLoading();
const route = useRoute();

const editIf = ref(false);

const formData = ref({
  object_id: route.params.id,
  name: '',
  status: true,
  description: '',
  error_condition: '',
  error_message: '',
  error_location: [],
});

function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Checkbox') return diyCheck;
  if (type === 'integer') return InputInteger;
}

// 表单校验
const formList = ref([]);

async function createForm() {
  const arr = [
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入规则名称',
      value: formData.value.name,
      title: '规则名称',
      field_api: 'name',
      sort: 1,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.status,
      title: '启用',
      field_api: 'status',
      sort: 2,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: formData.value.description,
      title: '描述',
      Field_Help_Text: '请输入...',
      field_api: 'description',
      sort: 3,
    },
    {
      type: 'Formula',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.error_condition,
      title: '错误条件公式',
      field_api: 'error_condition',
      sort: 5,
    },
    {
      type: 'Error',
      field_readonly: false,
      Field_Help_Text: '请输入...',
      value: formData.value.error_message,
      title: '自定义错误消息',
      field_api: 'error_message',
      sort: 6,
    },
    {
      type: 'Position',
      field_readonly: false,
      Field_Help_Text: '',
      value: formData.value.error_location,
      title: '错误位置',
      field_api: 'error_location',
      sort: 8,
    },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
}

// 选择运算符值
const operatorVal = ref();
const operatorHolder = ref('选择运算符');
const operatorOpts = ref([
  {
    label: '等于',
    value: '=',
  },
  {
    label: '不等于',
    value: '!=',
  },
  {
    label: '大于',
    value: '>',
  },
  {
    label: '大于等于',
    value: '>=',
  },
  {
    label: '小于',
    value: '<',
  },
  {
    label: '小于等于',
    value: '<=',
  },
]);

// 选择插入函数值
const functionVal = ref();
const functionHolder = ref('插入函数');
const functionOpts = ref([
  {
    label: 'IF',
    value: 'if :\n  \nelse:\n',
  },
  {
    label: 'ACOS',
    value: 'ACOS',
  },
  {
    label: 'ADDMONTHS',
    value: 'ADDMONTHS',
  },
  {
    label: 'AND',
    value: 'AND',
  },
  {
    label: 'ASCLL',
    value: 'ASCLL',
  },
  {
    label: 'ASIN',
    value: 'ASIN',
  },
]);

const changeOperator = (value) => {
  const formulaItem = formList.value.find(
    (item) => item.title === '错误条件公式',
  );

  if (formulaItem) {
    formulaItem.value = formulaItem.value ?? '';
    formulaItem.value += value || '';
    formData.value.error_condition = formulaItem.value;
  }
};
const changeFunction = (value) => {
  const formulaItem = formList.value.find(
    (item) => item.title === '错误条件公式',
  );

  if (formulaItem) {
    formulaItem.value = formulaItem.value ?? '';
    formulaItem.value += value || '';
    formData.value.error_condition = formulaItem.value;
  }
};

// 表单校验
const formRef = ref();
const rules = {
  name: [{ required: true, message: '请输入规则名称!', trigger: 'blur' }],
};

const modelForm = computed(() => {
  // 处理modalForm值
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
// 同步更新fromData对象值
watch(
  () => formList.value,
  (newData) => {
    newData.forEach((item) => {
      if (formData.value[item.field_api] !== undefined) {
        formData.value[item.field_api] = item.value;
      }
    });
  },
  { deep: true }, // 深度监听
);

// 取消编辑
async function cancelEdit() {
  emits('cancelEditRule');
}

// 检查语法
const grammar_active = ref(false);
const checkPython = async () => {
  const { data } = await checkPythonApi({
    python_str: formData.value.error_condition,
  });
  if (data === true) {
    message.success('语法正确！');
    grammar_active.value = false;
  } else {
    message.error('语法错误！');
    grammar_active.value = true;
  }
};

// 保存事件
async function onSave(btn) {
  formRef.value
    .validateFields() // 触发校验
    .then(async () => {
      const a = cloneDeep(formData.value);
      a.error_location[0]
        ? (a.error_location[0] = 1)
        : (a.error_location[0] = 0);
      a.error_location[1]
        ? (a.error_location[1] = 1)
        : (a.error_location[1] = 0);
      a.error_location = JSON.stringify(a.error_location);
      if (formData.value.error_condition) {
        await checkPython();
      }
      if (grammar_active.value) return;
      await addRuleApi(a);
      if (btn === 'save') {
        message.success('保存成功');
      } else {
        message.success('新建成功');
      }
      emits('changeRule');
    })
    .catch(() => {});
}

onMounted(async () => {
  startLoading();
  if (props.choseData && Object.keys(props.choseData).length > 0) {
    const { data } = await ruleDetailApi(props.choseData.id);
    formData.value = data;
    formData.value.error_location = JSON.parse(formData.value.error_location);
    formData.value.error_location[0]
      ? (formData.value.error_location[0] = true)
      : (formData.value.error_location[0] = false);
    formData.value.error_location[1]
      ? (formData.value.error_location[1] = true)
      : (formData.value.error_location[1] = false);
    editIf.value = true;
  }
  await createForm();
  stopLoading();
});
</script>
<template>
  <div>
    <Card :title="editIf ? '编辑验证规则' : '新建验证规则'">
      <template #extra>
        <div class="flex gap-6">
          <Button v-if="editIf" type="primary" @click="onSave('save')">
            保存
          </Button>
          <Button v-if="!editIf" type="primary" @click="onSave('sure')">
            确定
          </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
      </template>
      <div>
        通过指定条件和相应错误消息，定义验证规则。错误条件编写成布尔公式(expression)，返回"真"或"假"。如果公式(expression)返回"真"，中止保存并显示错误消息，用户可以更正错误，然后重试。
      </div>
      <div class="mt-4 h-[66vh] overflow-y-auto">
        <Form
          ref="formRef"
          :label-col="{ span: 4 }"
          :model="modelForm"
          :rules="rules"
          :wrapper-col="{
            span: 14,
          }"
          name="nestMessages"
        >
          <template v-for="(item, index) in formList" :key="index">
            <FormItem :label="item.title" :name="item.field_api" class="m-[2%]">
              <div
                v-if="item.type !== 'Formula' && item.type !== 'Error'"
                class="col-span-6 !w-[70%]"
              >
                <component
                  :is="getComponent(item.type)"
                  v-model="item.value"
                  :clear="true"
                  :parameters="item"
                  class="!w-[100%]"
                />
              </div>
              <template v-if="item.type === 'Formula'">
                <div class="col-span-6">
                  <div class="mt-1.5">示例：[@变量]>100</div>
                  <div class="flex">
                    <div class="flex-1">
                      如果"折扣"大于30%，则显示错误
                      <br />如果公式结果为
                      <span class="font-bold">真</span>
                      ，则显示错误消息区中定义的文本
                    </div>
                  </div>
                  <div class="my-6 flex !w-[70%] gap-6">
                    <Button type="primary">插入字段</Button>
                    <FormItem :name="`${operatorVal}`" no-style>
                      <Select
                        v-model:value="operatorVal"
                        :options="operatorOpts"
                        :placeholder="operatorHolder"
                        @select="changeOperator"
                      />
                    </FormItem>
                    <FormItem :name="`${functionVal}`" no-style>
                      <Select
                        v-model:value="functionVal"
                        :options="functionOpts"
                        :placeholder="functionHolder"
                        @select="changeFunction"
                      />
                    </FormItem>
                  </div>
                  <div class="my-6 !w-[70%]">
                    <component
                      :is="getComponent('TextArea')"
                      v-model="item.value"
                      :clear="true"
                      :parameters="item"
                      :rows="5"
                      class="col-span-6"
                    />
                  </div>
                  <div class="mt-6 flex !w-[70%] justify-end">
                    <Button type="primary" @click="checkPython">
                      检查语法
                    </Button>
                  </div>
                </div>
              </template>
              <template v-if="item.type === 'Error'">
                <div class="col-span-6">
                  <div class="mt-1.5">示例：折扣百分比不能超过30%</div>
                  <div class="flex">
                    <div class="flex-1">
                      此消息将在"错误条件"公式为
                      <span class="font-bold">真</span>
                      时显示
                    </div>
                  </div>
                  <div class="my-6 !w-[70%]">
                    <component
                      :is="getComponent('TextArea')"
                      v-model="item.value"
                      :clear="true"
                      :parameters="item"
                      :rows="5"
                      class="col-span-6"
                    />
                  </div>
                  <div class="mt-1">
                    该错误消息可出现在首页或页中某一特定字段下面
                  </div>
                </div>
              </template>
              <template v-if="item.type === 'Position'">
                <div class="col-span-6">
                  <div class="ml-2 flex !w-[70%] gap-4">
                    <FormItem name="homepage">
                      <Checkbox v-model:checked="item.value[0]">
                        首页
                      </Checkbox>
                    </FormItem>
                    <FormItem name="field">
                      <Checkbox v-model:checked="item.value[1]">
                        字段
                      </Checkbox>
                    </FormItem>
                  </div>
                </div>
              </template>
            </FormItem>
          </template>
        </Form>
      </div>
    </Card>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
