<script setup>
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenModal, VbenSpinner } from '@vben/common-ui';

import { <PERSON><PERSON>, Card } from 'ant-design-vue';

import { useLoading } from '#/hook';

import EditModel from './EditModel.vue';

const props = defineProps({
  columnLists: {
    type: Array,
    default: () => [],
  },
  objectName: {
    type: String,
    default: null,
  },
  recordId: {
    type: String,
    default: null,
  },
  tabName: {
    type: String,
    default: null,
  },
  // 是否是新建数据
  newIf: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['finishEdit', 'finishAdd']);
// 编辑弹窗
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: EditModel,
});
const objectNames = ref(props?.objectName);
const recordIds = ref(props?.recordId);

const { isLoading, startLoading, stopLoading } = useLoading();

const router = useRouter();
const route = useRoute();

// 处理字段值
function handleFieldValue(data) {
  if (['dropdownlist', 'Lookup_Relationship'].includes(data.type)) {
    return data.value?.name;
  }
  if (data.type === 'Checkbox') {
    return data.value ? '是' : '否';
  }
  return data.value;
}

// 前往查找相关字段的页面
function openDetail(item) {
  // 其他表格详情跳转
  router.push({
    name: `/dashboard/${item.field_related_to_table}/detail`,
    params: {
      id: item.value?.id,
    },
  });
}

// 新建完成
const afterAdd = () => {
  emits('finishAdd');
};
// 编辑完成
const afterEdit = () => {
  emits('finishEdit');
};

// 获取所有字段列表
async function getTableColumns() {}
// tab名称
const tabName = ref(props?.tabName);
// 是否是新建数据
const newIf = ref(props.newIf);

onMounted(() => {
  newIf.value = props.newIf;
  tabName.value = props?.tabName;
  startLoading();
  stopLoading();
});
</script>

<template>
  <div>
    <Card :title="tabName">
      <template #extra>
        <div class="flex gap-2">
          <Button
            v-if="tabName !== '系统信息' && !newIf"
            type="primary"
            @click="() => editModalApi.open()"
          >
            编辑
          </Button>
          <Button
            v-if="newIf"
            type="primary"
            @click="() => editModalApi.open()"
          >
            配置所有信息
          </Button>
        </div>
      </template>
      <div
        class="grid max-h-[700px] w-full flex-1 grid-cols-4 gap-1 overflow-y-scroll text-sm"
      >
        <template v-for="item in props.columnLists" :key="item.key">
          <div
            v-if="item.type === 'group' && newIf"
            class="card-box bg-theme-color col-span-4 mb-3 mt-3 p-1 font-bold text-[#fff] opacity-80"
          >
            <span class="ml-2 opacity-100">{{ item.name }}</span>
          </div>
          <div v-if="item.type !== 'group'" class="col-span-2 mb-2 flex">
            <span class="w-[30%] text-right">{{ item.name }}：</span>
            <span
              v-if="item.type !== 'Lookup_Relationship'"
              class="flex-1 overflow-scroll"
            >
              {{ handleFieldValue(item) }}
            </span>
            <span
              v-if="item.type === 'Lookup_Relationship'"
              class="color-theme-color flex-1 cursor-pointer font-bold"
              @click="openDetail(item)"
            >
              {{ handleFieldValue(item) }}
            </span>
          </div>
        </template>
      </div>
    </Card>
    <EditModal
      :column-list="props.columnLists"
      :edit-recard-id="recordIds"
      :new-if="newIf"
      :object-id="route.meta.objectId"
      :object-name="objectNames"
      @after-add="afterAdd"
      @after-edit="afterEdit"
      @refresh="getTableColumns"
    />
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>

<style></style>
