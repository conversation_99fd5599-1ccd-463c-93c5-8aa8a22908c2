<script setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

import { useVbenModal, VbenSpinner } from '@vben/common-ui';

import {
  Button,
  Card,
  Checkbox,
  Input,
  message,
  Popconfirm,
  Table,
} from 'ant-design-vue';
import { debounce } from 'lodash-es';
import { ColorPicker } from 'vue3-colorpicker';

import { delGlobalapi, getGlobalDetailApi, updateGlobalapi } from '#/api';
import { useLoading } from '#/hook';

import AddGlobal from './AddGlobal.vue';

import 'vue3-colorpicker/style.css';

const { isLoading, startLoading, stopLoading } = useLoading();

const router = useRoute();

const [AddGlobalModal, AddGlobalModelApi] = useVbenModal({
  connectedComponent: AddGlobal,
});

const valColumns = ref([
  {
    title: '值',
    dataIndex: 'selectoption_Values',
    key: 'selectoption_Values',
    align: 'center',
  },
  {
    title: 'API名称',
    dataIndex: 'selectoption_id',
    key: 'selectoption_id',
    align: 'center',
  },
  // {
  //   title: '默认',
  //   dataIndex: 'selectoption_default',
  //   key: 'selectoption_default',
  //   align: 'center',
  // },
  {
    title: '背景颜色',
    dataIndex: 'selectoption_color',
    key: 'selectoption_color',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
    width: '350px',
  },
]);
// 值
const valSource = ref([]);
// 禁用值
const disableSource = ref([]);
const valLoading = ref(false);
const disableLoading = ref(false);

// 添加按钮
const addGlobal = () => {
  AddGlobalModelApi.open();
};

// 获取值表数据
const getTable = async () => {
  startLoading();
  const { data } = await getGlobalDetailApi(router.params.id);
  data.select_options.forEach((item) => {
    if (item.selectoption_color === null || item.selectoption_color === '') {
      item.selectoption_color = 'transparent';
    }
    item.readOnly = true;
  });
  valSource.value = data.select_options
    .filter((item) => item.selectoption_Active === true)
    .sort((a, b) => a.selectoption_orderby - b.selectoption_orderby);

  disableSource.value = data.select_options
    .filter((item) => item.selectoption_Active === false)
    .sort((a, b) => a.selectoption_orderby - b.selectoption_orderby);
  stopLoading();
};

// 是否点击编辑
const editIf = ref(false);
// 编辑数据
const editVal = ref({});
const editData = (item) => {
  valSource.value.forEach((option) => {
    if (!option.readOnly) {
      option.selectoption_color = editVal.value.selectoption_color;
      option.selectoption_Values = editVal.value.selectoption_Values;
      option.selectoption_id = editVal.value.selectoption_id;
      option.selectoption_default = editVal.value.selectoption_default;
    }
  });
  editVal.value = { ...item };
  valSource.value.forEach((option) => {
    option.readOnly = true;
  });
  item.readOnly = false;
  editIf.value = true;
};
// 取消编辑
const cancelBtn = (data) => {
  data.selectoption_color = editVal.value.selectoption_color;
  data.selectoption_Values = editVal.value.selectoption_Values;
  data.selectoption_id = editVal.value.selectoption_id;
  data.selectoption_default = editVal.value.selectoption_default;
  valSource.value.forEach((option) => {
    option.readOnly = true;
  });
};

// 确定编辑
const sureEdit = async (item) => {
  startLoading();
  const { readOnly: _, ...data } = item;
  await updateGlobalapi(data);

  const ysDefault = valSource.value.find(
    (val) => val.selectoption_default === true && val.id !== item.id,
  );
  if (ysDefault) {
    ysDefault.selectoption_default = false;
    const { readOnly: _, ...data2 } = ysDefault;
    await updateGlobalapi(data2);
  }
  stopLoading();
  valSource.value.forEach((option) => {
    option.readOnly = true;
  });
  message.success('修改成功');
  await getTable();
};

// 删除选项
const deleteVal = async (id) => {
  await delGlobalapi(id);
  message.success('删除成功');
  await getTable();
};

// 清空颜色
const clearColor = debounce((record) => {
  record.selectoption_color = 'transparent';
  sureEdit(record);
}, 300);

// 实时修改颜色
const changeColor = debounce((record) => {
  if (record.readOnly !== false) {
    sureEdit(record);
  }
}, 1500);

// 禁用
const onForbidden = async (id) => {
  await updateGlobalapi({ id, selectoption_Active: false });
  message.success('禁用成功');
  await getTable();
};

// 启用
const onStart = async (id) => {
  await updateGlobalapi({ id, selectoption_Active: true });
  message.success('启用成功');
  await getTable();
};

onMounted(async () => {
  editIf.value = false;
  getTable();
});
</script>
<template>
  <div class="mt-4">
    <Card title="值">
      <template #extra>
        <div class="flex gap-6">
          <Button type="primary" @click="addGlobal"> 添加 </Button>
        </div>
      </template>
      <div>
        <Table
          :columns="valColumns"
          :data-source="valSource"
          :loading="valLoading"
          :pagination="false"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template
              v-if="
                column.dataIndex === 'selectoption_Values' ||
                column.dataIndex === 'selectoption_id'
              "
            >
              <Input
                v-if="!record.readOnly"
                v-model:value="record[column.dataIndex]"
                style="width: 100%"
              />
              <text v-else>{{ record[column.dataIndex] }}</text>
            </template>
            <!-- <template v-if="column.dataIndex === 'selectoption_default'">
              <Checkbox
                v-model:checked="record.selectoption_default"
                :disabled="record.readOnly"
              />
            </template> -->
            <template v-if="column.dataIndex === 'selectoption_color'">
              <ColorPicker
                v-model:pure-color="record.selectoption_color"
                format="hex6"
                picker-type="chrome"
                style="width: 100px"
                @pure-color-change="changeColor(record)"
              />
            </template>

            <template v-if="column.dataIndex === 'operation'">
              <div v-if="record.readOnly" class="flex justify-around">
                <Button type="primary" @click="editData(record)">编辑</Button>
                <Popconfirm
                  cancel-text="取消"
                  ok-text="确定"
                  title="您确定要删除该选项值吗？"
                  @confirm="deleteVal(record.id)"
                >
                  <Button type="primary"> 删除 </Button>
                </Popconfirm>
                <Button danger type="primary" @click="onForbidden(record.id)">
                  禁用
                </Button>
                <Button type="primary" @click="clearColor(record)">
                  清空颜色
                </Button>
              </div>
              <div v-else class="flex justify-around">
                <Button type="primary" @click="cancelBtn(record)">取消</Button>
                <Button type="primary" @click="sureEdit(record)">确定</Button>
              </div>
            </template>
          </template>
        </Table>
      </div>
    </Card>
    <Card class="mt-4" title="禁用值列表">
      <div>
        <Table
          :columns="valColumns"
          :data-source="disableSource"
          :loading="disableLoading"
          :pagination="false"
          bordered
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'selectoption_default'">
              <Checkbox
                v-model:checked="record.selectoption_default"
                :disabled="record.readOnly"
              />
            </template>
            <template v-if="column.dataIndex === 'selectoption_color'">
              <ColorPicker
                v-model:pure-color="record.selectoption_color"
                format="hex6"
                picker-type="chrome"
                style="width: 100px"
              />
            </template>

            <template v-if="column.dataIndex === 'operation'">
              <div class="flex justify-around">
                <Button type="primary" @click="onStart(record.id)">启用</Button>
              </div>
            </template>
          </template>
        </Table>
      </div>
    </Card>
    <AddGlobalModal @new-opt="getTable" />
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
