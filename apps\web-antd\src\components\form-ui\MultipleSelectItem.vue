<script setup>
import { onMounted, ref, watch } from 'vue';

import { Select } from 'ant-design-vue';

import { getFilterProSelectApi } from '#/api';

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['update:modelValue']);

const selectValue = ref([]);
const selectOption = ref([]);

// 提示信息
const placeHolderval = ref(props.placeHolder ?? null);

// 获取下拉选项列表
async function getSelectOptions() {
  const object_id = props.objectId || '';
  const field = props.parameters.key;
  const { data } = await getFilterProSelectApi(object_id, field);

  selectOption.value = data.map((item) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
  // 去重
  selectOption.value = selectOption.value.filter(
    (item, index, self) =>
      index ===
      self.findIndex((t) => t.value === item.value && t.label === item.label),
  );

  // 处理初始化的多选值
  selectValue.value = props.modelValue.split(',');
}

watch(selectValue, (newValue) => {
  selectValue.value = newValue;
  emits('update:modelValue', selectValue.value.join(','));
});

onMounted(() => {
  getSelectOptions();
});
</script>

<template>
  <Select
    v-model:value="selectValue"
    :disabled="props.parameters.field_readonly"
    :options="selectOption"
    :placeholder="placeHolderval"
    :show-arrow="true"
    allow-clear
    mode="multiple"
  />
</template>

<style></style>
