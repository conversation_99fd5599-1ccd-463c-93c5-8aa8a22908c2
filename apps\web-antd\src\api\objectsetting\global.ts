import { requestClient } from '#/api/request';
/**
 *
 * @returns 列表-新建全局选项列表集
 */
export async function newGlobalapi(data: any) {
  return requestClient.post(`/global_select_hand/add_global_select`, data);
}

/**
 *
 * @returns 全局选项-获取详情信息
 */
export async function getGlobalDetailApi(id: string) {
  return requestClient.get(`/global_select_hand/get_global_select/${id}`);
}

/**
 *
 * @returns 全局选项-修改主信息
 */
export async function updateGlobalMainapi(data: any) {
  return requestClient.post(`/global_select_hand/update`, data);
}

/**
 *
 * @returns 全局选项-修改单个值信息
 */
export async function updateGlobalapi(data: any) {
  return requestClient.post(`/select_hand/update`, data);
}

/**
 *
 * @returns 全局选项-删除一条选项
 */
export async function delGlobalapi(id: string) {
  return requestClient.delete(`/select_hand/${id}`);
}

/**
 *
 * @returns 全局选项-新增一条选项
 */
export async function addGlobalapi(data: any) {
  return requestClient.post(`/select_hand/add`, data);
}
