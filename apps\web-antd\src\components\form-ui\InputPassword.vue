<script setup>
import { ref } from 'vue';

import { InputPassword } from 'ant-design-vue';

const props = defineProps({
  value: {
    type: String,
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const inputValue = ref(props.modelValue);

const changeValue = () => {
  emits('update:modelValue', inputValue.value);
};
</script>

<template>
  <InputPassword
    v-model:value="inputValue"
    :disabled="props.parameters.field_readonly"
    autocomplete="new-password"
    placeholder="若不更改密码，则无需填写"
    type="text"
    @change="changeValue"
  />
</template>

<style></style>
