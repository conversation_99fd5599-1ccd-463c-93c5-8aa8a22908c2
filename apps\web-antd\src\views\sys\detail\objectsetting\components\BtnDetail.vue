<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import {
  Button,
  Card,
  message,
  Pagination,
  Popconfirm,
  Table,
} from 'ant-design-vue';

import { btnObjDelApi, btnObjListApi } from '#/api';

import NewBtn from './NewBtn.vue';

const route = useRoute();

const btnLoading = ref(false);
const dataSource = ref([]);
const btnTotal = ref(0);
const dataColumns = [
  {
    title: '按钮名称',
    dataIndex: 'button_name',
    key: 'button_name',
    align: 'center',
  },
  {
    title: '按钮图标',
    dataIndex: 'button_ico',
    key: 'button_ico',
    align: 'center',
  },
  {
    title: '按钮排序',
    dataIndex: 'button_orderby',
    key: 'button_orderby',
    align: 'center',
  },
  {
    title: '按钮类型',
    dataIndex: 'button_type',
    key: 'button_type',
    align: 'center',
  },
  {
    title: '按钮位置',
    dataIndex: 'Button_position',
    key: 'Button_position',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    align: 'center',
  },
];

const dataParams = reactive({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
  order_by: 'button_orderby',
  sort_by: 'ASC',
});
// 获取按钮列表
async function getBtnList() {
  btnLoading.value = true;
  const { data } = await btnObjListApi(dataParams);
  dataSource.value = data.data;
  btnTotal.value = data.total;
  btnLoading.value = false;
}

const showBtnNew = ref(false);
const btnData = ref({});
// 编辑按钮
async function editBtn(data) {
  showBtnNew.value = true;
  btnData.value = data;
}

// 新建按钮
const newBtn = () => {
  showBtnNew.value = true;
  btnData.value = {};
};

// 删除按钮
async function deletedBtn(id) {
  await btnObjDelApi(id);
  message.success('删除成功');
  getBtnList();
}

// 改变页数
async function pageChange(page, pageSize) {
  dataParams.page = page;
  dataParams.page_size = pageSize;
  getBtnList(); // 重新调用
}

// 新建或保存按钮完成
async function saveNewt() {
  showBtnNew.value = false;
  getBtnList();
}

onMounted(() => {
  getBtnList();
});
</script>

<template>
  <div>
    <Card v-if="!showBtnNew" title="按钮、链接和操作">
      <template #extra>
        <div class="flex gap-2">
          <Button type="primary" @click="newBtn"> 新建 </Button>
        </div>
      </template>
      <Table
        :columns="dataColumns"
        :data-source="dataSource"
        :loading="btnLoading"
        :pagination="false"
        :scroll="{ y: 500 }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <template
            v-if="
              column.key === 'button_ico' ||
              column.key === 'button_type' ||
              column.key === 'Button_position'
            "
          >
            <text>{{ record[column.key].name }}</text>
          </template>
          <template v-if="column.key === 'operation'">
            <div class="flex justify-center gap-6">
              <Button type="primary" @click="editBtn(record)">编辑</Button>
              <Popconfirm
                cancel-text="取消"
                ok-text="确定"
                title="您确定要删除该条按钮吗？"
                @confirm="deletedBtn(record.id)"
              >
                <Button danger type="primary"> 删除 </Button>
              </Popconfirm>
            </div>
          </template>
        </template>
        <template #footer>
          <div class="text-right">
            <Pagination
              v-model:current="dataParams.page"
              v-model:page-size="dataParams.page_size"
              :show-size-changer="true"
              :show-total="(btnTotal) => `共 ${btnTotal} 条`"
              :total="btnTotal"
              @change="pageChange"
            />
          </div>
        </template>
      </Table>
    </Card>
    <!-- 新建按钮 -->
    <NewBtn
      v-if="showBtnNew"
      :btn-data="btnData"
      @cancel-edit-bt="showBtnNew = false"
      @save-newt="saveNewt"
    />
  </div>
</template>

<style></style>
