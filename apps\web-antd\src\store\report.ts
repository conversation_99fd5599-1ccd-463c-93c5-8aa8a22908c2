import { defineStore } from 'pinia';

interface CounterState {
  showValue: boolean;
  showPercent: boolean;
  combine: boolean;
  showTotal: boolean;
  title: string;
  showGroup: boolean;
  field: string;
}

export const useReportStore = defineStore('report', {
  state: (): CounterState => ({
    showValue: false,
    showPercent: false,
    combine: false,
    showTotal: false,
    title: '',
    showGroup: false,
    field: '',
  }),
});
