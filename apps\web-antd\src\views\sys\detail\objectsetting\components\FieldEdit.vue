<script setup>
import { onMounted, ref } from 'vue';

import AudioType from '../fieldcomponents/AudioType.vue';
import AutoNumber from '../fieldcomponents/AutoNumber.vue';
import Checkbox from '../fieldcomponents/Checkbox.vue';
import Currency from '../fieldcomponents/Currency.vue';
import Date from '../fieldcomponents/Date.vue';
import Dropdownlist from '../fieldcomponents/Dropdownlist.vue';
import Formula from '../fieldcomponents/Formula.vue';
import Group from '../fieldcomponents/Group.vue';
import Integer from '../fieldcomponents/Integer.vue';
import LookupRelationship from '../fieldcomponents/LookupRelationship.vue';
import Number from '../fieldcomponents/Number.vue';
import Password from '../fieldcomponents/Password.vue';
import Percent from '../fieldcomponents/Percent.vue';
import TextArea from '../fieldcomponents/TextArea.vue';
import TextType from '../fieldcomponents/TextType.vue';
import UploadType from '../fieldcomponents/UploadType.vue';
// import TextAreaHTML from '../fieldcomponents/TextAreaHTML.vue';
// import TextAreaRich from '../fieldcomponents/TextAreaRich.vue';

const props = defineProps({
  fieldEditval: {
    type: Object,
    default: () => ({}),
  },
});

const emits = defineEmits(['cancelField']);
// 字段类型
const fieldType = ref('');
// 字段id
const fieldId = ref('');
onMounted(async () => {
  fieldType.value = props.fieldEditval.Field_type.id || '';
  fieldId.value = props.fieldEditval.id;
});

const cancelEdit = () => {
  emits('cancelField');
};
</script>

<template>
  <div>
    <TextType
      v-if="fieldType === 'Text'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <TextArea
      v-if="fieldType === 'Text_Area'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <!-- 富文本 -->
    <!-- <TextAreaRich
      v-if="fieldType === 'Text_Area_Rich'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    /> -->
    <!-- <TextAreaHTML
      v-if="fieldType === 'Text_Area_HTML'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    /> -->
    <Password
      v-if="fieldType === 'Password'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <Formula
      v-if="fieldType === 'Formula'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <AutoNumber
      v-if="fieldType === 'Auto_Number'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <Group
      v-if="fieldType === 'group'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <Integer
      v-if="fieldType === 'integer'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <Number
      v-if="fieldType === 'Number'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <!-- <Currency
      v-if="fieldType === 'Currency' || fieldType === 'Date_Time'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    /> -->
    <Currency
      v-if="fieldType === 'Date_Time'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <Percent
      v-if="fieldType === 'Percent'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <Date
      v-if="fieldType === 'Date' || fieldType === 'Time'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <Checkbox
      v-if="fieldType === 'Checkbox'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <UploadType
      v-if="
        fieldType === 'upload_file' ||
        fieldType === 'upload_pic' ||
        fieldType === 'upload_pics' ||
        fieldType === 'upload_video' ||
        fieldType === 'upload_files' ||
        fieldType === 'color_picker' ||
        fieldType === 'Country'
      "
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <AudioType
      v-if="fieldType === 'upload_audio'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <LookupRelationship
      v-if="fieldType === 'Lookup_Relationship'"
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
    <Dropdownlist
      v-if="
        fieldType === 'dropdownlist' ||
        fieldType === 'dropdownlist_Multi_Select' ||
        fieldType === 'Picklist' ||
        fieldType === 'Picklist_Multi_Select'
      "
      :field-id="fieldId"
      @cancel-edit="cancelEdit"
    />
  </div>
</template>
