<script setup>
import { ref, watch } from 'vue';

import { Radio, RadioGroup } from 'ant-design-vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const checkValue = ref(null);

checkValue.value = props.modelValue || null;

watch(checkValue, (newValue) => {
  emits('update:modelValue', newValue);
});
</script>

<template>
  <RadioGroup v-model:value="checkValue" name="radioGroup">
    <Radio value="1">A</Radio>
    <Radio value="2">B</Radio>
    <Radio value="3">C</Radio>
    <Radio value="4">D</Radio>
  </RadioGroup>
</template>

<style></style>
