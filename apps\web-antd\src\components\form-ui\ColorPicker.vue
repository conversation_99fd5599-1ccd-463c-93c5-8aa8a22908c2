<script setup>
import { ref, watch } from 'vue';

import { ColorPicker } from 'vue3-colorpicker';

import 'vue3-colorpicker/style.css';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const colorValue = ref('transparent');
props.modelValue && (colorValue.value = props.modelValue);

watch(colorValue, (newValue) => {
  emits('update:modelValue', newValue);
});
</script>

<template>
  <div>
    <ColorPicker
      v-model:pure-color="colorValue"
      format="hex6"
      picker-type="chrome"
      style="width: 100px"
    />
  </div>
</template>

<style></style>
