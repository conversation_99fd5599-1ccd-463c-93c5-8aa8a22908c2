<script setup>
import { computed, onMounted, reactive, ref, watch } from 'vue';

import { VbenIcon, VbenSpinner } from '@vben/common-ui';

import {
  Button,
  Card,
  Form,
  FormItem,
  Popconfirm,
  Select,
  Table,
} from 'ant-design-vue';

import {
  Checkbox,
  InputInteger,
  InputText,
  InputTextArea,
} from '#/components/form-ui';
import { useLoading } from '#/hook';

const emits = defineEmits(['cancelEditRule']);
const { isLoading, startLoading, stopLoading } = useLoading();
// 表单数据对象
const formData = ref({
  object_id: 1,
  name: '',
  full_name: '',
  description: '',
  status: true,
  user_conditions: {
    conditions: '1',
    userIf: [
      {
        id: 1,
        field: '$User.division_c',
        operatorL: 'EQUALS',
        type: '1',
        price: '1',
      },
    ],
  },
  record_criteria: {
    criteria: '1',
    userIf2: [
      {
        id: 2,
        field: '$Order_approve_c',
        operatorL: 'EQUALS',
        type: '1',
        price: '1',
      },
    ],
  },
});
// 条件类型下拉选项
const selectOpt = ref([{ label: '用户条件', value: '1' }]);

// 是否是新建页面
const newIf = ref(false);
// 显示表单
const showForm = ref(false);
onMounted(async () => {
  startLoading();
  await createForm();
  stopLoading();
});

// ==================类型组件
function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'TextArea') return InputTextArea;
  if (type === 'Checkbox') return Checkbox;
  if (type === 'integer') return InputInteger;
}
// 表单校验
const formRef = ref();
const rules = {
  name: [{ required: true, message: '请输入规则名称!', trigger: 'blur' }],
};

// ==============表单属性==========
const formList = ref([]);
const modelForm = computed(() => {
  // 处理modalForm值
  const fromValues = {};
  formList.value
    .filter((item) => item.type !== 'group')
    .forEach((item) => {
      fromValues[item.field_api] = item.value;
    });
  return fromValues;
});
// 同步更新fromData对象值
watch(
  () => formList.value,
  (newData) => {
    newData.forEach((item) => {
      if (formData.value[item.field_api] !== undefined) {
        formData.value[item.field_api] = item.value;
      }
    });
  },
  { deep: true }, // 深度监听
);

async function createForm() {
  const arr = [
    {
      type: 'title',
      value: '规则详细信息',
      Field_Help_Text: '',
      sort: 1,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入规则名称',
      value: formData.value.name,
      title: '规则名称',
      field_api: 'name',
      sort: 5,
    },
    {
      type: 'Text',
      field_readonly: false,
      Field_Help_Text: '请输入全名',
      value: formData.value.full_name,
      title: '全名',
      field_api: 'full_name',
      sort: 10,
    },
    {
      type: 'TextArea',
      field_readonly: false,
      value: formData.value.description,
      title: '描述',
      Field_Help_Text: '请输入...',
      field_api: 'description',
      sort: 15,
    },
    {
      type: 'Checkbox',
      field_readonly: false,
      value: formData.value.status,
      title: '活动',
      field_api: 'status',
      sort: 20,
    },
    {
      type: 'title',
      value: '用户条件',
      sort: 21,
      Field_Help_Text: '选择此限制规则应用于哪些用户',
    },
    {
      type: 'Logic',
      field_readonly: false,
      value: formData.value.user_conditions,
      title: '关联逻辑',
      field_api: 'user_conditions',
      sort: 27,
    },
    {
      type: 'title',
      value: 'Record Criteria',
      sort: 30,
      Field_Help_Text: '选择允许指定用户查看哪些记录',
    },
    {
      type: 'Logics',
      field_readonly: false,
      value: formData.value.user_conditions,
      title: '关联逻辑',
      field_api: 'user_conditions',
      sort: 32,
    },
  ];
  formList.value.push(...arr);
  formList.value.sort((a, b) => a.sort - b.sort);
  showForm.value = true;
}

// 关联逻辑表格
const columns = [
  {
    title: '字段',
    dataIndex: 'field',
  },
  {
    title: '运算符',
    dataIndex: 'operator',
  },
  {
    title: '类型',
    dataIndex: 'type',
  },
  {
    title: '值',
    dataIndex: 'value',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];
// 运算符下拉选项
const operatorOpt = [
  { label: '等于', value: 'EQUALS' },
  { label: '不等于', value: 'NOT_EQUALS' },
  { label: '大于', value: 'MORE_THAN' },
  { label: '小于', value: 'LESS_THAN' },
  { label: '介于', value: 'BETWEEN' },
  { label: '大于等于', value: 'MORE_THAN_AND_EQUAL' },
  { label: '小于等于', value: 'LESS_THAN_AND_EQUAL' },
];
const FieldData = reactive({
  type: 'dropdownlist',
  field_readonly: false,
  field_api: '',
  placeHolderval: '请选择字段',
});

// 添加用户条件
const addConditions = () => {
  const newData = {
    id: 2,
    field: '$User.division_c',
    operatorL: 'EQUALS',
    type: '1',
    price: '1',
  };
  formData.value.user_conditions.userIf.push(newData);
};
const onDelete = (i) => {
  formData.value.user_conditions.userIf.splice(i, 1);
};

// 添加Record Criteria
const addCriteria = () => {
  const newData = {
    id: 1,
    field: '$User.division_c',
    operatorL: 'EQUALS',
    type: '1',
    price: '1',
  };

  formData.value.record_criteria.userIf2.push(newData);
};

const onDelete2 = (i) => {
  formData.value.record_criteria.userIf2.splice(i, 1);
};

// async function saveEdit() {
//   formRef.value
//     .validateFields() // 触发校验
//     .then(() => {
//       serveData();
//     })
//     .catch(() => {});
// }

const cancelEdit = () => {
  emits('cancelEditRule');
};
</script>
<template>
  <div>
    <Card title="数据筛选 显示规则">
      <template #extra>
        <div class="flex gap-2">
          <Button type="primary"> 新建 </Button>
          <Button @click="cancelEdit"> 取消 </Button>
        </div>
      </template>
      <template v-if="showForm">
        <div
          :style="{ height: newIf ? '58vh' : '70vh' }"
          class="h-[70vh] overflow-y-auto"
        >
          <Form
            ref="formRef"
            :label-col="{ span: 4 }"
            :model="modelForm"
            :rules="rules"
            :wrapper-col="{
              span: 14,
            }"
            name="nestMessages"
          >
            <template v-for="(item, index) in formList" :key="index">
              <div v-if="item.type === 'title'">
                <div class="font-bold">{{ item.value }}</div>
                <div>{{ item.Field_Help_Text }}</div>
              </div>
              <FormItem
                v-if="
                  item.type !== 'title' &&
                  item.type !== 'Logic' &&
                  item.type !== 'Logics'
                "
                :label="item.title"
                :name="item.field_api"
                class="m-[2%]"
              >
                <component
                  :is="getComponent(item.type)"
                  v-model="item.value"
                  :parameters="item"
                  class="col-span-6 w-[70%]"
                />
              </FormItem>
              <!-- Logic 类型的表单项 -->
              <template v-if="item.type === 'Logic'">
                <FormItem
                  class="ml-[2%] mt-[2%]"
                  label="条件类型："
                  name="logicSection"
                >
                  <div>
                    <div class="mb-4 flex w-[70%] items-center justify-between">
                      <Select
                        v-model:value="formData.user_conditions.conditions"
                        :options="selectOpt"
                      />
                    </div>
                    <Button type="primary" @click="addConditions">
                      添加
                    </Button>
                    <Table
                      :columns="columns"
                      :data-source="formData.user_conditions.userIf"
                      :pagination="false"
                      bordered
                      class="mt-4"
                    >
                      <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'field'">
                          <FormItem :name="`${record.key}-field`" no-style>
                            <Select
                              v-model:value="record.field"
                              :options="selectOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'operator'">
                          <FormItem :name="`${record.key}-operatorL`" no-style>
                            <Select
                              v-model:value="record.operatorL"
                              :options="operatorOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'type'">
                          <FormItem :name="`${record.key}-type`" no-style>
                            <Select
                              v-model:value="record.type"
                              :options="selectOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'value'">
                          <FormItem :name="`${record.key}-price`" no-style>
                            <Select
                              v-model:value="record.price"
                              :options="selectOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'operation'">
                          <Popconfirm
                            v-if="formData.user_conditions.userIf.length > 0"
                            title="确定删除吗?"
                            @confirm="onDelete(index)"
                          >
                            <Button type="primary">删除</Button>
                          </Popconfirm>
                        </template>
                      </template>
                    </Table>
                    <div class="mt-4 flex items-center">
                      <VbenIcon
                        class="size-4"
                        icon="fluent-color:warning-32"
                      />设计限制规则，以便只有一个活动规则应用于指定用户
                    </div>
                  </div>
                </FormItem>
              </template>
              <template v-if="item.type === 'Logics'">
                <FormItem
                  class="ml-[2%] mt-[2%]"
                  label="条件类型："
                  name="logicSection"
                >
                  <div>
                    <div class="mb-4 flex w-[70%] items-center justify-between">
                      <Select
                        v-model:value="formData.record_criteria.criteria"
                        :options="selectOpt"
                      />
                    </div>
                    <Button type="primary" @click="addCriteria"> 添加 </Button>
                    <Table
                      :columns="columns"
                      :data-source="formData.record_criteria.userIf2"
                      :pagination="false"
                      bordered
                      class="mt-4"
                    >
                      <template #bodyCell="{ column, record, index }">
                        <template v-if="column.dataIndex === 'field'">
                          <FormItem :name="`${record.key}-field`" no-style>
                            <Select
                              v-model:value="record.field"
                              :options="selectOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'operator'">
                          <FormItem :name="`${record.key}-operatorL`" no-style>
                            <Select
                              v-model:value="record.operatorL"
                              :options="operatorOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'type'">
                          <FormItem :name="`${record.key}-type`" no-style>
                            <Select
                              v-model:value="record.type"
                              :options="selectOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'value'">
                          <FormItem :name="`${record.key}-price`" no-style>
                            <Select
                              v-model:value="record.price"
                              :options="selectOpt"
                              :placeholder="FieldData.placeHolderval"
                            />
                          </FormItem>
                        </template>
                        <template v-if="column.dataIndex === 'operation'">
                          <Popconfirm
                            v-if="formData.user_conditions.userIf.length > 0"
                            title="确定删除吗?"
                            @confirm="onDelete2(index)"
                          >
                            <Button type="primary">删除</Button>
                          </Popconfirm>
                        </template>
                      </template>
                    </Table>
                  </div>
                </FormItem>
              </template>
            </template>
          </Form>
        </div>
      </template>
    </Card>
    <VbenSpinner :spinning="isLoading" />
  </div>
</template>
<style lang="scss" scoped></style>
