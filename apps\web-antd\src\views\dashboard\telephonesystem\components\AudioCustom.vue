<script setup>
import { onMounted, ref, toRefs } from 'vue';

import { VbenIconButton } from '@vben/common-ui';
import { Icon } from '@vben/icons';

import { Button, Dropdown, Menu, MenuItem, Slider } from 'ant-design-vue';

// 音频播放倍速

const props = defineProps({
  recordUrl: {
    type: String,
    default: '',
  },
});

const { recordUrl } = toRefs(props);
const SliderValue = ref(0);
const isPlaying = ref(false); // 标记音频是否正在播放
const audioRef = ref(null); // 音频元素引用
const duration = ref(0); // 音频时长
const isMuted = ref(false); // 标记音频是否静音
const playbackRate = ref(1);

// 播放/暂停音频
const togglePlay = () => {
  if (audioRef.value) {
    if (isPlaying.value) {
      audioRef.value.pause();
    } else {
      audioRef.value.play();
    }
    isPlaying.value = !isPlaying.value;
  }
};

// 后退 5 秒
const rewind = () => {
  if (audioRef.value) {
    audioRef.value.currentTime = Math.max(0, audioRef.value.currentTime - 5);
  }
};

// 快进 5 秒
const fastForward = () => {
  if (audioRef.value) {
    audioRef.value.currentTime = Math.min(
      audioRef.value.duration,
      audioRef.value.currentTime + 5,
    );
  }
};

// 更新进度条
const updateSlider = () => {
  if (audioRef.value) {
    SliderValue.value =
      (audioRef.value.currentTime / audioRef.value.duration) * 100;
  }
};

// 跳转播放进度
const seek = (value) => {
  if (audioRef.value) {
    audioRef.value.currentTime = (value / 100) * audioRef.value.duration;
  }
};

// 切换静音状态
const toggleMute = () => {
  if (audioRef.value) {
    isMuted.value = !isMuted.value;
    audioRef.value.muted = isMuted.value;
  }
};

// 设置播放倍速
const setPlaybackRate = (rate) => {
  if (audioRef.value) {
    playbackRate.value = rate;
    audioRef.value.playbackRate = rate;
  }
};

onMounted(() => {
  if (audioRef.value) {
    audioRef.value.addEventListener('timeupdate', updateSlider);
    audioRef.value.addEventListener('loadedmetadata', () => {
      duration.value = Math.floor(audioRef.value.duration);
    });

    // 监听音频播放结束事件
    audioRef.value.addEventListener('ended', () => {
      isPlaying.value = false;
    });
  }
});

// 将秒转换为 00:00 格式
const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const secs = Math.floor(seconds % 60);
  return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
};
</script>

<template>
  <div class="flex h-full flex-col justify-center">
    <!-- 进度条 -->
    <div class="flex w-full items-center justify-between gap-3">
      <span>{{ formatTime(audioRef?.currentTime || 0) }}</span>
      <Slider
        v-model:value="SliderValue"
        :tip-formatter="null"
        class="flex-1"
        @change="seek"
      />
      <span>{{ formatTime(duration) }}</span>
    </div>
    <div class="flex items-center justify-center gap-5">
      <VbenIconButton @click="toggleMute">
        <Icon
          :icon="isMuted ? 'quill:mute' : 'lets-icons:sound-max'"
          class="size-7"
        />
      </VbenIconButton>
      <VbenIconButton tooltip="后退5s" @click="rewind">
        <Icon
          class="size-7"
          icon="material-symbols-light:fast-rewind-rounded"
        />
      </VbenIconButton>
      <Button
        class="card-box bg-r bg-theme-color flex h-[50px] w-[50px] cursor-pointer justify-center rounded-full p-3"
        @click="togglePlay"
      >
        <Icon
          :icon="
            isPlaying ? 'material-symbols-light:pause' : 'line-md:play-filled'
          "
          class="size-7 text-white"
        />
      </Button>
      <VbenIconButton tooltip="快进5s" @click="fastForward">
        <Icon
          class="size-7"
          icon="material-symbols-light:fast-forward-rounded"
        />
      </VbenIconButton>
      <Dropdown :trigger="['hover']">
        <template #overlay>
          <Menu>
            <MenuItem key="0.5" @click="setPlaybackRate(0.5)">0.5x</MenuItem>
            <MenuItem key="1" @click="setPlaybackRate(1)">1x</MenuItem>
            <MenuItem key="1.5" @click="setPlaybackRate(1.5)">1.5x</MenuItem>
            <MenuItem key="2" @click="setPlaybackRate(2)">2x</MenuItem>
          </Menu>
        </template>

        <span class="cursor-pointer">倍速 {{ playbackRate }}x</span>
      </Dropdown>
    </div>
    <audio ref="audioRef" :src="recordUrl"></audio>
  </div>
</template>

<style></style>
