<!-- eslint-disable unicorn/prefer-add-event-listener -->
<script setup>
import { onMounted, onUnmounted, ref } from 'vue';

import { loginTCCCApi } from '#/api';

import AiService from './components/AiService.vue';
import CallRecord from './components/CallRecord.vue';
import TelephoneList from './components/TelephoneList.vue';

// 初始化腾讯通话
function injectTcccWebSDK(SdkURL) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script');
    script.setAttribute('crossorigin', 'anonymous');
    // 需要渲染进的DomId，如果填写则没有悬浮窗，工作台直接渲染进指定的 dom 元素
    // 为保证工作台 UI 完整，渲染的 Dom 最小高度为480px，最小宽度为760px
    // script.dataset.renderDomId = "renderDom";
    script.src = SdkURL;

    /*
     * 增加enableShared，表示启用多Tab功能
     */
    script.dataset.enableShared = 'true';
    document.body.append(script);
    script.addEventListener('load', () => {
      // 加载JS SDK文件成功，此时可使用全局变量"tccc"
      window.tccc.on(window.tccc.events.ready, () => {
        /**
         * Tccc SDK 初始化成功，此时可调用外呼、监听呼入事件等功能。
         * 注意⚠️：请确保只初始化一次 SDK
         */
        window.tccc.UI.hidefloatButton();
        resolve('初始化成功');
      });
      window.tccc.on(window.tccc.events.tokenExpired, ({ message }) => {
        console.error('初始化失败', message);
        reject(message);
      });

      window.tccc.on(window.tccc.events.userAccessed, async () => {
        console.warn('坐席接听电话');
        // getDiscernToken();
      });
      window.tccc.on(window.tccc.events.calloutAccepted, async () => {
        console.warn('用户接听电话');
        // getDiscernToken();
      });
    });
  });
}

// const wsUrl = ref('wss://asr.cloud.tencent.com/asr/v2/');
// const ws = ref(null);

// 获取秘钥
// async function getDiscernToken() {
//   const { data } = await getDiscernTokenApi();
//   console.warn('获取临时秘钥', data);
//   wsUrl.value = `${wsUrl.value}${data.appid}?${data.query}`;
//   ws.value = new WebSocket(wsUrl.value);

//   // 监听 WebSocket 连接成功事件
//   ws.value.onopen = () => {
//     console.warn('WebSocket 连接成功');
//   };

//   ws.value.onmessage = (event) => {
//     // 可根据实际需求处理收到的消息
//     console.warn('收到消息:', event.data);
//     if (event.code === 0) {
//       // console.warn('连接成功');
//     }
//   };

//   // 监听连接关闭事件
//   ws.value.onclose = () => {
//     console.warn('WebSocket 连接关闭');
//   };
// }

// 登录话务系统
async function loginTelephoneSystem() {
  const { data } = await loginTCCCApi();
  if (window.tccc) {
    console.warn('已经初始化SDK了');
    window.tccc.Agent.online();
    window.tccc.UI.show();
    return;
  }
  injectTcccWebSDK(data.sdkUrl);
}

const currentTelephoneRecord = ref(null);
// 获取当前点击的通话记录详情
const getCurrentCallRecord = (item) => {
  currentTelephoneRecord.value = item;
};

// 通话记录结束后返回数据
const endData = ref({});
const endCall = (data) => {
  endData.value = data;
};

// 获取识别语音知识库列表
const discernResult = ref([]);
const getDiscernResult = (data) => {
  discernResult.value.push(data);
};

onMounted(() => {
  loginTelephoneSystem();
});

onUnmounted(() => {
  window.tccc.Agent.offline();
  window.tccc.UI.hide();
  stopRecording();
  if (ws.value) {
    ws.value.close();
  }
});
</script>

<template>
  <div class="h-full p-2">
    <div class="card-box grid h-full grid-cols-10 overflow-hidden">
      <div class="col-span-2 border-r">
        <TelephoneList
          :end-data="endData"
          @get-current-call-record="getCurrentCallRecord"
        />
      </div>
      <div class="col-span-5 border-r">
        <CallRecord
          :current-telephone-record="currentTelephoneRecord"
          :discern-result="discernResult"
          @end-call="endCall"
        />
      </div>
      <div class="col-span-3">
        <AiService @get-discern-result="getDiscernResult" />
      </div>
    </div>
  </div>
</template>

<style></style>
