<script setup>
import { ref } from 'vue';

import { Input } from 'ant-design-vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const inputValue = ref(props.modelValue);
</script>

<template>
  <Input
    v-model:value="inputValue"
    disabled
    placeholder="保存后自动计算结果"
    type="text"
  />
</template>

<style></style>
