<script setup>
import { ref, watch } from 'vue';

import { Button, message, Upload } from 'ant-design-vue';

import { UploadImgApi } from '#/api/common';

const props = defineProps({
  modelValue: {
    type: String,
    default: null,
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const fileList = ref([]);
props.modelValue &&
  fileList.value.push({
    url: props.modelValue,
    name: props.modelValue,
    status: 'done',
  });

async function customRequest(file) {
  const fileParams = new FormData();
  fileParams.append('file', file.file);
  fileParams.append('file_rel_id', 'id_test');
  fileParams.append('file_rel_Object', 'object_test');
  fileParams.append('file_max_num', 1);

  try {
    const { data } = await UploadImgApi(fileParams);
    fileList.value = [{ url: data.url, name: data.url, status: 'done' }];
    message.success('上传成功');
    emits('update:modelValue', data.url);
  } catch {
    fileList.value = [];
  }
}

watch(fileList, (newValue) => {
  if (newValue.length === 0) {
    emits('update:modelValue', null);
  } else {
    emits('update:modelValue', newValue[0].url);
  }
});
</script>

<template>
  <div>
    <Upload
      v-model:file-list="fileList"
      :custom-request="customRequest"
      :disabled="props.parameters.field_readonly"
      action
      name="file"
    >
      <Button v-if="fileList.length === 0">+上传文件</Button>
    </Upload>
  </div>
</template>

<style></style>
