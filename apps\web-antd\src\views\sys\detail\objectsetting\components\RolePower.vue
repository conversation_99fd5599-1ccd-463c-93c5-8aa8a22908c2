<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';

import {
  Button,
  Card,
  Checkbox,
  Input,
  message,
  Modal,
  Pagination,
  Popconfirm,
  Select,
  SelectOption,
  Table,
} from 'ant-design-vue';

import {
  changeRolepowerApi,
  findFieldApi,
  getObjetBtnApi,
  getObjSelectApi,
  getRoleApi,
} from '#/api';

const route = useRoute();

const btnLoading = ref(false);
const dataSource = ref([]);
const btnTotal = ref(0);
// 当前选择的角色id
const roleId = ref('');
const dataColumns = ref([
  {
    title: '角色',
    key: 'Permissions_role_id',
    dataIndex: 'Permissions_role_id',
    width: 100,
    align: 'center',
    fixed: 'left',
  },
  {
    title: '类型',
    key: 'Permissions_type',
    dataIndex: 'Permissions_type',
    width: 65,
    align: 'center',
    fixed: 'left',
  },

  {
    title: '菜单显示',
    key: 'Permissions_menu_show',
    dataIndex: 'Permissions_menu_show',
    width: 100,
    align: 'center',
  },
  {
    title: '查询',
    key: 'Permissions_select',
    dataIndex: 'Permissions_select',
    width: 80,
    align: 'center',
  },
  {
    title: '添加',
    key: 'Permissions_insert',
    dataIndex: 'Permissions_insert',
    width: 80,
    align: 'center',
  },
  {
    title: '修改',
    key: 'Permissions_update',
    dataIndex: 'Permissions_update',
    width: 80,
    align: 'center',
  },
  {
    title: '删除',
    key: 'Permissions_delete',
    dataIndex: 'Permissions_delete',
    width: 80,
    align: 'center',
  },
  {
    title: '按钮',
    key: 'Permissions_button',
    dataIndex: 'Permissions_button',
    width: 140,
    align: 'center',
  },
  {
    title: '单据管理',
    key: 'Permissions_templet',
    dataIndex: 'Permissions_templet',
    width: 170,
    align: 'center',
  },
  {
    title: '附件上传',
    key: 'Permissions_attachments',
    dataIndex: 'Permissions_attachments',
    width: 170,
    align: 'center',
  },
  {
    title: '数据范围',
    key: 'Permissions_datas_filter',
    dataIndex: 'Permissions_datas_filter',
    width: 170,
    align: 'center',
  },
  {
    title: '编辑全部',
    key: 'Permissions_select_all',
    dataIndex: 'Permissions_select_all',
    width: 100,
    align: 'center',
  },
]);

// 筛选逻辑数据
const condition = ref({
  logic: 'and',
  diy_logic: '',
  condition_list: [
    {
      number: null,
      field_id: null,
      operator: null,
      content: null,
    },
  ],
});

const dataParams = reactive({
  page: 1,
  page_size: 10,
  object_id: route.params.id,
  order_by: 'Btn_order',
  sort_by: 'ASC',
  keywords: null,
});

// 单据管理选项
const Permissions_templet_options = ref([]);
const get_receipts = async (key) => {
  const { data } = await getObjSelectApi('Permissions', key);
  if (Array.isArray(data)) {
    Object.assign(Permissions_templet_options.value, data);
  }
};
// 附件上传选项
const Permissions_attachments_options = ref([]);

const get_attachments = async (key) => {
  const { data } = await getObjSelectApi('Permissions', key);
  if (Array.isArray(data)) {
    Object.assign(Permissions_attachments_options.value, data);
  }
};
// 数据范围选项
const Permission_data_filter_options = ref([]);

const get_filter = async (key) => {
  const { data } = await getObjSelectApi('Permissions', key);
  if (Array.isArray(data)) {
    Object.assign(Permission_data_filter_options.value, data);
  }
};
// 按钮选项
const objbutOption = ref([]);

const getWorkList = async () => {
  const { data } = await getObjetBtnApi(route.params.id);
  objbutOption.value = data;
};

// 按钮选择
const objbutChange = async (value, id) => {
  await changeRolepowerApi({
    id,
    Permissions_button: JSON.stringify(value),
  });
  message.success('修改成功');
  // getRoleList();
};

// 获取列表
async function getRoleList() {
  btnLoading.value = true;
  const { data } = await getRoleApi({
    page: dataParams.page,
    page_size: dataParams.page_size,
    object_id: route.params.id,
  });
  btnTotal.value = data.total;
  dataSource.value = data.data.map((item) => {
    item.Permissions_filter = JSON.parse(item.Permissions_filter);
    item.Permissions_button = item.Permissions_button
      ? JSON.parse(item.Permissions_button)
      : [];
    return item;
  });
  getWorkList();
  btnLoading.value = false;
}

// 显示筛选逻辑弹窗
const roleVisible = ref(false);

// 选择数据范围
const choseRange = (e) => {
  if (e && e === 'Permissions_datas_filter') {
    roleVisible.value = true;
  }
};
// 关联逻辑表格
const columns = [
  {
    title: '字段',
    dataIndex: 'field',
    width: '25%',
  },
  {
    title: '运算符',
    dataIndex: 'operator',
    width: '22%',
  },
  {
    title: '值',
    dataIndex: 'value',
    width: '38%',
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: '15%',
  },
];

const filterOption = (input, option) => {
  return option.label.toLowerCase().includes(input.toLowerCase());
};

let field_list = reactive([]);
// 运算符下拉列表
const Operation_options = reactive([
  {
    id: 'NOT_CONTAINS',
    name: '不包含',
  },
  {
    id: 'CONTAINS',
    name: '包含',
  },
  {
    id: 'EQUALS',
    name: '等于',
  },
  {
    id: 'MORE_THAN',
    name: '大于',
  },
  {
    id: 'LESS_THAN',
    name: '小于',
  },
  {
    id: 'BETWEEN',
    name: '介于',
  },
  {
    id: 'MORE_THAN_AND_EQUAL',
    name: '大于等于',
  },
  {
    id: 'LESS_THAN_AND_EQUAL',
    name: '小于等于',
  },
]);

// 添加逻辑
const condition_add = () => {
  condition.value.condition_list.push({
    number: null,
    field_id: null,
    operator: null,
    content: null,
  });
};
// 删除逻辑
const onDelete = (i) => {
  condition.value.condition_list.splice(i, 1);
};
const cancelAdd = () => {
  roleVisible.value = false;
};
const sureAdd = async () => {
  condition.value.condition_list = condition.value.condition_list
    .filter((items) => items.field_id)
    .map((item, index) => {
      return {
        ...item,
        number: index + 1,
      };
    });
  await changeRolepowerApi({
    id: roleId.value,
    Permissions_filter: JSON.stringify(condition.value),
  });
  message.success('保存成功');
  roleVisible.value = false;
};
const permissions_checked = async (data, Permissions_filter, key) => {
  roleId.value = data.id;
  condition.value = Permissions_filter || {
    logic: 'and',
    diy_logic: '',
    condition_list: [
      {
        number: null,
        field_id: null,
        operator: null,
        content: null,
      },
    ],
  };
  await changeRolepowerApi(data);
  message.success('修改成功');
  if (data.Permissions_datas_filter === 'Permissions_datas_filter') {
    roleVisible.value = true;
  }
  if (key === 'Permissions_select_all') {
    getRoleList();
  }
};

// 改变页数
async function pageChange(page, pageSize) {
  dataParams.page = page;
  dataParams.page_size = pageSize;
  getRoleList(); // 重新调用
}

// 获取对象字段
const findFieldList = async () => {
  const { data } = await findFieldApi(route.params.id);
  field_list = data.map((item) => {
    return {
      label: item.name,
      value: item.id,
    };
  });
};
const filterByLabel = (inputValue, option) => {
  if (!inputValue || !option.button_name) return true;
  return option.button_name.includes(inputValue);
};

onMounted(() => {
  getRoleList();
  findFieldList();
});
</script>

<template>
  <div>
    <Card title="角色权限">
      <Table
        :columns="dataColumns"
        :data-source="dataSource"
        :loading="btnLoading"
        :pagination="false"
        :scroll="{ y: 500 }"
        bordered
      >
        <template #bodyCell="{ column, record }">
          <!-- 角色+类型 -->
          <template
            v-if="
              column.key === 'Permissions_role_id' ||
              column.key === 'Permissions_type'
            "
          >
            {{ record[column.key].name }}
          </template>
          <!-- 复选框 -->
          <template
            v-if="
              column.key === 'Permissions_delete' ||
              column.key === 'Permissions_insert' ||
              column.key === 'Permissions_select' ||
              column.key === 'Permissions_update' ||
              column.key === 'Permissions_menu_show' ||
              column.key === 'Permissions_select_all'
            "
          >
            <Checkbox
              v-model:checked="record[column.key]"
              @change="
                permissions_checked(
                  {
                    id: record.id,
                    [column.key]: record[column.key],
                  },
                  record.Permissions_filter,
                  column.key,
                )
              "
            />
          </template>
          <!-- 按钮 -->
          <template v-if="column.key === 'Permissions_button'">
            <Select
              v-model:value="record[column.key]"
              :field-names="{ label: 'button_name', value: 'id' }"
              :filter-option="filterByLabel"
              :options="objbutOption"
              mode="multiple"
              placeholder="请选择"
              style="width: 100%"
              @change="objbutChange($event, record.id)"
            />
          </template>
          <!-- 单据管理 -->
          <template v-if="column.key === 'Permissions_templet'">
            <Select
              v-model:value="record[column.key].name"
              style="width: 100%"
              @change="
                permissions_checked(
                  {
                    id: record.id,
                    [column.key]: record[column.key].name,
                  },
                  record.Permissions_filter,
                  column.key,
                )
              "
              @click="get_receipts(column.key)"
            >
              <SelectOption
                v-for="item in Permissions_templet_options"
                :key="item"
                :value="item.id"
              >
                {{ item.name }}
              </SelectOption>
            </Select>
          </template>
          <!-- 附件上传 -->
          <template v-if="column.key === 'Permissions_attachments'">
            <Select
              v-model:value="record[column.key].name"
              style="width: 100%"
              @change="
                permissions_checked(
                  {
                    id: record.id,
                    [column.key]: record[column.key].name,
                  },
                  record.Permissions_filter,
                  column.key,
                )
              "
              @click="get_attachments(column.key)"
            >
              <SelectOption
                v-for="item in Permissions_attachments_options"
                :key="item"
                :value="item.id"
              >
                {{ item.name }}
              </SelectOption>
            </Select>
          </template>
          <!-- 数据范围 -->
          <template v-if="column.key === 'Permissions_datas_filter'">
            <!-- 数据范围 -->
            <Select
              v-model:value="record[column.key].name"
              style="width: 100%"
              @change="
                permissions_checked(
                  {
                    id: record.id,
                    [column.key]: record[column.key].name,
                  },
                  record.Permissions_filter,
                  column.key,
                )
              "
              @click="get_filter(column.key)"
              @select="choseRange"
            >
              <SelectOption
                v-for="item in Permission_data_filter_options"
                :key="item"
                :value="item.id"
              >
                {{ item.name }}
              </SelectOption>
            </Select>
          </template>
        </template>
        <template #footer>
          <div class="text-right">
            <Pagination
              v-model:current="dataParams.page"
              v-model:page-size="dataParams.page_size"
              :show-size-changer="true"
              :show-total="(btnTotal) => `共 ${btnTotal} 条`"
              :total="btnTotal"
              @change="pageChange"
            />
          </div>
        </template>
      </Table>
    </Card>
    <Modal v-model:open="roleVisible" title="筛选条件" width="1000px">
      <div style="padding: 20px">
        <Button class="!mb-6 rounded-lg" type="primary" @click="condition_add">
          添加条件逻辑
        </Button>
        <Table
          :columns="columns"
          :data-source="condition.condition_list"
          :pagination="false"
          :scroll="{ y: 400 }"
          bordered
        >
          <template #bodyCell="{ column, record, index }">
            <!-- 字段选择下拉框 -->
            <template v-if="column.dataIndex === 'field'">
              <Select
                v-model:value="record.field_id"
                :filter-option="filterOption"
                :options="field_list"
                :show-search="true"
                class="border-1 w-[80%] border-solid border-inherit"
                placeholder="请选择字段"
              />
            </template>
            <!-- 运算符选择下拉框 -->
            <template v-if="column.dataIndex === 'operator'">
              <Select
                v-model:value="record.operator"
                class="w-[60%]"
                placeholder="请选择运算符"
              >
                <SelectOption
                  v-for="items in Operation_options"
                  :key="items"
                  :value="items.id"
                >
                  {{ items.name }}
                </SelectOption>
              </Select>
            </template>
            <!-- 值输入框 -->
            <template v-if="column.dataIndex === 'value'">
              <div class="flex gap-4">
                <Input
                  v-model:value="record.content"
                  placeholder="请输入..."
                  style="width: 80%; border: 1px solid #ccc"
                />
              </div>
            </template>

            <!-- 删除按钮 -->
            <template v-if="column.dataIndex === 'operation'">
              <Popconfirm
                v-if="dataSource.length > 0"
                title="确定删除吗?"
                @confirm="onDelete(index)"
              >
                <Button type="primary">删除</Button>
              </Popconfirm>
            </template>
          </template>
        </Table>
        <div class="mt-8">
          <span>筛选逻辑：</span>
          <Select v-model:value="condition.logic" style="width: 10%">
            <SelectOption value="and">AND</SelectOption>
            <SelectOption value="diy">自定义</SelectOption>
          </Select>
          <span v-if="condition.logic === 'diy'" class="ml-8">
            自定义逻辑：
          </span>
          <Input
            v-if="condition.logic === 'diy'"
            v-model:value="condition.diy_logic"
            class="border-1 w-[50%] border-solid border-inherit"
            placeholder="请输入..."
          />
        </div>
      </div>
      <template #footer>
        <Button class="mr-2.5 rounded-lg" @click="cancelAdd">取消</Button>
        <Button class="rounded-lg" type="primary" @click="sureAdd">确定</Button>
      </template>
    </Modal>
  </div>
</template>

<style></style>
