<!-- eslint-disable no-use-before-define -->
<script setup>
import { computed, h, ref, toRaw } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import { useVbenModal } from '@vben/common-ui';
import { useRefresh } from '@vben/hooks';

import { SendOutlined } from '@ant-design/icons-vue';
import {
  Alert,
  Button,
  Collapse,
  CollapsePanel,
  Form,
  FormItem,
  message,
  Select,
  Skeleton,
  Step,
  Steps,
  Table,
  Tooltip,
} from 'ant-design-vue';
import { Sender, theme } from 'ant-design-x-vue';

import {
  autoFillTableApi,
  createTableDataApi,
  getFilterProSelectApi,
  getTableAddOrEditListApi,
  getTableColumnApi,
  getTableDataApi,
} from '#/api';
import {
  Audio,
  Checkbox,
  ColorPicker,
  Date,
  DateTime,
  Formula,
  InputInteger,
  InputNumber,
  InputPassword,
  InputPercent,
  InputText,
  InputTextArea,
  LookupItem,
  SelectItem,
  Time,
  UploadFile,
  UploadImg,
  UploadVideo,
} from '#/components/form-ui';

const props = defineProps({
  objectName: {
    type: String,
    default: '',
  },
  objectId: {
    type: String,
    default: '',
  },
  // 详情页添加使用的参数
  addDefult: {
    type: Object,
    default: () => null,
    required: false,
  },
  // 详情页添加使用的参数
  columnList: {
    type: Array,
    default: () => [],
    required: false,
  },
  // 是否从头部全局搜索进入
  searchIf: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 从头部全局搜索传入对象名
  searchData: {
    type: Object,
    default: () => null,
    required: false,
  },
});
const emits = defineEmits(['clearFilter', 'afterAdd']);
const { token } = theme.useToken();
const [Modal, modalApi] = useVbenModal({
  class: 'w-max-[1000px] w-[60vw]',
  contentClass: 'p-8',
  closeOnClickModal: false,
  footer: false,
  async onOpenChange(isOpen) {
    if (isOpen) {
      current.value = 0;
      selectedRowKeys.value = [];
      chooseList.value = [];
      inputValue.value = '';
      await getFelidsMessage();
      await getModelList();
      // 如果是全局搜索页面进入，直接跳转到步骤二
      if (props.searchIf === true) {
        await searchSubmit(props.searchData);
      }
    }
  },
});
const route = useRoute();
const router = useRouter();
const felidList = ref([]);
const current = ref(0);
const formRef = ref(null);
const components = ref([]);
const skeletonLoading = ref(true);
const modelList = ref([]);
const chooseModel = ref('');
const chooseList = ref([]);
const activeKey = ref([]);
const fieldsColumns = ref([
  {
    title: '字段',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '字段类型',
    dataIndex: 'typeName',
    key: 'typeName',
  },
]);
const selectedRowKeys = ref([]);
const rules = ref({});
const inputValue = ref('');
// 表格配置项
const rowSelection = {
  selectedRowKeys,
  onChange: (selectedRowKeys, selectedRows) => {
    onSelectChange(selectedRowKeys, selectedRows);
  },
};

// 获取表格信息
async function getFelidsMessage() {
  const { data } = await getTableColumnApi(props.objectName);
  felidList.value = data.slice(0, -6) || []; // slice操作去除后面几项，例如：修改人，操作时间等等
  const fieldTypeMapping = {
    Text: '文本类型',
    Number: '数字类型',
    Date: '日期类型',
    Date_Time: '日期和时间类型',
    Checkbox: '单选框类型',
    dropdownlist: '下拉选择框类型',
    Text_Area: '多行文本类型',
    Text_Area_Rich: '富文本类型',
    integer: '整数类型',
    group: '分组类型',
    Lookup_Relationship: '查找相关类型',
    Time: '时间类型',
  };

  felidList.value.forEach((item) => {
    item.typeName = fieldTypeMapping[item.type] || item.type;
  });
  selectedRowKeys.value = felidList.value.map((item) => item.key);
  chooseList.value = felidList.value.map((item) => {
    return {
      id: item.id,
      name: item.name,
      type: item.type,
      key: item.key,
      field_length: item.field_length,
      field_visible: item.field_visible,
      field_required: item.field_required,
      Field_Help_Text: item.Field_Help_Text,
    };
  });
}

// 获取大模型类型
async function getModelList() {
  const { data } = await getTableDataApi('ai_engine');
  modelList.value = data.data.map((item) => {
    return item.engine_name === 'deepseek-r1'
      ? {
          label: `${item.engine_name}(深度思考)`,
          value: item.engine_key_name.id,
        }
      : {
          label: item.engine_name,
          value: item.engine_key_name.id,
        };
  });
  chooseModel.value = modelList.value[0].value;
}

async function getAddItemMessage(AIVal) {
  const { data } = await getTableAddOrEditListApi(props.objectName);
  components.value = data.filter(
    (item) =>
      item.field_visible === true &&
      item.key !== 'system_information' &&
      item.key !== 'OwnerId',
  );

  const addDefultData = modalApi.getData();

  components.value.forEach((item) => {
    item.value = null;
  });
  if (addDefultData?.addDefultMessage) {
    components.value.forEach((item) => {
      if (item.key === addDefultData.addDefultMessage.field) {
        item.value = {
          name: null,
          id: addDefultData.addDefultId,
          field: addDefultData.addDefultMessage.main_table_display_field,
        };
      }
    });
  }
  // 处理自定义校验规则
  components.value
    .filter((item) => item.type !== 'group' && item.field_required)
    .forEach((item) => {
      rules.value[item.key] = [
        {
          required: true,
          message: `${item.name}不能为空`,
          trigger: ['blur'],
        },
      ];
    });
  const promises = components.value.map(async (item, index) => {
    if (item.type === 'dropdownlist') {
      const { data } = await getFilterProSelectApi(props.objectName, item.key);
      components.value[index].selectOpts = data.map((item) => {
        return {
          label: `${item.name}`,
          value: item.id,
        };
      });
    }
  });

  // 等待所有异步操作完成
  await Promise.all(promises);

  // 之后处理 AIVal
  AIVal.forEach((val) => {
    const item = components.value.find((item) => item.key === val.key);
    if (item && item.type !== 'dropdownlist') {
      item.value = val.value;
    } else if (item && item.type === 'dropdownlist') {
      item.value = {
        label: `${val.value.name}(AI建议值)`,
        value: val.value.id,
      };
    }

    if (item.type === 'dropdownlist') {
      item.selectOpts.push({
        label: `${val.value.name}(AI建议值)`,
        value: val.value.id,
      });
    }
  });
  skeletonLoading.value = false;
}

function getComponent(type) {
  if (['Geolocation', 'Text'].includes(type)) return InputText;
  if (type === 'Text_Area') return InputTextArea;
  if (type === 'Number') return InputNumber;
  if (type === 'Password') return InputPassword;
  if (type === 'dropdownlist') return SelectItem;
  if (type === 'Lookup_Relationship') return LookupItem;
  if (type === 'Percent') return InputPercent;
  if (type === 'Date') return Date;
  if (type === 'Date_Time') return DateTime;
  if (type === 'Time') return Time;
  if (type === 'Checkbox') return Checkbox;
  if (type === 'upload_pic') return UploadImg;
  if (type === 'integer') return InputInteger;
  if (type === 'Formula') return Formula;
  if (type === 'upload_video') return UploadVideo;
  if (type === 'upload_file') return UploadFile;
  if (type === 'upload_audio') return Audio;
  if (type === 'color_picker') return ColorPicker;
}
const modelForm = computed(() => {
  // 处理modalForm值
  const fromValues = {};
  components.value
    .filter((item) => item.type !== 'group' && item.value)
    .forEach((item) => {
      fromValues[item.key] = item.value;
    });

  return fromValues;
});

// 提交
async function submitVal() {
  current.value = 1;
  components.value = [];
  skeletonLoading.value = true;
  chooseList.value = chooseList.value.map((item) => {
    return {
      id: item.id,
      name: item.name,
      type: item.type,
      key: item.key,
      field_length: item.field_length,
      field_visible: item.field_visible,
      field_required: item.field_required,
      Field_Help_Text: item.Field_Help_Text,
    };
  });

  const AIParams = {
    ai_mode: chooseModel.value,
    object_name: route.meta.title,
    object_uid: props.objectId,
    object_id: props.objectName,
    input_content: inputValue.value,
    fields_content: toRaw(chooseList.value),
  };

  const { data } = await autoFillTableApi(JSON.stringify(AIParams));

  getAddItemMessage(data);
}

// 全局搜索进入后直接提交
async function searchSubmit(search) {
  current.value = 1;
  components.value = [];
  inputValue.value = search.keyWord;
  skeletonLoading.value = true;
  chooseList.value = chooseList.value.map((item) => {
    return {
      id: item.id,
      name: item.name,
      type: item.type,
      key: item.key,
      field_length: item.field_length,
      field_visible: item.field_visible,
      field_required: item.field_required,
      Field_Help_Text: item.Field_Help_Text,
    };
  });

  const AIParams = {
    ai_mode: chooseModel.value,
    object_name: search.name,
    object_uid: props.objectId,
    object_id: props.objectName,
    input_content: search.keyWord,
    fields_content: toRaw(chooseList.value),
  };

  const { data } = await autoFillTableApi(JSON.stringify(AIParams));
  getAddItemMessage(data);
}
const { refresh } = useRefresh();
// 完成
async function finish() {
  const changeField = components.value
    .filter((item) => item.type !== 'group' && item.value)
    .map((item) => {
      return {
        key: item.key,
        type: item.type,
        value: item.value,
      };
    });

  const dataParams = {};

  changeField.forEach((item) => {
    switch (item.type) {
      case 'Date': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'YYYY-MM-DD').format('YYYY-MM-DD')
          : null;

        break;
      }
      case 'Date_Time': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'YYYY-MM-DD HH:mm:ss').format(
              'YYYY-MM-DD HH:mm:ss',
            )
          : null;

        break;
      }
      case 'Time': {
        dataParams[item.key] = item.value
          ? dayjs(item.value, 'HH:mm:ss').format('HH:mm:ss')
          : null;

        break;
      }
      default: {
        dataParams[item.key] = item.value ?? null;
      }
    }
  });
  formRef.value.validate().then(() => {
    createTableDataApi(props.objectName, dataParams)
      .then(() => {
        message.success('添加成功');
        if (props.addDefult) {
          emits('afterAdd');
        } else {
          emits('clearFilter');
        }
        modalApi.close();
        // 如果是全局搜索填表跳转到目标对象页面
        if (props.searchIf === true) {
          router
            .push({
              name: `/dashboard/${props.searchData?.obj}`,
              params: {
                id: props.objectId,
              },
            })
            .then(() => {
              // 刷新目标路由
              refresh();
            });
        }
      })
      .catch((error) => {
        if (error.code === 422) {
          message.error('AI预设值与系统值不匹配，请手动修改');
        }
      });
  });
}

// 表格选择
function onSelectChange(newSelectedRowKeys, e) {
  selectedRowKeys.value = newSelectedRowKeys;
  chooseList.value = e;
}

const isAutoSize = ref(false);
</script>

<template>
  <Modal>
    <template #title>
      <div class="flex gap-2">
        <span>AI智能填表</span>
      </div>
    </template>
    <Steps :current="current" class="mb-[10px]" type="navigation">
      <Step title="输入内容" @click="() => (currentStep = 0)" />
      <Step disabled title="生成表格" />
    </Steps>
    <div v-show="current === 0">
      <Collapse v-model:active-key="activeKey" ghost>
        <CollapsePanel key="1" header="选择字段">
          <Table
            :columns="fieldsColumns"
            :data-source="felidList"
            :pagination="{ pageSize: 7 }"
            :row-selection="rowSelection"
            size="small"
          />
        </CollapsePanel>
      </Collapse>

      <Sender
        v-model:value="inputValue"
        :auto-size="isAutoSize"
        class="h-[200px]"
        placeholder="例：帮我生成一个天天超市销货额为1万元的销货单"
        @cancel="() => {}"
        @submit="
          (msg) => {
            submitVal();
            value = '';
          }
        "
      >
        <template #footer>
          <Select
            v-model:value="chooseModel"
            :options="modelList"
            class="absolute bottom-[10px] w-[200px]"
            @change="
              (val) => {
                console.log(val);
              }
            "
          />
        </template>
        <template
          #actions="{
            info: {
              components: { SendButton },
            },
          }"
        >
          <Tooltip title="确认生成">
            <component
              :is="SendButton"
              :icon="h(SendOutlined)"
              :style="{ color: token.colorPrimary, fontSize: '15px' }"
              shape="default"
              type="text"
            />
          </Tooltip>
        </template>
      </Sender>
    </div>
    <div v-show="current === 1">
      <Alert
        class="mb-[10px]"
        message="该表格仅为AI建议值，需要根据实际情况进行修改"
        show-icon
        type="warning"
      />
      <Skeleton :loading="skeletonLoading" active />
      <div class="mt-[10px] max-h-[530px] overflow-scroll pr-[20px]">
        <Form
          ref="formRef"
          :label-col="{ span: 9 }"
          :model="modelForm"
          :rules="rules"
          :wrapper-col="{ span: 15 }"
          class="mt-[10px]"
          name="basic"
        >
          <div class="grid grid-cols-1 xl:grid-cols-2">
            <template v-for="item in components" :key="item.key">
              <div
                v-if="item.type === 'group'"
                class="card-box bg-theme-color col-span-1 mb-3 mt-3 p-1 font-bold text-[#fff] opacity-80 xl:col-span-2"
              >
                <span class="opacity-100">{{ item.name }}</span>
              </div>
              <FormItem v-else :label="item.name" :name="item.key">
                <component
                  :is="getComponent(item.type)"
                  v-if="components"
                  v-model="item.value"
                  :object-id="props.objectId"
                  :object-name="props.objectName"
                  :parameters="item"
                  :select-alone="true"
                  :select-opts="item.selectOpts"
                  class="w-[100%]"
                />
              </FormItem>
            </template>
          </div>
        </Form>
      </div>
      <div class="flex justify-between gap-2">
        <Button type="primary" @click="() => (current = 0)"> 上一步 </Button>
        <Button type="primary" @click="finish"> 完成 </Button>
      </div>
    </div>
  </Modal>
</template>

<style lang="scss" scoped></style>
