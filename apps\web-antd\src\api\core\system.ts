import { requestClient } from '#/api/request';

/**
 *  @returns 获取系统配置信息
 */
export async function getSystemConfigApi() {
  return requestClient.post('/options/get_optionss', {
    page: 1,
    page_size: 1000,
  });
}

/**
 * @returns 保存系统基本信息配置
 */
export async function saveSystemConfigApi(data: any) {
  return requestClient.post('/options_hand/save', data);
}

/**
 * @returns 获取文件存储配置信息
 */

export async function getFileConfigApi() {
  return requestClient.get('/options_hand/type//file_storage');
}

/**
 * @returns 获取地图配置信息
 */

export async function getMapConfigApi() {
  return requestClient.get('/options_hand/type//map');
}
