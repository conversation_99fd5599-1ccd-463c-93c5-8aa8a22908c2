<script setup>
import { ref } from 'vue';

import { Input } from 'ant-design-vue';

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  parameters: {
    type: Object,
    default: () => ({
      field_readonly: false,
    }),
  },
});

const emits = defineEmits(['update:modelValue']);

const inputValue = ref(props.value);

const changeValue = () => {
  emits('update:modelValue', inputValue.value);
};

const errorMessage = ref(null);
const blur = () => {
  if (!inputValue.value) {
    errorMessage.value = null;
    return;
  }
  const isValidEmail = /^[\w-]+@[\w-]+(?:\.[\w-]+)+$/;
  if (isValidEmail.test(inputValue.value)) {
    errorMessage.value = null;
    emits('update:modelValue', inputValue.value);
  } else {
    errorMessage.value = '请输入正确的邮箱格式';
  }
};
</script>

<template>
  <div class="flex flex-col gap-1">
    <Input
      v-model:value="inputValue"
      :disabled="props.parameters.field_readonly"
      :placeholder="props.parameters.Field_Help_Text"
      type="text"
      @blur="blur"
      @change="changeValue"
    />
    <div v-if="errorMessage" class="text-[14px] text-[red]">
      {{ errorMessage }}
    </div>
  </div>
</template>

<style></style>
